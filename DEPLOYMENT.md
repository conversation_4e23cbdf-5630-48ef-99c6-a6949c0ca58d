# 高性能通行记录处理系统部署指南

## 系统概述

本系统实现了基于Redis缓存的高性能通行记录处理功能，支持：
- 高并发通行记录接收和处理（支持10000+ TPS）
- 实时学生在寝状态更新
- 高性能统计查询（查询响应时间<10ms）
- 异步批量数据持久化
- 数据库压力监控和自动限流

## 部署前准备

### 1. 环境要求
- Java 8+
- PostgreSQL 9.5+
- Redis 5.0+
- Maven 3.6+

### 2. 数据库准备

#### 2.1 创建队列表（主数据源）
```sql
-- 执行 database/create_queue_table.sql
psql -h localhost -U postgres -d transfer < database/create_queue_table.sql
```

#### 2.2 确保FastGate数据库表结构
确保FastGate数据库中存在以下表：
- `tbl_person` - 人员表
- `tbl_access_control_record` - 通行记录表（支持分表）

### 3. Redis配置
```bash
# 启动Redis服务
redis-server

# 建议配置（redis.conf）:
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 编译和部署

### 1. 编译项目
```bash
mvn clean package
```

### 2. 环境配置设置

#### 2.1 开发环境配置
使用开发环境配置启动：
```bash
java -Dspring.profiles.active=dev -jar demo-0.0.1-SNAPSHOT.jar
```

开发环境配置特点：
- 使用本地数据库和Redis
- 启用调试日志
- 启用Mock数据
- 较小的连接池和缓存设置
- 详细的监控信息展示

#### 2.2 生产环境配置
使用生产环境配置启动：
```bash
java -Dspring.profiles.active=prod -jar demo-0.0.1-SNAPSHOT.jar
```

生产环境配置特点：
- 支持环境变量配置敏感信息
- 优化的连接池和缓存设置
- 简化的日志输出
- 禁用Mock数据
- 增强的监控和安全设置

#### 2.3 环境变量设置（生产环境）
```bash
# 数据库配置
export DB_MASTER_USERNAME=transfer_user
export DB_MASTER_PASSWORD=your_master_password
export DB_FASTGATE_USERNAME=fastgate_user
export DB_FASTGATE_PASSWORD=your_fastgate_password

# Redis配置
export REDIS_HOST=your_redis_host
export REDIS_PORT=6379
export REDIS_PASSWORD=your_redis_password

# FastGate配置
export FASTGATE_HOST=your_fastgate_host
export FASTGATE_PORT=8443
export FASTGATE_PASSWORD=your_fastgate_password

# 管理端口
export MANAGEMENT_PORT=8091
```

#### 2.4 外部配置文件（可选）
仍可使用 `application-external.yml` 覆盖特定配置：
```yaml
# 仅覆盖需要调整的配置项
async:
  persistence:
    batch:
      size: 800  # 根据实际环境调整
    max:
      tps: 1500

db:
  monitor:
    max:
      tps: 2500
```

### 3. 启动应用

#### 3.1 开发环境启动
```bash
# 使用开发环境配置
java -Dspring.profiles.active=dev -jar demo-0.0.1-SNAPSHOT.jar

# 或者指定JVM参数
java -Xms1g -Xmx2g -Dspring.profiles.active=dev -jar demo-0.0.1-SNAPSHOT.jar
```

#### 3.2 生产环境启动
```bash
# 基本启动
java -Dspring.profiles.active=prod -jar demo-0.0.1-SNAPSHOT.jar

# 推荐的生产环境启动参数
java -Xms4g -Xmx8g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 \
     -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps \
     -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/fastgate-demo/ \
     -Dspring.profiles.active=prod \
     -jar demo-0.0.1-SNAPSHOT.jar
```

## 功能验证

### 1. 健康检查
```bash
# 系统整体健康状态
curl http://localhost:8099/api/v2/dormitory/health

# 数据库监控状态  
curl http://localhost:8099/api/monitor/database/statistics

# 缓存统计信息
curl http://localhost:8099/api/v2/dormitory/cache/statistics
```

### 2. 接口测试
```bash
# 获取寝室统计信息
curl http://localhost:8099/api/v2/dormitory/statistics

# 查询学生状态列表
curl "http://localhost:8099/api/v2/dormitory/students/all?page=0&size=20"

# 查询指定状态学生
curl "http://localhost:8099/api/v2/dormitory/students/by-status?status=1&page=0&size=20"
```

### 3. 性能测试
```bash
# 模拟通行记录测试
curl -X POST "http://localhost:8099/api/test/simulate-access-records?count=1000&concurrent=10"

# 压力测试
curl -X POST "http://localhost:8099/api/test/stress-test?duration=60&tps=500"
```

## 核心API接口

### 高性能查询接口（推荐使用）

| 接口 | 方法 | 说明 | 性能 |
|------|------|------|------|
| `/api/v2/dormitory/statistics` | GET | 获取寝室统计信息 | <10ms |
| `/api/v2/dormitory/students/all` | GET | 分页查询所有学生状态 | <50ms |
| `/api/v2/dormitory/students/by-status` | GET | 按状态查询学生列表 | <30ms |
| `/api/v2/dormitory/student/{code}` | GET | 查询单个学生状态 | <5ms |

### 原有接口（兼容保留）

| 接口 | 方法 | 说明 | 性能 |
|------|------|------|------|
| `/dormitory/statistics` | GET | 原有统计接口 | 2-5秒 |
| `/dormitory/students` | GET | 原有学生查询接口 | 1-3秒 |

### 系统管理接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/v2/dormitory/cache/refresh` | POST | 刷新统计缓存 |
| `/api/v2/dormitory/cache/clear` | POST | 清除所有缓存 |
| `/api/v2/dormitory/persistence/toggle` | POST | 开关异步持久化 |
| `/api/monitor/database/statistics` | GET | 数据库监控统计 |

## 性能调优

### 1. Redis优化
```bash
# 内存优化
CONFIG SET maxmemory-policy allkeys-lru
CONFIG SET maxmemory 2gb

# 持久化优化（根据需要）
CONFIG SET save "900 1 300 10 60 10000"
```

### 2. 数据库连接池优化
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          hikari:
            maximum-pool-size: 20
            minimum-idle: 5
            connection-timeout: 30000
        fastgate:
          hikari:
            maximum-pool-size: 30
            minimum-idle: 10
            connection-timeout: 30000
```

### 3. 异步处理优化
```yaml
async:
  persistence:
    batch:
      size: 1000  # 增大批量大小以提高吞吐量
    max:
      tps: 2000   # 根据数据库性能调整
```

### 4. JVM调优
```bash
# 启动参数建议
java -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 \
     -XX:+PrintGC -XX:+PrintGCDetails \
     -jar demo-0.0.1-SNAPSHOT.jar
```

## 监控和告警

### 1. 关键指标监控
- Redis连接数和内存使用率
- 数据库连接数和TPS
- 异步队列积压情况
- 应用响应时间

### 2. 告警阈值建议
- Redis内存使用率 > 80%
- 数据库连接数 > 80%
- 异步队列积压 > 10000条
- API响应时间 > 500ms

### 3. 日志监控
重要日志关键词：
- `ERROR` - 系统错误
- `数据库进入高压力状态` - 数据库压力告警
- `Redis连接失败` - 缓存服务异常
- `异步队列积压` - 持久化延迟

## 故障排查

### 1. 常见问题

#### 问题1：Redis连接失败
```bash
# 检查Redis服务状态
redis-cli ping

# 检查连接配置
curl http://localhost:8099/api/v2/dormitory/cache/statistics
```

#### 问题2：数据库压力过高
```bash
# 检查数据库监控
curl http://localhost:8099/api/monitor/database/statistics

# 动态调整TPS限制
curl -X POST "http://localhost:8099/api/v2/dormitory/persistence/max-tps?maxTPS=500"
```

#### 问题3：异步队列积压
```bash
# 检查队列状态
curl http://localhost:8099/api/v2/dormitory/queue/statistics

# 临时禁用持久化
curl -X POST "http://localhost:8099/api/v2/dormitory/persistence/toggle?enabled=false"
```

### 2. 性能基准
在标准配置下（4核CPU，8GB内存，SSD硬盘）：
- 通行记录处理能力：5000+ TPS
- 统计查询响应时间：< 10ms
- 分页查询响应时间：< 50ms
- 数据库写入TPS：1000-2000

## 升级和维护

### 1. 版本升级
1. 停止应用服务
2. 备份Redis数据（如需要）
3. 更新应用JAR包
4. 检查配置文件兼容性
5. 重启服务并验证功能

### 2. 数据备份
```bash
# Redis数据备份
redis-cli BGSAVE

# 数据库备份
pg_dump -h localhost -U postgres transfer > transfer_backup.sql
pg_dump -h localhost -U postgres fastgate > fastgate_backup.sql
```

### 3. 缓存预热
系统重启后建议执行缓存预热：
```bash
# 刷新统计缓存
curl -X POST http://localhost:8099/api/v2/dormitory/cache/refresh

# 模拟一些查询请求来预热缓存
for i in {1..10}; do
  curl "http://localhost:8099/api/v2/dormitory/students/all?page=$i&size=50" > /dev/null
done
```

## 技术支持

如遇到问题，请收集以下信息：
1. 应用日志（最近1小时）
2. 系统健康状态：`curl http://localhost:8099/api/monitor/health/overview`
3. 错误现象描述和复现步骤
4. 系统环境信息（Java版本、数据库版本、Redis版本等）