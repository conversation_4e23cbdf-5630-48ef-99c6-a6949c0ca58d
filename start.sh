#!/bin/bash

# ===========================================
# FastGate Demo 应用启动脚本
# 自动检测环境，根据配置文件决定启动模式
# ===========================================

# 应用配置
APP_NAME="fastgate-demo"
JAR_NAME="transfer-1.jar"
MAIN_CLASS="fastgatedemo.demo.StartMain"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_HOME="$SCRIPT_DIR"

# 日志目录配置
LOG_DIR="$APP_HOME/logs"
PID_FILE="$APP_HOME/${APP_NAME}.pid"

# 操作类型
ACTION=${1:-start}

# 自动检测环境函数
detect_environment() {
    # 配置文件路径 - 优先使用config目录下的配置文件
    local config_dir_config="$APP_HOME/config/application.yml"
    local external_config="$APP_HOME/application-external.yml"
    local target_config="$APP_HOME/target/config/application.yml"
    local main_config="$APP_HOME/src/main/resources/application.yml"
    
    local config_file=""
    
    # 按优先级查找配置文件：config目录 > external > target/config > src
    if [ -f "$config_dir_config" ]; then
        config_file="$config_dir_config"
    elif [ -f "$external_config" ]; then
        config_file="$external_config"
    elif [ -f "$target_config" ]; then
        config_file="$target_config"
    elif [ -f "$main_config" ]; then
        config_file="$main_config"
    else
        echo "警告: 找不到配置文件，使用默认开发环境"
        echo "dev"
        return
    fi
    
    # 从配置文件中提取环境配置
    local profile=$(grep -A 2 "profiles:" "$config_file" | grep "active:" | sed 's/.*active: *\([^# ]*\).*/\1/' | tr -d ' ')
    
    # 如果没有找到或为空，默认使用dev
    if [ -z "$profile" ]; then
        profile="dev"
    fi
    
    echo "$profile"
}

# 自动检测当前环境
PROFILE=$(detect_environment)

# 根据检测到的环境设置配置
if [ "$PROFILE" = "prod" ]; then
    # 生产环境配置
    JAVA_OPTS="-Xms4g -Xmx8g -XX:+UseG1GC -XX:MaxGCPauseMillis=100"
    JAVA_OPTS="$JAVA_OPTS -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"
    JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$LOG_DIR/"
    JAVA_OPTS="$JAVA_OPTS -Dspring.profiles.active=prod"
    LOG_FILE="$LOG_DIR/${APP_NAME}-prod.log"
    GC_LOG_FILE="$LOG_DIR/gc-prod.log"
    PORT=8099
else
    # 开发环境配置 (默认)
    JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC"
    JAVA_OPTS="$JAVA_OPTS -Dspring.profiles.active=dev"
    LOG_FILE="$LOG_DIR/${APP_NAME}-dev.log"
    GC_LOG_FILE="$LOG_DIR/gc-dev.log"
    PORT=8080
fi

# 添加GC日志配置
JAVA_OPTS="$JAVA_OPTS -Xloggc:$GC_LOG_FILE -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=100M"

# 添加Spring Boot配置目录参数
JAVA_OPTS="$JAVA_OPTS -Dspring.config.location=file:./config/"

# 通用JVM配置
JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Duser.timezone=Asia/Shanghai"
JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"

# 函数定义
create_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        echo "创建日志目录: $LOG_DIR"
    fi
}

check_java() {
    if [ -z "$JAVA_HOME" ]; then
        JAVA_CMD=java
    else
        JAVA_CMD="$JAVA_HOME/bin/java"
    fi
    
    if ! command -v $JAVA_CMD &> /dev/null; then
        echo "错误: 找不到Java命令. 请检查JAVA_HOME环境变量或确保java在PATH中"
        exit 1
    fi
    
    JAVA_VERSION=$($JAVA_CMD -version 2>&1 | awk -F '"' '/version/ {print $2}')
    echo "使用Java版本: $JAVA_VERSION"
}

check_jar() {
    if [ ! -f "$APP_HOME/$JAR_NAME" ]; then
        echo "错误: 找不到JAR文件 $APP_HOME/$JAR_NAME"
        echo "请先执行 'mvn clean package' 构建应用"
        exit 1
    fi
}

get_pid() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if [ -n "$PID" ] && kill -0 $PID 2>/dev/null; then
            echo $PID
        else
            rm -f "$PID_FILE"
            echo ""
        fi
    else
        echo ""
    fi
}

start_app() {
    local pid=$(get_pid)
    if [ -n "$pid" ]; then
        echo "应用已在运行中 (PID: $pid, 环境: $PROFILE, 端口: $PORT)"
        return 1
    fi
    
    echo "检测到环境: $PROFILE"
    echo "启动 $APP_NAME (环境: $PROFILE, 端口: $PORT)..."
    
    # 创建日志目录
    create_log_dir
    
    # 检查Java和JAR文件
    check_java
    check_jar
    
    # 启动应用
    cd "$APP_HOME"
    nohup $JAVA_CMD $JAVA_OPTS -jar "$JAR_NAME" > "$LOG_FILE" 2>&1 &
    
    local app_pid=$!
    echo $app_pid > "$PID_FILE"
    
    sleep 3
    
    # 验证启动是否成功
    if kill -0 $app_pid 2>/dev/null; then
        echo "✓ 应用启动成功!"
        echo "  - PID: $app_pid"
        echo "  - 环境: $PROFILE"
        echo "  - 端口: $PORT"
        echo "  - 访问地址: http://localhost:$PORT"
        echo "  - 日志文件: $LOG_FILE"
        echo "  - GC日志: $GC_LOG_FILE"
        echo "  - PID文件: $PID_FILE"
        
        # 显示最后几行日志
        echo ""
        echo "最近日志:"
        tail -n 10 "$LOG_FILE"
    else
        echo "✗ 应用启动失败!"
        rm -f "$PID_FILE"
        echo "请检查日志文件: $LOG_FILE"
        tail -n 20 "$LOG_FILE"
        exit 1
    fi
}

stop_app() {
    local pid=$(get_pid)
    if [ -z "$pid" ]; then
        echo "应用未运行"
        return 0
    fi
    
    echo "停止 $APP_NAME (PID: $pid)..."
    
    # 优雅停止
    kill $pid
    
    # 等待最多30秒
    local count=0
    while [ $count -lt 30 ]; do
        if ! kill -0 $pid 2>/dev/null; then
            rm -f "$PID_FILE"
            echo "✓ 应用已停止"
            return 0
        fi
        count=$((count + 1))
        sleep 1
    done
    
    # 强制停止
    echo "优雅停止超时，执行强制停止..."
    kill -9 $pid 2>/dev/null
    rm -f "$PID_FILE"
    echo "✓ 应用已强制停止"
}

status_app() {
    local pid=$(get_pid)
    if [ -n "$pid" ]; then
        echo "✓ 应用正在运行"
        echo "  - PID: $pid"
        echo "  - 环境: $PROFILE"
        echo "  - 端口: $PORT"
        echo "  - 内存使用: $(ps -o pid,rss,pmem -p $pid | tail -n 1 | awk '{print $2" KB ("$3"%)"}')"
        echo "  - 运行时间: $(ps -o pid,etime -p $pid | tail -n 1 | awk '{print $2}')"
        echo "  - 日志文件: $LOG_FILE"
        
        # 检查端口是否监听
        if command -v netstat &> /dev/null; then
            local port_status=$(netstat -tlnp 2>/dev/null | grep ":$PORT " | grep "$pid")
            if [ -n "$port_status" ]; then
                echo "  - 端口状态: 正在监听 $PORT"
            else
                echo "  - 端口状态: 未监听 $PORT (可能正在启动中)"
            fi
        fi
    else
        echo "✗ 应用未运行"
        return 1
    fi
}

show_logs() {
    if [ -f "$LOG_FILE" ]; then
        echo "显示日志文件: $LOG_FILE"
        echo "================================"
        tail -f "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

show_usage() {
    echo "FastGate Demo 应用控制脚本"
    echo ""
    echo "功能: 自动检测环境配置并启动相应环境的应用"
    echo ""
    echo "使用方法:"
    echo "  $0 [操作]"
    echo ""
    echo "操作:"
    echo "  start   启动应用 (默认)"
    echo "  stop    停止应用"
    echo "  restart 重启应用"
    echo "  status  查看状态"
    echo "  logs    查看日志"
    echo ""
    echo "环境检测:"
    echo "  脚本会自动从配置文件中检测环境 (dev/prod)"
    echo "  配置文件优先级: config/application.yml > application-external.yml > target/config/application.yml > src/main/resources/application.yml"
    echo ""
    echo "示例:"
    echo "  $0              # 自动检测环境并启动"
    echo "  $0 start        # 自动检测环境并启动"
    echo "  $0 stop         # 停止应用"
    echo "  $0 status       # 查看运行状态"
    echo "  $0 logs         # 查看实时日志"
}

# 主逻辑
case "$ACTION" in
    start)
        start_app
        ;;
    stop)
        stop_app
        ;;
    restart)
        stop_app
        sleep 2
        start_app
        ;;
    status)
        status_app
        ;;
    logs)
        show_logs
        ;;
    *)
        show_usage
        exit 1
        ;;
esac