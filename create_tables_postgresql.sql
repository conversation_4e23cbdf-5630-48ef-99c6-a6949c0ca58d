-- PostgreSQL 建表脚本
-- 从MySQL中间库转换而来的智能门禁/人脸识别系统数据库
-- 创建时间: 自动生成

-- 删除现有表（如果存在）
DROP TABLE IF EXISTS person_dormitory_relation CASCADE;
DROP TABLE IF EXISTS person_card_info CASCADE;
DROP TABLE IF EXISTS face_photo CASCADE;
DROP TABLE IF EXISTS entry_record_images CASCADE;
DROP TABLE IF EXISTS entry_record CASCADE;
DROP TABLE IF EXISTS middleware_person_info CASCADE;
DROP TABLE IF EXISTS person_info CASCADE;
DROP TABLE IF EXISTS dormitory_info CASCADE;

-- 创建自动更新时间戳的函数
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 1. 宿舍信息表
CREATE TABLE dormitory_info (
    id BIGSERIAL PRIMARY KEY,
    dormitory_code VARCHAR(50) NOT NULL UNIQUE,
    building_code VARCHAR(50) NOT NULL,
    floor INTEGER NOT NULL,
    room_num INTEGER NOT NULL,
    room_name VARCHAR(100) NOT NULL,
    parent_code VARCHAR(50) NOT NULL,
    floor_name VARCHAR(100),
    gender_code VARCHAR(10),
    bed_count INTEGER,
    status INTEGER DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 基础人员信息表
CREATE TABLE person_info (
    seqid VARCHAR(50) PRIMARY KEY,
    person_name VARCHAR(100) NOT NULL,
    person_code VARCHAR(50) NOT NULL UNIQUE,
    department_code VARCHAR(50),
    gender INTEGER CHECK (gender IN (0, 1, 2)), -- 0:未知, 1:男, 2:女
    telephone VARCHAR(20),
    idcard VARCHAR(18),
    status INTEGER DEFAULT 1 CHECK (status IN (0, 1)), -- 0:禁用, 1:启用
    update_time VARCHAR(20),
    sync_flag INTEGER DEFAULT 0 CHECK (sync_flag IN (0, 1)), -- 0:未同步, 1:已同步
    person_type VARCHAR(10),
    cardnum VARCHAR(50),
    cardtype VARCHAR(10),
    person_picture BYTEA
);

-- 3. 中间件人员信息表
CREATE TABLE middleware_person_info (
    id BIGSERIAL PRIMARY KEY,
    per_code VARCHAR(50) NOT NULL UNIQUE,
    acc_num VARCHAR(50) NOT NULL UNIQUE,
    acc_name VARCHAR(100) NOT NULL,
    gender INTEGER NOT NULL CHECK (gender IN (0, 1, 2)), -- 0:未知, 1:男, 2:女
    status INTEGER DEFAULT 1,
    per_type INTEGER NOT NULL,
    id_card VARCHAR(50),
    telephone VARCHAR(255),
    email VARCHAR(255),
    acc_type INTEGER,
    department_code VARCHAR(255),
    department_name VARCHAR(255),
    supervisor_job_no VARCHAR(50),
    birthday VARCHAR(20),
    address VARCHAR(255),
    nation VARCHAR(50),
    ic_card VARCHAR(50),
    qrcode VARCHAR(50),
    bluetooth_card VARCHAR(50),
    register_time TIMESTAMP,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    remark VARCHAR(255),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(255),
    update_by VARCHAR(255)
);

-- 4. 出入记录表
CREATE TABLE entry_record (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(64) NOT NULL,
    lib_id INTEGER NOT NULL DEFAULT 0,
    lib_type SMALLINT NOT NULL,
    match_status SMALLINT NOT NULL,
    record_uuid VARCHAR(64) NOT NULL UNIQUE,
    device_direction SMALLINT,
    check_time TIMESTAMP,
    match_person_id INTEGER,
    match_face_id INTEGER,
    match_confidence INTEGER,
    name VARCHAR(50),
    sex SMALLINT CHECK (sex IN (0, 1, 2)), -- 0:未知, 1:男, 2:女
    code_type SMALLINT,
    code_no VARCHAR(64),
    identity_id VARCHAR(64),
    card_id VARCHAR(64),
    card_status SMALLINT,
    temperature DECIMAL(4,1),
    mask SMALLINT CHECK (mask IN (0, 1)), -- 0:未戴口罩, 1:戴口罩
    qr_code VARCHAR(128),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. 出入记录图像表
CREATE TABLE entry_record_images (
    id BIGSERIAL PRIMARY KEY,
    record_uuid VARCHAR(64) NOT NULL,
    image_id VARCHAR(64) NOT NULL,
    type VARCHAR(8) NOT NULL,
    file_format VARCHAR(10) NOT NULL,
    shot_time TIMESTAMP NOT NULL,
    width INTEGER NOT NULL,
    height INTEGER NOT NULL,
    device_id VARCHAR(64),
    storage_path VARCHAR(256),
    image_data BYTEA,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 人脸照片表
CREATE TABLE face_photo (
    id BIGSERIAL PRIMARY KEY,
    per_code VARCHAR(50) NOT NULL,
    acc_num VARCHAR(50) NOT NULL,
    photo_data TEXT NOT NULL,
    photo_path VARCHAR(255) NOT NULL,
    face_status INTEGER NOT NULL DEFAULT 1,
    application_status INTEGER NOT NULL DEFAULT 1,
    photo_fix_id VARCHAR(50),
    feature_path VARCHAR(255),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(255),
    update_by VARCHAR(255)
);

-- 7. 人员证件信息表
CREATE TABLE person_card_info (
    id BIGSERIAL PRIMARY KEY,
    record_uuid VARCHAR(64) NOT NULL,
    device_id VARCHAR(64) NOT NULL,
    cap_time TIMESTAMP,
    id_type VARCHAR(3),
    id_type_ext VARCHAR(32),
    card_status SMALLINT,
    phy_card_id VARCHAR(128),
    name VARCHAR(50),
    gender_code CHAR(1) CHECK (gender_code IN ('0', '1', '2')), -- 0:未知, 1:男, 2:女
    ethic_code VARCHAR(2),
    birthday TIMESTAMP,
    address VARCHAR(128),
    id_number VARCHAR(32),
    issuing_authority VARCHAR(128),
    issuing_date TIMESTAMP,
    valid_date_start TIMESTAMP,
    valid_date_end TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 人员宿舍关系表
CREATE TABLE person_dormitory_relation (
    id BIGSERIAL PRIMARY KEY,
    person_code VARCHAR(50) NOT NULL,
    dormitory_code VARCHAR(50) NOT NULL,
    bed_no VARCHAR(20),
    assign_date VARCHAR(20),
    status INTEGER DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
-- dormitory_info 表索引
CREATE INDEX idx_dormitory_building_code ON dormitory_info(building_code);
CREATE INDEX idx_dormitory_floor ON dormitory_info(floor);
CREATE INDEX idx_dormitory_room_num ON dormitory_info(room_num);
CREATE INDEX idx_dormitory_parent_code ON dormitory_info(parent_code);
CREATE INDEX idx_dormitory_status ON dormitory_info(status);

-- person_info 表索引
CREATE INDEX idx_person_info_person_code ON person_info(person_code);
CREATE INDEX idx_person_info_status ON person_info(status);
CREATE INDEX idx_person_info_department_code ON person_info(department_code);

-- middleware_person_info 表索引
CREATE INDEX idx_middleware_acc_name ON middleware_person_info(acc_name);
CREATE INDEX idx_middleware_status ON middleware_person_info(status);
CREATE INDEX idx_middleware_per_type ON middleware_person_info(per_type);
CREATE INDEX idx_middleware_id_card ON middleware_person_info(id_card);

-- entry_record 表索引
CREATE INDEX idx_entry_device_id ON entry_record(device_id);
CREATE INDEX idx_entry_lib_type ON entry_record(lib_type);
CREATE INDEX idx_entry_match_status ON entry_record(match_status);
CREATE INDEX idx_entry_check_time ON entry_record(check_time);
CREATE INDEX idx_entry_identity_id ON entry_record(identity_id);
CREATE INDEX idx_entry_card_id ON entry_record(card_id);

-- entry_record_images 表索引
CREATE INDEX idx_images_record_uuid ON entry_record_images(record_uuid);
CREATE INDEX idx_images_image_id ON entry_record_images(image_id);
CREATE INDEX idx_images_type ON entry_record_images(type);
CREATE INDEX idx_images_shot_time ON entry_record_images(shot_time);
CREATE INDEX idx_images_device_id ON entry_record_images(device_id);

-- face_photo 表索引
CREATE INDEX idx_face_per_code ON face_photo(per_code);
CREATE INDEX idx_face_acc_num ON face_photo(acc_num);
CREATE INDEX idx_face_status ON face_photo(face_status);
CREATE INDEX idx_face_application_status ON face_photo(application_status);

-- person_card_info 表索引
CREATE INDEX idx_card_record_uuid ON person_card_info(record_uuid);
CREATE INDEX idx_card_device_id ON person_card_info(device_id);
CREATE INDEX idx_card_cap_time ON person_card_info(cap_time);
CREATE INDEX idx_card_phy_card_id ON person_card_info(phy_card_id);
CREATE INDEX idx_card_name ON person_card_info(name);
CREATE INDEX idx_card_id_number ON person_card_info(id_number);

-- person_dormitory_relation 表索引
CREATE INDEX idx_relation_person_code ON person_dormitory_relation(person_code);
CREATE INDEX idx_relation_dormitory_code ON person_dormitory_relation(dormitory_code);
CREATE INDEX idx_relation_status ON person_dormitory_relation(status);
-- 优化已分配宿舍学生查询的复合索引
CREATE INDEX idx_relation_status_person_code ON person_dormitory_relation(status, person_code);

-- 添加外键约束
ALTER TABLE person_dormitory_relation 
ADD CONSTRAINT fk_relation_person_code 
FOREIGN KEY (person_code) REFERENCES person_info(person_code);

ALTER TABLE person_dormitory_relation 
ADD CONSTRAINT fk_relation_dormitory_code 
FOREIGN KEY (dormitory_code) REFERENCES dormitory_info(dormitory_code);

ALTER TABLE entry_record_images 
ADD CONSTRAINT fk_images_record_uuid 
FOREIGN KEY (record_uuid) REFERENCES entry_record(record_uuid);

-- 创建自动更新时间戳的触发器
CREATE TRIGGER update_dormitory_info_modtime 
BEFORE UPDATE ON dormitory_info 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_middleware_person_info_modtime 
BEFORE UPDATE ON middleware_person_info 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_entry_record_modtime 
BEFORE UPDATE ON entry_record 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_entry_record_images_modtime 
BEFORE UPDATE ON entry_record_images 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_face_photo_modtime 
BEFORE UPDATE ON face_photo 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_person_card_info_modtime 
BEFORE UPDATE ON person_card_info 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_person_dormitory_relation_modtime 
BEFORE UPDATE ON person_dormitory_relation 
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- 添加表注释
COMMENT ON TABLE dormitory_info IS '宿舍信息表 - 存储宿舍楼、楼层、房间等基础信息';
COMMENT ON TABLE person_info IS '基础人员信息表 - 存储人员基本档案信息';
COMMENT ON TABLE middleware_person_info IS '中间件人员信息表 - 存储详细的人员档案信息';
COMMENT ON TABLE entry_record IS '出入记录表 - 存储门禁通行记录';
COMMENT ON TABLE entry_record_images IS '出入记录图像表 - 存储门禁抓拍图片';
COMMENT ON TABLE face_photo IS '人脸照片表 - 存储人脸识别用照片';
COMMENT ON TABLE person_card_info IS '人员证件信息表 - 存储身份证等证件信息';
COMMENT ON TABLE person_dormitory_relation IS '人员宿舍关系表 - 存储人员住宿分配关系';

-- 添加重要字段注释
COMMENT ON COLUMN dormitory_info.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN middleware_person_info.gender IS '性别：0-未知，1-男，2-女';
COMMENT ON COLUMN entry_record.match_status IS '匹配状态：识别结果状态';
COMMENT ON COLUMN entry_record.device_direction IS '设备方向：进出方向标识';
COMMENT ON COLUMN entry_record.mask IS '口罩：0-未戴，1-戴口罩';
COMMENT ON COLUMN face_photo.face_status IS '人脸状态：人脸照片状态';
COMMENT ON COLUMN face_photo.application_status IS '应用状态：照片应用状态'; 