# 中间库设计说明文档

    
## 1. 表结构设计

### 1.1. 人员信息表(person_info)

人员信息表存储学生和教职工的基本信息，从学工系统获取并映射到EGS平台的tbl_person表。

|                   |              |          |                   |           |
| ----------------- | ------------ | -------- | ----------------- | --------- |
| **字段名**           | **数据类型**     | **是否必填** | **描述**            | **来源**    |
| **id**            | bigint(20)   | **是**    | 主键ID(自增)          | 中间库生成     |
| **per_code**      | varchar(50)  | **是**    | 人员编码(与一卡通人员编号对应)  | 一卡通系统     |
| **acc_num**       | varchar(50)  | **是**    | 学号/工号(与学工系统学号对应)  | 学工系统XSBH  |
| **acc_name**      | varchar(100) | **是**    | 姓名                | 学工系统XM    |
| **gender**        | int(11)      | **是**    | 性别(1-男，2-女，0-未知)  | 学工系统XBDM  |
| **status**        | int(11)      | **是**    | 状态(1-启用，0-禁用)     | 中间库生成     |
| **per_type**      | int(11)      | **是**    | 人员类型(3-学生，5-教职工等) | 中间库生成     |
| id_card           | varchar(50)  | 否        | 身份证号              | 学工系统SFZJH |
| telephone         | varchar(255) | 否        | 电话号码              | 学工系统SJH   |
| email             | varchar(255) | 否        | 电子邮箱              | 学工系统DZXX  |
| acc_type          | int(11)      | 否        | 账号类型              | 中间库生成     |
| department_code   | varchar(255) | 否        | 部门编码              | 学工系统DWDM  |
| department_name   | varchar(255) | 否        | 部门名称              | 学工系统      |
| supervisor_job_no | varchar(50)  | 否        | 导师职工号(学生类型人员必需)   | 学工系统DSZGH |
| birthday          | varchar(20)  | 否        | 生日                | 学工系统CSRQ  |
| address           | varchar(255) | 否        | 地址                | 学工系统      |
| nation            | varchar(50)  | 否        | 民族                | 学工系统MZDM  |
| ic_card           | varchar(50)  | 否        | IC卡号              | 一卡通系统     |
| qrcode            | varchar(50)  | 否        | 二维码               | 一卡通系统     |
| bluetooth_card    | varchar(50)  | 否        | 蓝牙卡号              | 一卡通系统     |
| register_time     | datetime     | 否        | 注册时间              | 中间库生成     |
| start_time        | datetime     | 否        | 开始时间              | 中间库生成     |
| end_time          | datetime     | 否        | 结束时间              | 中间库生成     |
| remark            | varchar(255) | 否        | 备注                | 学工系统      |
| create_time       | datetime     | 否        | 创建时间              | 中间库生成     |
| update_time       | datetime     | 否        | 更新时间              | 中间库生成     |
| create_by         | varchar(255) | 否        | 创建人               | 中间库生成     |
| update_by         | varchar(255) | 否        | 更新人               | 中间库生成     |

### 1.2. 人脸照片表(face_photo)

人脸照片表存储人员的人脸照片数据，从一卡通系统获取并映射到EGS平台的tbl_person_picture表。

|   |   |   |   |   |
|---|---|---|---|---|
|**字段名**|**数据类型**|**是否必填**|**描述**|**来源**|
|**id**|bigint(20)|**是**|主键ID(自增)|中间库生成|
|**per_code**|varchar(50)|**是**|人员编码(与person_info表关联)|一卡通系统|
|**acc_num**|varchar(50)|**是**|学号/工号|学工系统XSBH|
|**photo_data**|longtext|**是**|照片数据(Base64编码)|一卡通系统|
|**photo_path**|varchar(255)|**是**|照片路径(EGS系统需要)|中间库生成|
|**face_status**|int(11)|**是**|人脸状态(1-有效，0-无效)|一卡通系统|
|**application_status**|int(11)|**是**|应用状态(1-有效，0-无效)|中间库生成|
|photo_fix_id|varchar(50)|否|照片固定ID|一卡通系统|
|feature_path|varchar(255)|否|特征路径|中间库生成|
|create_time|datetime|否|创建时间|中间库生成|
|update_time|datetime|否|更新时间|中间库生成|
|create_by|varchar(255)|否|创建人|中间库生成|
|update_by|varchar(255)|否|更新人|中间库生成|

### 1.3. 宿舍信息表(dormitory_info)

宿舍信息表存储宿舍的基本信息，从学工系统获取并映射到EGS平台的tbl_dormitory表。

|                    |              |          |                |               |
| ------------------ | ------------ | -------- | -------------- | ------------- |
| **字段名**            | **数据类型**     | **是否必填** | **描述**         | **来源**        |
| **id**             | bigint(20)   | **是**    | 主键ID(自增)       | 中间库生成         |
| **dormitory_code** | varchar(50)  | **是**    | 宿舍代码           | 学工系统SSDM      |
| **building_code**  | varchar(50)  | **是**    | 楼宇代码           | 学工系统SSLDM     |
| **building_name**  | varchar(50)  | **是**    | 楼宇名称           | 学工系统**SSLMC** |
| **floor**          | int(11)      | **是**    | 楼层             | 学工系统LC        |
| **room_num**       | int(11)      | **是**    | 房间号            | 学工系统FJH       |
| **room_name**      | varchar(100) | **是**    | 房间名称           | 中间库生成         |
| **parent_code**    | varchar(50)  | **是**    | 父级编码(楼宇编码)     | 学工系统SSLDM     |
| floor_name         | varchar(100) | 否        | 楼层名称           | 中间库生成         |
| gender_code        | varchar(10)  | 否        | 性别代码           | 学工系统XBDM      |
| bed_count          | int(11)      | 否        | 床位数            | 学工系统CWS       |
| status             | int(11)      | 否        | 状态(1-使用，0-不使用) | 学工系统SFSY      |
| create_time        | datetime     | 否        | 创建时间           | 中间库生成         |
| update_time        | datetime     | 否        | 更新时间           | 中间库生成         |

### 1.4. 人员宿舍关联表(person_dormitory_relation)

人员宿舍关联表存储人员与宿舍的关联信息，从学工系统获取并映射到EGS平台的tbl_person_dormitory表。

|   |   |   |   |   |
|---|---|---|---|---|
|**字段名**|**数据类型**|**是否必填**|**描述**|**来源**|
|**id**|bigint(20)|**是**|主键ID(自增)|中间库生成|
|**person_code**|varchar(50)|**是**|人员编码(与person_info表关联)|学工系统XSBH|
|**dormitory_code**|varchar(50)|**是**|宿舍代码(与dormitory_info表关联)|学工系统SSDM|
|bed_no|varchar(20)|否|床位号|学工系统CWH|
|assign_date|varchar(20)|否|分配日期|学工系统FPRQ|
|status|int(11)|否|状态(1-有效，0-无效)|中间库生成|
|create_time|datetime|否|创建时间|中间库生成|
|update_time|datetime|否|更新时间|中间库生成|

### 1.5. EGS推送数据表

#### 1.5.1. 出入记录表(entry_record)

出入记录表存储EGS系统推送的人员核验/出入记录主要信息。

|                  |              |          |                                     |         |
| ---------------- | ------------ | -------- | ----------------------------------- | ------- |
| **字段名**          | **数据类型**     | **是否必填** | **描述**                              | **来源**  |
| **id**           | bigint(20)   | **是**    | 主键ID(自增)                            | 中间库生成   |
| **device_id**    | varchar(64)  | **是**    | 设备编码                                | EGS推送系统 |
| **lib_id**       | int(11)      | **是**    | 库ID(默认0，实际为人员在终端的库ID)               | EGS推送系统 |
| **lib_type**     | tinyint(4)   | **是**    | 库类型(0-陌生人，1-库内人员（学生和老师），2-访客，3-黑名单) | EGS推送系统 |
| **match_status** | tinyint(4)   | **是**    | 匹配状态(1-匹配成功，2-匹配失败，3-匹配成功但不在规定时间)   | EGS推送系统 |
| **record_uuid**  | varchar(64)  | **是**    | 记录唯一标识(用于关联其他表)                     | EGS推送系统 |
| device_direction | tinyint(4)   | 否        | 设备方向(1-进，2-出)                       | EGS推送系统 |
| check_time       | datetime     | 否        | 核验时间                                | EGS推送系统 |
| match_person_id  | int(11)      | 否        | 匹配人员ID                              | EGS推送系统 |
| match_face_id    | int(11)      | 否        | 匹配人脸ID                              | EGS推送系统 |
| match_confidence | int(11)      | 否        | 匹配相似度(0-100)                        | EGS推送系统 |
| name             | varchar(50)  | 否        | 姓名(未识别为"-")                         | EGS推送系统 |
| sex              | tinyint(4)   | 否        | 性别(0-未知，1-男，2-女，9-未说明)              | EGS推送系统 |
| code_type        | tinyint(4)   | 否        | 编码类型(1-员工卡，2-学生卡/校园卡)               | EGS推送系统 |
| code_no          | varchar(64)  | 否        | 编码                                  | EGS推送系统 |
| identity_id      | varchar(64)  | 否        | 身份证号/IC卡号                           | EGS推送系统 |
| card_id          | varchar(64)  | 否        | 卡号                                  | EGS推送系统 |
| card_status      | tinyint(4)   | 否        | 卡状态(0-无效，1-有效)                      | EGS推送系统 |
| temperature      | decimal(4,1) | 否        | 体温(0-未启用)                           | EGS推送系统 |
| mask             | tinyint(4)   | 否        | 口罩状态(0-未知，1-未戴口罩，2-戴口罩)             | EGS推送系统 |
| qr_code          | varchar(128) | 否        | 二维码内容                               | EGS推送系统 |
| create_time      | datetime     | 否        | 创建时间                                | 中间库生成   |
| update_time      | datetime     | 否        | 更新时间                                | 中间库生成   |

#### 1.5.2. 出入记录图像表(entry_record_images)

出入记录图像表存储与出入记录相关的图像信息，通过record_uuid与主记录关联。

|   |   |   |   |   |
|---|---|---|---|---|
|**字段名**|**数据类型**|**是否必填**|**描述**|**来源**|
|**id**|bigint(20)|**是**|主键ID(自增)|中间库生成|
|**record_uuid**|varchar(64)|**是**|关联的出入记录UUID|EGS推送系统|
|**image_id**|varchar(64)|**是**|图像标识|EGS推送系统|
|**type**|varchar(8)|**是**|图片类型(11-人脸抠图，14-抓拍图，80-身份证图)|EGS推送系统|
|**file_format**|varchar(10)|**是**|图像文件格式(1-BMP，2-GIF，3-JPEG)|EGS推送系统|
|**shot_time**|datetime|**是**|拍摄时间|EGS推送系统|
|**width**|int(11)|**是**|水平像素值|EGS推送系统|
|**height**|int(11)|**是**|垂直像素值|EGS推送系统|
|device_id|varchar(64)|否|采集设备编码|EGS推送系统|
|storage_path|varchar(256)|否|图像文件的存储路径|EGS推送系统|
|image_data|longblob|否|base64编码的图像数据|EGS推送系统|
|create_time|datetime|否|创建时间|中间库生成|
|update_time|datetime|否|更新时间|中间库生成|

#### 1.5.3. 人员卡证信息表(person_card_info)

人员卡证信息表存储EGS系统推送的人员卡证详细信息，通过record_uuid与主记录关联。

|   |   |   |   |   |
|---|---|---|---|---|
|**字段名**|**数据类型**|**是否必填**|**描述**|**来源**|
|**id**|bigint(20)|**是**|主键ID(自增)|中间库生成|
|**record_uuid**|varchar(64)|**是**|关联的出入记录UUID|EGS推送系统|
|**device_id**|varchar(64)|**是**|设备编码|EGS推送系统|
|cap_time|datetime|否|采集时间|EGS推送系统|
|id_type|varchar(3)|否|证件种类(111-居民身份证，112-临时居民身份证，990-其他)|EGS推送系统|
|id_type_ext|varchar(32)|否|扩展证件种类(id_type为990时使用，1-门禁卡)|EGS推送系统|
|card_status|tinyint(4)|否|门禁卡状态(1-有效，0-无效)|EGS推送系统|
|phy_card_id|varchar(128)|否|物理卡号|EGS推送系统|
|name|varchar(50)|否|人员的中文姓名全称|EGS推送系统|
|gender_code|char(1)|否|性别代码(0-未知，1-男，2-女，9-未说明)|EGS推送系统|
|ethic_code|varchar(2)|否|民族代码(GB/T 3304)|EGS推送系统|
|birthday|datetime|否|出生日期|EGS推送系统|
|address|varchar(128)|否|住址|EGS推送系统|
|id_number|varchar(32)|否|有效证件号码|EGS推送系统|
|issuing_authority|varchar(128)|否|签发机关|EGS推送系统|
|issuing_date|datetime|否|发证日期|EGS推送系统|
|valid_date_start|datetime|否|有效期起始日期|EGS推送系统|
|valid_date_end|datetime|否|有效期截止日期|EGS推送系统|
|create_time|datetime|否|创建时间|中间库生成|
|update_time|datetime|否|更新时间|中间库生成|
### 1.6. 学生请假申请表(student_leave_application)

学生请假申请表存储学生的请假申请信息，从学工系统获取。

|字段名|数据类型|是否必填|描述|来源 (学工系统字段)|
|---|---|---|---|---|
|**id**|bigint(20)|**是**|主键ID(自增)|中间库生成|
|**application_no**|varchar(40)|**是**|申请编号|学工系统SQBH|
|**student_no**|varchar(40)|**是**|学生编号 (对应学工系统XSBH，可用于关联person_info表的acc_num)|学工系统XSBH|
|academic_year|varchar(10)|否|学年|学工系统XN|
|semester|varchar(10)|否|学期|学工系统XQ|
|leave_nature|varchar(40)|否|请假性质|学工系统QJXZ|
|leave_type|varchar(40)|否|请假类型|学工系统QJLX|
|application_date|datetime|否|申请日期|学工系统SQRQ|
|leave_start_date|datetime|否|请假开始日期|学工系统QJKSRQ|
|leave_end_date|datetime|否|请假结束日期|学工系统QJJSRQ|
|leave_days|decimal(5,1)|否|请假天数|学工系统QJTS|
|leave_reason|varchar(3000)|否|请假事由|学工系统QJSY|
|supporting_materials|varchar(40)|否|证明材料|学工系统ZMCL|
|leave_cancellation_status|varchar(3)|否|销假状态|学工系统XJZT|
|leave_cancellation_type|varchar(3)|否|销假类型 (1-人工销假 2-自动销假)|学工系统XJLX|
|leave_cancellation_date|datetime|否|销假日期|学工系统XJRQ|
|leave_canceller|varchar(40)|否|销假人|学工系统XJR|
|leave_cancellation_notes|varchar(600)|否|销假说明|学工系统XJSM|
|approval_status|varchar(3)|否|审核状态|学工系统SHZT|
|approval_comments|varchar(4000)|否|审核意见|学工系统SHYJ|
|rejection_status|varchar(3)|否|退回状态|学工系统THZT|
|source_type|varchar(3)|否|来源类型 (1-学生申请；2-老师新增；3-导入)|学工系统LYLX|
|create_time|datetime|否|创建时间|中间库生成|
|update_time|datetime|否|更新时间|中间库生成|
|create_by|varchar(255)|否|创建人|中间库生成|
|update_by|varchar(255)|否|更新人|中间库生成|
## 2. 附录：数据字典

### 2.1. 人员类型(per_type)

- 3: 学生
    
- 5: 教职工
    

### 2.2. 状态(status)

- 0: 禁用/无效
    
- 1: 启用/有效
    

### 2.3. EGS推送数据枚举值

#### 2.3.1. 库类型(lib_type)

- 0: 陌生人
    
- 1: 学生或教师
    
- 2: 访客
    
- 3: 黑名单
    

#### 2.3.2. 匹配状态(match_status)

- 1: 匹配成功
    
- 2: 匹配失败
    
- 3: 匹配成功但不在规定时间
    

#### 2.3.3. 设备方向(device_direction)

- 1: 进
    
- 2: 出
    

#### 2.3.4. 图片类型(type)

- 11: 人脸抠图
    
- 14: 抓拍图
    
- 80: 身份证图
    

#### 2.3.5. 图像文件格式(file_format)

- 1: BMP
    
- 2: GIF
    
- 3: JPEG
    

#### 2.3.6. 证件种类(id_type)

- 111: 居民身份证
    
- 112: 临时居民身份证
    
- 990: 其他
    

#### 2.3.7. 扩展证件种类(id_type_ext)

- 1: 门禁卡(当id_type为990时使用)
    

#### 2.3.8. 编码类型(code_type)

- 1: 员工卡
    
- 2: 学生卡/校园卡
    

#### 2.3.9. 口罩状态(mask)

- 0: 未知
    
- 1: 未戴口罩
    
- 2: 戴口罩