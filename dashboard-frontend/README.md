# 宿舍归寝情况大屏展示系统

## 项目简介

这是一个基于Vue 3的宿舍归寝情况大屏展示系统，专为电视大屏展示设计，提供实时的学生归寝数据可视化。

## 功能特性

### 核心功能
- **归寝情况统计**: 实时显示总人数、在寝人数、外出人数和归寝率
- **实时进出记录**: 滚动展示最新的学生进出记录
- **数据可视化**: 使用ECharts图表直观展示归寝情况分布
- **自动刷新**: 每30秒自动更新数据，确保信息实时性

### 大屏特性
- **全屏展示**: 适配大屏幕分辨率，无操作界面
- **自动滚动**: 进出记录自动滚动显示
- **美观界面**: 深色主题，适合长时间观看
- **响应式设计**: 支持不同尺寸的显示设备

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **图表库**: ECharts 5
- **构建工具**: Vite
- **样式预处理**: Sass/SCSS
- **HTTP客户端**: Axios

## 项目结构

```
dashboard-frontend/
├── src/
│   ├── api/                 # API接口封装
│   │   └── dormitory.js     # 宿舍相关API
│   ├── components/          # Vue组件
│   │   ├── StatisticsPanel.vue  # 统计面板组件
│   │   └── RecordsPanel.vue     # 记录面板组件
│   ├── styles/              # 样式文件
│   │   ├── variables.scss   # SCSS变量
│   │   └── global.scss      # 全局样式
│   ├── App.vue              # 根组件
│   └── main.js              # 入口文件
├── index.html               # HTML模板
├── vite.config.js           # Vite配置
├── package.json             # 项目依赖
└── README.md                # 项目说明
```

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd dashboard-frontend
npm install
```

### 开发模式
```bash
npm run dev
```
访问 http://localhost:3000

### 生产构建
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## API接口

系统依赖后端提供以下API接口：

### 统计接口
- `GET /api/dormitory-status/statistics` - 获取归寝统计信息
- `GET /api/dormitory-status/health` - 系统健康检查

### 记录接口
- `GET /api/dormitory-status/returned` - 获取已归寝人员列表
- `GET /api/dormitory-status/not-returned` - 获取未归寝人员列表

## 配置说明

### 代理配置
在 `vite.config.js` 中配置了API代理：
```javascript
proxy: {
  '/api': {
    target: 'http://localhost:8080',
    changeOrigin: true,
    secure: false
  }
}
```

### 刷新间隔
在 `App.vue` 中可以调整数据刷新间隔：
```javascript
const refreshInterval = 30000 // 30秒刷新一次
```

## 部署说明

### 1. 构建项目
```bash
npm run build
```

### 2. 部署到Web服务器
将 `dist` 目录下的文件部署到Web服务器（如Nginx、Apache等）

### 3. 配置反向代理
确保Web服务器配置了到后端API的反向代理：
```nginx
location /api/ {
    proxy_pass http://localhost:8080/api/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

## 故障排除

### 数据不显示
1. 检查后端服务是否正常运行
2. 检查API接口是否可访问
3. 查看浏览器控制台错误信息

### 自动刷新不工作
1. 检查网络连接
2. 确认API接口响应正常
3. 查看控制台是否有JavaScript错误

### 样式显示异常
1. 确认所有CSS文件已正确加载
2. 检查浏览器兼容性
3. 清除浏览器缓存

## 开发说明

### 添加新功能
1. 在 `src/api/` 中添加新的API接口
2. 在 `src/components/` 中创建新组件
3. 在 `App.vue` 中集成新组件

### 样式定制
1. 修改 `src/styles/variables.scss` 中的变量
2. 在组件中使用SCSS变量
3. 遵循现有的样式规范

### 性能优化
- 使用Vue 3的响应式系统
- 合理使用计算属性和监听器
- 避免不必要的DOM操作
- 使用虚拟滚动处理大量数据

## 许可证

MIT License
