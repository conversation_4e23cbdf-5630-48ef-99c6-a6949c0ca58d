@echo off
echo ========================================
echo 宿舍归寝情况大屏展示系统
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js环境检查通过
echo.

echo 正在检查项目依赖...
if not exist "node_modules" (
    echo 首次运行，正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
    echo 依赖安装完成
    echo.
)

echo 正在启动开发服务器...
echo 请确保后端服务已在 http://localhost:8080 运行
echo.
echo 启动后请访问: http://localhost:3000
echo 按 Ctrl+C 停止服务
echo.

npm run dev

pause
