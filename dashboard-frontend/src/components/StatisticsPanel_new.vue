<template>
  <div class="statistics-panel">
    <div class="panel-header">
      <h2 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        归寝情况统计
      </h2>
      <div class="update-time">
        <el-icon><Clock /></el-icon>
        更新时间: {{ updateTime }}
      </div>
    </div>
    
    <div class="statistics-grid">
      <!-- 总人数 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon total">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.totalPersons || 0 }}</div>
          <div class="stat-label">总人数</div>
        </div>
      </div>
      
      <!-- 在寝人数 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon in-dormitory">
          <el-icon><House /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.inDormitoryPersons || 0 }}</div>
          <div class="stat-label">在寝人数</div>
        </div>
      </div>
      
      <!-- 外出人数 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon out-dormitory">
          <el-icon><Position /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.outDormitoryPersons || 0 }}</div>
          <div class="stat-label">外出人数</div>
        </div>
      </div>
      
      <!-- 归寝率 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon return-rate">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ returnRate }}%</div>
          <div class="stat-label">归寝率</div>
        </div>
      </div>
      
      <!-- 无记录人员 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon no-record">
          <el-icon><QuestionFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.personsWithoutRecords || 0 }}</div>
          <div class="stat-label">无记录人员</div>
        </div>
      </div>
    </div>
    
    <!-- 图表展示 -->
    <div class="chart-container dashboard-card">
      <div class="chart-header">
        <h3 class="chart-title">
          <el-icon><PieChart /></el-icon>
          归寝情况分布
        </h3>
        <div class="chart-subtitle">实时数据可视化</div>
      </div>
      <div ref="chartRef" class="chart"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { DataAnalysis, Clock, User, House, Position, TrendCharts, QuestionFilled, PieChart } from '@element-plus/icons-vue'

const props = defineProps({
  statistics: {
    type: Object,
    default: () => ({})
  }
})

const chartRef = ref(null)
const updateTime = ref('')
let chart = null

// 计算归寝率
const returnRate = computed(() => {
  const total = props.statistics.totalPersons || 0
  const inDormitory = props.statistics.inDormitoryPersons || 0
  return total > 0 ? ((inDormitory / total) * 100).toFixed(1) : '0.0'
})

// 更新时间
const updateCurrentTime = () => {
  updateTime.value = new Date().toLocaleString('zh-CN')
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!chart || !chartRef.value) return
  
  // 获取容器尺寸
  const containerWidth = chartRef.value.clientWidth
  const containerHeight = chartRef.value.clientHeight
  
  // 根据容器尺寸动态调整图表配置
  const isSmallContainer = containerWidth < 400 || containerHeight < 200
  const legendFontSize = isSmallContainer ? 10 : 12
  const pieRadius = isSmallContainer ? ['20%', '50%'] : ['25%', '60%']
  const pieCenter = isSmallContainer ? ['40%', '50%'] : ['35%', '50%']
  
  const option = {
    backgroundColor: 'transparent',
    animation: true,
    animationDuration: 1500,
    animationEasing: 'cubicOut',
    animationDelay: function (idx) {
      return idx * 200;
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const total = props.statistics.totalPersons || 0;
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(1) : '0.0';
        return `
          <div style="padding: 8px;">
            <div style="font-size: 14px; font-weight: bold; margin-bottom: 6px;">
              ${params.seriesName}
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="font-weight: 500;">${params.name}</span>
            </div>
            <div style="margin-left: 18px; color: #409EFF; font-size: 16px; font-weight: bold;">
              ${params.value} 人 (${percentage}%)
            </div>
          </div>
        `;
      },
      backgroundColor: 'rgba(0, 0, 0, 0.85)',
      borderColor: '#409EFF',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#fff'
      },
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);'
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      textStyle: {
        color: '#fff',
        fontSize: legendFontSize,
        fontWeight: '500'
      },
      itemWidth: isSmallContainer ? 14 : 18,
      itemHeight: isSmallContainer ? 10 : 14,
      itemGap: isSmallContainer ? 10 : 15,
      icon: 'circle',
      formatter: function(name) {
        const data = [
          { name: '在寝人数', value: props.statistics.inDormitoryPersons || 0 },
          { name: '外出人数', value: props.statistics.outDormitoryPersons || 0 },
          { name: '无记录人员', value: props.statistics.personsWithoutRecords || 0 }
        ];
        const item = data.find(d => d.name === name);
        return item ? `${name}\n${item.value}人` : name;
      }
    },
    graphic: [
      {
        type: 'text',
        left: pieCenter[0],
        top: pieCenter[1],
        style: {
          text: `总人数\n${props.statistics.totalPersons || 0}`,
          textAlign: 'center',
          fill: '#fff',
          fontSize: isSmallContainer ? 14 : 18,
          fontWeight: 'bold',
          textShadowColor: 'rgba(0, 0, 0, 0.5)',
          textShadowBlur: 2
        }
      },
      {
        type: 'text',
        left: pieCenter[0],
        top: `${parseInt(pieCenter[1]) + (isSmallContainer ? 25 : 35)}%`,
        style: {
          text: `归寝率 ${returnRate.value}%`,
          textAlign: 'center',
          fill: '#409EFF',
          fontSize: isSmallContainer ? 12 : 16,
          fontWeight: 'bold',
          textShadowColor: 'rgba(0, 0, 0, 0.5)',
          textShadowBlur: 2
        }
      }
    ],
    series: [
      {
        name: '归寝情况',
        type: 'pie',
        radius: pieRadius,
        center: pieCenter,
        avoidLabelOverlap: true,
        roseType: 'area',
        itemStyle: {
          borderRadius: 12,
          borderColor: 'rgba(255, 255, 255, 0.8)',
          borderWidth: 2,
          shadowBlur: 15,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        label: {
          show: true,
          position: 'inside',
          fontSize: isSmallContainer ? 12 : 14,
          fontWeight: 'bold',
          color: '#fff',
          formatter: '{b}\n{c}人\n({d}%)',
          textStyle: {
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 2
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: isSmallContainer ? 16 : 20,
            fontWeight: 'bold',
            color: '#fff',
            formatter: '{b}\n{c}人\n({d}%)'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            borderWidth: 3,
            borderColor: '#fff'
          },
          scale: true,
          scaleSize: 10
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: props.statistics.inDormitoryPersons || 0,
            name: '在寝人数',
            itemStyle: { 
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#67C23A' },
                  { offset: 1, color: '#85CE61' }
                ]
              }
            }
          },
          {
            value: props.statistics.outDormitoryPersons || 0,
            name: '外出人数',
            itemStyle: { 
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#E6A23C' },
                  { offset: 1, color: '#EEBC6C' }
                ]
              }
            }
          },
          {
            value: props.statistics.personsWithoutRecords || 0,
            name: '无记录人员',
            itemStyle: { 
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#909399' },
                  { offset: 1, color: '#B1B3B8' }
                ]
              }
            }
          }
        ]
      },
      {
        name: '归寝率指示器',
        type: 'pie',
        radius: [isSmallContainer ? '65%' : '75%', isSmallContainer ? '70%' : '80%'],
        center: pieCenter,
        silent: true,
        label: {
          show: false
        },
        data: [
          {
            value: parseFloat(returnRate.value),
            name: '已归寝',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#409EFF' },
                  { offset: 1, color: '#66B1FF' }
                ]
              }
            }
          },
          {
            value: 100 - parseFloat(returnRate.value),
            name: '未归寝',
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        ]
      }
    ]
  }
  
  chart.setOption(option, true)
}

// 监听统计数据变化
const updateStatistics = () => {
  nextTick(() => {
    updateChart()
  })
}

onMounted(() => {
  updateCurrentTime()
  // 每秒更新时间
  const timeInterval = setInterval(updateCurrentTime, 1000)
  
  nextTick(() => {
    initChart()
  })
  
  // 监听窗口大小变化
  const resizeHandler = () => {
    if (chart) {
      setTimeout(() => {
        chart.resize()
        updateChart()
      }, 100)
    }
  }
  window.addEventListener('resize', resizeHandler)
  
  onUnmounted(() => {
    clearInterval(timeInterval)
    window.removeEventListener('resize', resizeHandler)
    if (chart) {
      chart.dispose()
    }
  })
})

// 暴露更新方法
defineExpose({
  updateStatistics
})
</script>

<style scoped lang="scss">
.statistics-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 100vh;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-sm;
  flex-shrink: 0;

  .section-title {
    margin: 0;
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }

  .update-time {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    color: $text-secondary;
    font-size: $font-size-sm;
  }
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: $spacing-sm;
  margin-bottom: $spacing-sm;
  flex-shrink: 0;
  height: auto;
  max-height: 25%;
}

.stat-card {
  padding: $spacing-xs $spacing-sm;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  min-height: 60px;
  max-height: 80px;

  .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;

    &.total {
      background: linear-gradient(135deg, #409EFF, #66B1FF);
      color: white;
    }

    &.in-dormitory {
      background: linear-gradient(135deg, #67C23A, #85CE61);
      color: white;
    }

    &.out-dormitory {
      background: linear-gradient(135deg, #E6A23C, #EEBC6C);
      color: white;
    }

    &.return-rate {
      background: linear-gradient(135deg, #F56C6C, #F89898);
      color: white;
    }

    &.no-record {
      background: linear-gradient(135deg, #909399, #B1B3B8);
      color: white;
    }
  }

  .stat-content {
    flex: 1;

    .stat-value {
      font-size: $font-size-display;
      font-weight: 700;
      color: $text-primary;
      line-height: 1;
      margin-bottom: $spacing-xs;
    }

    .stat-label {
      font-size: $font-size-lg;
      color: $text-secondary;
      font-weight: 500;
    }
  }
}

.chart-container {
  flex: 1;
  padding: $spacing-sm;
  min-height: 200px;
  max-height: calc(75vh - 200px);
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(103, 194, 58, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
    pointer-events: none;
  }

  .chart-header {
    text-align: center;
    margin-bottom: $spacing-xs;
    position: relative;
    z-index: 2;
    flex-shrink: 0;
    height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .chart-title {
      margin: 0;
      font-size: $font-size-md;
      font-weight: 600;
      color: $text-primary;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-xs;
      line-height: 1.2;
    }

    .chart-subtitle {
      font-size: $font-size-sm;
      color: $text-secondary;
      opacity: 0.8;
      margin-top: 2px;
      line-height: 1;
    }
  }

  .chart {
    flex: 1;
    width: 100%;
    height: calc(100% - 50px);
    min-height: 150px;
    max-height: calc(75vh - 250px);
    position: relative;
    z-index: 1;
  }
}

@media (max-width: 1200px) {
  .statistics-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: $spacing-sm;
    max-height: 30%;
  }

  .stat-card {
    min-height: 55px;
    max-height: 70px;
    padding: $spacing-xs;

    .stat-icon {
      width: 45px;
      height: 45px;
      font-size: 18px;
    }

    .stat-content {
      .stat-value {
        font-size: $font-size-lg;
      }
    }
  }

  .chart-container {
    min-height: 180px;
    max-height: calc(70vh - 150px);

    .chart-header {
      height: 40px;

      .chart-title {
        font-size: $font-size-sm;
      }

      .chart-subtitle {
        font-size: $font-size-xs;
      }
    }

    .chart {
      height: calc(100% - 40px);
      min-height: 140px;
    }
  }
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
    max-height: 35%;
  }

  .stat-card {
    min-height: 50px;
    max-height: 60px;
    padding: $spacing-xs;

    .stat-icon {
      width: 40px;
      height: 40px;
      font-size: 16px;
    }

    .stat-content {
      .stat-value {
        font-size: $font-size-md;
      }

      .stat-label {
        font-size: $font-size-sm;
      }
    }
  }

  .chart-container {
    min-height: 160px;
    max-height: calc(65vh - 120px);

    .chart-header {
      height: 35px;

      .chart-title {
        font-size: $font-size-xs;
      }

      .chart-subtitle {
        display: none;
      }
    }

    .chart {
      height: calc(100% - 35px);
      min-height: 125px;
    }
  }
}
</style>
