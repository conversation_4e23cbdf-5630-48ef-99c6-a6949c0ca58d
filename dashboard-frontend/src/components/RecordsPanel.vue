<template>
  <div class="records-panel">
    <div class="panel-header">
      <div class="header-left">
        <h2 class="section-title">
          <el-icon><List /></el-icon>
          实时进出记录
        </h2>
      </div>
      <div class="header-right">
        <div class="status-indicator">
          <span class="status-dot online"></span>
          <span class="status-text">实时数据</span>
        </div>
      </div>
    </div>
    
    <div class="records-container dashboard-card">
      <div class="records-header">
        <div class="header-item">姓名</div>
        <div class="header-item">学号</div>
        <div class="header-item">操作</div>
        <div class="header-item">时间</div>
        <div class="header-item">楼栋</div>
        <div class="header-item">设备</div>
        <div class="header-item">状态</div>
      </div>
      
      <PerfectScrollbar 
        class="records-body" 
        ref="recordsBodyRef"
        :options="{ suppressScrollX: true }"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <div 
          v-for="(record, index) in displayRecords" 
          :key="`${record.personCode}-${record.lastPassTimeStr}-${index}`"
          class="record-item"
          :class="{ 'highlight': record.isNew }"
        >
          <div class="record-cell name">
            <el-avatar :size="32" class="avatar">
              {{ record.personName.charAt(0) }}
            </el-avatar>
            <span>{{ record.personName }}</span>
          </div>
          
          <div class="record-cell code">
            {{ record.personCode }}
          </div>
          
          <div class="record-cell action">
            <el-tag 
              :type="record.lastInOrOut === 1 ? 'success' : 'warning'"
              :effect="record.lastInOrOut === 1 ? 'dark' : 'plain'"
              size="small"
            >
              <el-icon>
                <component :is="record.lastInOrOut === 1 ? 'ArrowDown' : 'ArrowUp'" />
              </el-icon>
              {{ record.lastInOrOutDesc }}
            </el-tag>
          </div>
          
          <div class="record-cell time">
            {{ formatTime(record.lastPassTimeStr) }}
          </div>

          <div class="record-cell building">
            <el-tag
              size="small"
              type="info"
              effect="plain"
              v-if="record.buildingName"
            >
              {{ record.buildingName }}
            </el-tag>
            <span v-else class="no-data-text">未知</span>
          </div>

          <div class="record-cell device">
            {{ record.lastDeviceName || record.lastAreaName || '未知设备' }}
          </div>

          <div class="record-cell status">
            <span
              class="status-badge"
              :class="record.isInDormitory ? 'in-dormitory' : 'out-dormitory'"
            >
              {{ record.dormitoryStatusDesc }}
            </span>
          </div>
        </div>
        
        <!-- 无数据提示 -->
        <div v-if="displayRecords.length === 0" class="no-data">
          <el-icon><DocumentRemove /></el-icon>
          <p>暂无进出记录</p>
        </div>
      </PerfectScrollbar>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { List, ArrowDown, ArrowUp, DocumentRemove } from '@element-plus/icons-vue'

const props = defineProps({
  records: {
    type: Array,
    default: () => []
  }
})

const recordsBodyRef = ref(null)
const maxDisplayRecords = 15 // 最多显示15条记录
const scrollSpeed = 3000 // 滚动速度（毫秒）

// 显示的记录（限制数量并添加动画标记）
const displayRecords = computed(() => {
  return props.records.slice(0, maxDisplayRecords).map((record, index) => ({
    ...record,
    isNew: index < 3 // 前3条标记为新记录
  }))
})

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '--'
  
  try {
    // 如果是完整的日期时间字符串
    if (timeStr.includes('T') || timeStr.includes(' ')) {
      const date = new Date(timeStr)
      return date.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
    
    // 如果只是时间字符串
    return timeStr
  } catch (error) {
    console.warn('时间格式化失败:', timeStr, error)
    return timeStr
  }
}

// 自动滚动
let scrollInterval = null

const startAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval)
  }
  
  scrollInterval = setInterval(() => {
    if (recordsBodyRef.value && displayRecords.value.length > 10) {
      const container = recordsBodyRef.value.$el || recordsBodyRef.value
      const scrollHeight = container.scrollHeight
      const clientHeight = container.clientHeight
      const currentScrollTop = container.scrollTop
      
      // 如果已经滚动到底部，回到顶部
      if (currentScrollTop + clientHeight >= scrollHeight - 10) {
        container.scrollTop = 0
      } else {
        // 否则向下滚动一个记录的高度
        container.scrollTop += 60 // 每条记录大约60px高度
      }
    }
  }, scrollSpeed)
}

const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval)
    scrollInterval = null
  }
}

onMounted(() => {
  // 延迟启动自动滚动，确保数据已加载
  setTimeout(() => {
    startAutoScroll()
  }, 2000)
})

onUnmounted(() => {
  stopAutoScroll()
})

// 监听鼠标悬停，暂停滚动
const handleMouseEnter = () => {
  stopAutoScroll()
}

const handleMouseLeave = () => {
  startAutoScroll()
}

// 暴露方法
defineExpose({
  startAutoScroll,
  stopAutoScroll
})
</script>

<style scoped lang="scss">
.records-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  width: 100%;
  min-height: 40px; // 确保有足够的高度
  
  .header-left {
    flex: 1;
    min-width: 0; // 允许内容收缩
    overflow: hidden; // 防止内容溢出
    
    .section-title {
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      white-space: nowrap; // 防止标题换行
      overflow: hidden; // 防止溢出
      text-overflow: ellipsis; // 长文本显示省略号
    }
  }
  
  .header-right {
    flex-shrink: 0; // 防止右侧内容被压缩
    margin-left: $spacing-md; // 确保与左侧有间距
    max-width: 150px; // 限制最大宽度
    
    .status-indicator {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      color: $text-secondary;
      font-size: $font-size-sm;
      white-space: nowrap; // 防止文字换行
      overflow: hidden; // 防止溢出
      
      .status-text {
        display: inline-block; // 确保文字正常显示
        overflow: hidden; // 防止文字溢出
        text-overflow: ellipsis; // 长文本显示省略号
        max-width: 100px; // 限制文字最大宽度
      }
    
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: $success-color;
        box-shadow: 0 0 6px $success-color;
        animation: pulse 2s ease-in-out infinite;
        flex-shrink: 0; // 防止圆点被压缩
      }
    }
  }
}

.records-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.records-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 1.5fr 1fr;
  gap: $spacing-md;
  padding: $spacing-md $spacing-lg;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid $border-color;
  font-weight: 600;
  color: $text-secondary;
  font-size: $font-size-sm;
  
  .header-item {
    text-align: center;
  }
}

.records-body {
  flex: 1;
  height: 100%;
  padding: $spacing-sm 0;
  
  // Perfect Scrollbar 会自动处理滚动
  // 移除原生滚动条相关样式
}

.record-item {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 1.5fr 1fr;
  gap: $spacing-md;
  padding: $spacing-md $spacing-lg;
  border-bottom: 1px solid $border-color-light;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.03);
  }
  
  &.highlight {
    background: rgba(64, 158, 255, 0.1);
    border-left: 3px solid $primary-color;
    animation: slideInRight 0.5s ease-out;
  }
  
  .record-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $font-size-sm;
    color: $text-primary;
    
    &.name {
      justify-content: flex-start;
      gap: $spacing-sm;
      
      .avatar {
        background: linear-gradient(135deg, $primary-color, #66B1FF);
        color: white;
        font-size: $font-size-xs;
        font-weight: 600;
      }
    }
    
    &.code {
      font-family: 'Courier New', monospace;
      color: $text-secondary;
    }
    
    &.time {
      font-family: 'Courier New', monospace;
      color: $text-secondary;
    }
    
    &.building {
      justify-content: center;

      .no-data-text {
        color: $text-muted;
        font-size: $font-size-xs;
      }
    }

    &.device {
      color: $text-secondary;
      font-size: $font-size-xs;
    }
  }
}

.status-badge {
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  
  &.in-dormitory {
    background: rgba(103, 194, 58, 0.2);
    color: $success-color;
    border: 1px solid rgba(103, 194, 58, 0.3);
  }
  
  &.out-dormitory {
    background: rgba(230, 162, 60, 0.2);
    color: $warning-color;
    border: 1px solid rgba(230, 162, 60, 0.3);
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: $text-muted;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: $spacing-md;
  }
  
  p {
    font-size: $font-size-lg;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 1200px) {
  .panel-header {
    flex-direction: row; // 保持水平布局
    align-items: center;
    gap: $spacing-sm;
    
    .header-left {
      flex: 1;
      min-width: 0;
      
      .section-title {
        font-size: $font-size-md; // 缩小字体
      }
    }
    
    .header-right {
      margin-left: $spacing-sm;
      max-width: 120px; // 进一步限制宽度
      
      .status-indicator {
        font-size: $font-size-xs; // 缩小字体
        
        .status-text {
          max-width: 80px; // 缩小文字宽度
        }
      }
    }
  }
  
  .records-header,
  .record-item {
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    
    .record-cell.device {
      display: none;
    }
    
    .header-item:nth-child(5) {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .panel-header {
    .header-right {
      .status-indicator {
        .status-text {
          display: none; // 在小屏幕上隐藏文字，只显示圆点
        }
      }
    }
  }
  
  .records-header,
  .record-item {
    grid-template-columns: 2fr 1fr 1fr 1fr;
    font-size: $font-size-xs;
    
    .record-cell.device,
    .record-cell.time {
      display: none;
    }
    
    .header-item:nth-child(4),
    .header-item:nth-child(5) {
      display: none;
    }
  }
}
</style>
