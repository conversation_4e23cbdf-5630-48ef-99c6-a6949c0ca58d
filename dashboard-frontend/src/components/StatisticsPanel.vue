<template>
  <div class="statistics-panel">
    <div class="panel-header">
      <h2 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        归寝情况统计
      </h2>
      <div class="update-time">
        <el-icon><Clock /></el-icon>
        更新时间: {{ updateTime }}
      </div>
    </div>
    
    <div class="statistics-grid">
      <!-- 总人数 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon total">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.totalPersons || 0 }}</div>
          <div class="stat-label">总人数</div>
        </div>
      </div>
      
      <!-- 在寝人数 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon in-dormitory">
          <el-icon><House /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.inDormitoryPersons || 0 }}</div>
          <div class="stat-label">在寝人数</div>
        </div>
      </div>
      
      <!-- 外出人数 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon out-dormitory">
          <el-icon><Position /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.outDormitoryPersons || 0 }}</div>
          <div class="stat-label">外出人数</div>
        </div>
      </div>
      
      <!-- 归寝率 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon return-rate">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ returnRate }}%</div>
          <div class="stat-label">归寝率</div>
        </div>
      </div>

      <!-- 无记录人员 -->
      <div class="stat-card dashboard-card">
        <div class="stat-icon no-record">
          <el-icon><QuestionFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.personsWithoutRecords || 0 }}</div>
          <div class="stat-label">无记录人员</div>
        </div>
      </div>
    </div>
    
    <!-- 图表展示 -->
    <div class="chart-container dashboard-card">
      <div class="chart-header">
        <h3 class="chart-title">
          <el-icon><PieChart /></el-icon>
          归寝情况分布
        </h3>
        <div class="chart-subtitle">实时数据可视化</div>
      </div>
      <div ref="chartRef" class="chart"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { DataAnalysis, Clock, User, House, Position, TrendCharts, QuestionFilled, PieChart } from '@element-plus/icons-vue'

const props = defineProps({
  statistics: {
    type: Object,
    default: () => ({})
  }
})

const chartRef = ref(null)
const mainChartRef = ref(null)
const trendChartRef = ref(null)
const gaugeChartRef = ref(null)
const updateTime = ref('')
let chart = null
let mainChart = null
let trendChart = null
let gaugeChart = null

// 计算归寝率

// 计算归寝率
const returnRate = computed(() => {
  const total = props.statistics.totalPersons || 0
  const inDormitory = props.statistics.inDormitoryPersons || 0
  return total > 0 ? ((inDormitory / total) * 100).toFixed(1) : '0.0'
})

// 更新时间
const updateCurrentTime = () => {
  updateTime.value = new Date().toLocaleString('zh-CN')
}

// 生成趋势数据 (模拟24小时数据)
const generateTrendData = () => {
  const hours = []
  const rates = []
  const currentHour = new Date().getHours()

  for (let i = 0; i < 24; i++) {
    const hour = (currentHour - 23 + i + 24) % 24
    hours.push(`${hour.toString().padStart(2, '0')}:00`)

    // 模拟归寝率数据，晚上时间归寝率较高
    let rate
    if (hour >= 22 || hour <= 6) {
      rate = 85 + Math.random() * 10 // 晚上和凌晨归寝率高
    } else if (hour >= 7 && hour <= 17) {
      rate = 20 + Math.random() * 30 // 白天归寝率低
    } else {
      rate = 50 + Math.random() * 35 // 傍晚逐渐增高
    }
    rates.push(Math.round(rate))
  }

  return { hours, rates }
}

// 更新图表数据
const updateChart = () => {
  if (!chart || !chartRef.value) return

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const total = props.statistics.totalPersons || 0;
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(1) : '0.0';
        return `${params.name}: ${params.value}人 (${percentage}%)`;
      }
    },
    series: [{
      name: '归寝情况',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: [
        {
          value: props.statistics.inDormitoryPersons || 0,
          name: '在寝人数',
          itemStyle: { color: '#67C23A' }
        },
        {
          value: props.statistics.outDormitoryPersons || 0,
          name: '外出人数',
          itemStyle: { color: '#E6A23C' }
        },
        {
          value: props.statistics.personsWithoutRecords || 0,
          name: '无记录人员',
          itemStyle: { color: '#909399' }
        }
      ]
    }]
  }

  chart.setOption(option, true)
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  updateChart()
}

// 初始化所有图表
const initCharts = () => {
  initChart()
  // 注意：由于模板中只有一个图表容器，其他图表功能暂时注释
  // initMainChart()
  // initTrendChart()
  // initGaugeChart()
}

// 以下函数暂时注释，因为模板中只有一个图表容器
// 初始化主饼图
// const initMainChart = () => {
//   if (!mainChartRef.value) return
//
//   mainChart = echarts.init(mainChartRef.value)
//   updateMainChart()
// }

// 初始化趋势图
// const initTrendChart = () => {
//   if (!trendChartRef.value) return
//
//   trendChart = echarts.init(trendChartRef.value)
//   updateTrendChart()
// }

// 初始化仪表盘图
// const initGaugeChart = () => {
//   if (!gaugeChartRef.value) return
//
//   gaugeChart = echarts.init(gaugeChartRef.value)
//   updateGaugeChart()
// }



// 更新所有图表
const updateCharts = () => {
  updateChart()
  // 注意：由于模板中只有一个图表容器，其他图表功能暂时注释
  // updateMainChart()
  // updateTrendChart()
  // updateGaugeChart()
}

// 以下函数暂时注释，因为模板中只有一个图表容器
// 更新主饼图数据
/*
const updateMainChart = () => {
  if (!mainChart || !mainChartRef.value) return

  // 获取容器尺寸
  const containerWidth = mainChartRef.value.clientWidth
  const containerHeight = mainChartRef.value.clientHeight
  
  // 根据容器尺寸动态调整图表配置
  const isSmallContainer = containerWidth < 400 || containerHeight < 200
  const legendFontSize = isSmallContainer ? 10 : 12
  const pieRadius = isSmallContainer ? ['20%', '50%'] : ['25%', '60%']
  const pieCenter = isSmallContainer ? ['40%', '50%'] : ['35%', '50%']
  
  const option = {
    backgroundColor: 'transparent',
    animation: true,
    animationDuration: 1500,
    animationEasing: 'cubicOut',
    animationDelay: function (idx) {
      return idx * 200;
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const total = props.statistics.totalPersons || 0;
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(1) : '0.0';
        return `
          <div style="padding: 8px;">
            <div style="font-size: 14px; font-weight: bold; margin-bottom: 6px;">
              ${params.seriesName}
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="font-weight: 500;">${params.name}</span>
            </div>
            <div style="margin-left: 18px; color: #409EFF; font-size: 16px; font-weight: bold;">
              ${params.value} 人 (${percentage}%)
            </div>
          </div>
        `;
      },
      backgroundColor: 'rgba(0, 0, 0, 0.85)',
      borderColor: '#409EFF',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#fff'
      },
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);'
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      textStyle: {
        color: '#fff',
        fontSize: legendFontSize,
        fontWeight: '500'
      },
      itemWidth: isSmallContainer ? 14 : 18,
      itemHeight: isSmallContainer ? 10 : 14,
      itemGap: isSmallContainer ? 10 : 15,
      icon: 'circle',
      formatter: function(name) {
        const data = [
          { name: '在寝人数', value: props.statistics.inDormitoryPersons || 0 },
          { name: '外出人数', value: props.statistics.outDormitoryPersons || 0 },
          { name: '无记录人员', value: props.statistics.personsWithoutRecords || 0 }
        ];
        const item = data.find(d => d.name === name);
        return item ? `${name}\n${item.value}人` : name;
      }
    },
    graphic: [
      {
        type: 'text',
        left: pieCenter[0],
        top: pieCenter[1],
        style: {
          text: `总人数\n${props.statistics.totalPersons || 0}`,
          textAlign: 'center',
          fill: '#fff',
          fontSize: isSmallContainer ? 14 : 18,
          fontWeight: 'bold',
          textShadowColor: 'rgba(0, 0, 0, 0.5)',
          textShadowBlur: 2
        }
      },
      {
        type: 'text',
        left: pieCenter[0],
        top: `${parseInt(pieCenter[1]) + (isSmallContainer ? 25 : 35)}%`,
        style: {
          text: `归寝率 ${returnRate.value}%`,
          textAlign: 'center',
          fill: '#409EFF',
          fontSize: isSmallContainer ? 12 : 16,
          fontWeight: 'bold',
          textShadowColor: 'rgba(0, 0, 0, 0.5)',
          textShadowBlur: 2
        }
      }
    ],
    series: [
      {
        name: '归寝情况',
        type: 'pie',
        radius: pieRadius,
        center: pieCenter,
        avoidLabelOverlap: true,
        roseType: 'area',
        itemStyle: {
          borderRadius: 12,
          borderColor: 'rgba(255, 255, 255, 0.8)',
          borderWidth: 2,
          shadowBlur: 15,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        label: {
          show: true,
          position: 'inside',
          fontSize: isSmallContainer ? 12 : 14,
          fontWeight: 'bold',
          color: '#fff',
          formatter: '{b}\n{c}人\n({d}%)',
          textStyle: {
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 2
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: isSmallContainer ? 16 : 20,
            fontWeight: 'bold',
            color: '#fff',
            formatter: '{b}\n{c}人\n({d}%)'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            borderWidth: 3,
            borderColor: '#fff'
          },
          scale: true,
          scaleSize: 10
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: props.statistics.inDormitoryPersons || 0,
            name: '在寝人数',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#67C23A' },
                  { offset: 1, color: '#85CE61' }
                ]
              }
            }
          },
          {
            value: props.statistics.outDormitoryPersons || 0,
            name: '外出人数',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#E6A23C' },
                  { offset: 1, color: '#EEBC6C' }
                ]
              }
            }
          },
          {
            value: props.statistics.personsWithoutRecords || 0,
            name: '无记录人员',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#909399' },
                  { offset: 1, color: '#B1B3B8' }
                ]
              }
            }
          }
        ]
      },
      {
        name: '归寝率指示器',
        type: 'pie',
        radius: [isSmallContainer ? '65%' : '75%', isSmallContainer ? '70%' : '80%'],
        center: pieCenter,
        silent: true,
        label: {
          show: false
        },
        data: [
          {
            value: parseFloat(returnRate.value),
            name: '已归寝',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 1, y2: 1,
                colorStops: [
                  { offset: 0, color: '#409EFF' },
                  { offset: 1, color: '#66B1FF' }
                ]
              }
            }
          },
          {
            value: 100 - parseFloat(returnRate.value),
            name: '未归寝',
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        ]
      }
    ]
  }
  
  mainChart.setOption(option, true)
}
*/

// 更新趋势图
/*
const updateTrendChart = () => {
  if (!trendChart || !trendChartRef.value) return
  
  const { hours, rates } = generateTrendData()
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '8%',
      right: '5%',
      bottom: '12%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: {
        lineStyle: { color: 'rgba(255, 255, 255, 0.6)' }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10,
        interval: 2
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLine: {
        lineStyle: { color: 'rgba(255, 255, 255, 0.6)' }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10,
        formatter: '{value}%'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#409EFF',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        return `
          <div style="padding: 6px;">
            <div style="font-weight: bold;">${params[0].name}</div>
            <div>归寝率: ${params[0].value}%</div>
          </div>
        `
      }
    },
    series: [{
      data: rates,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.6)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ]
        }
      },
      lineStyle: {
        color: '#409EFF',
        width: 2,
        shadowColor: 'rgba(64, 158, 255, 0.5)',
        shadowBlur: 10
      },
      itemStyle: {
        color: '#409EFF',
        borderColor: '#fff',
        borderWidth: 1
      },
      animation: true,
      animationDuration: 2000,
      animationEasing: 'cubicOut'
    }]
  }
  
  trendChart.setOption(option, true)
}
*/

// 更新仪表盘图
/*
const updateGaugeChart = () => {
  if (!gaugeChart || !gaugeChartRef.value) return
  
  const rate = parseFloat(returnRate.value)
  
  const option = {
    backgroundColor: 'transparent',
    series: [
      {
        name: '归寝率',
        type: 'gauge',
        center: ['50%', '60%'],
        radius: '80%',
        min: 0,
        max: 100,
        splitNumber: 5,
        progress: {
          show: true,
          width: 15,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 0,
              colorStops: [
                { offset: 0, color: '#F56C6C' },
                { offset: 0.5, color: '#E6A23C' },
                { offset: 1, color: '#67C23A' }
              ]
            },
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 10
          }
        },
        axisLine: {
          lineStyle: {
            width: 15,
            color: [[1, 'rgba(255, 255, 255, 0.1)']]
          }
        },
        axisTick: {
          distance: -30,
          splitNumber: 5,
          lineStyle: {
            width: 2,
            color: 'rgba(255, 255, 255, 0.5)'
          }
        },
        splitLine: {
          distance: -35,
          length: 15,
          lineStyle: {
            width: 3,
            color: 'rgba(255, 255, 255, 0.6)'
          }
        },
        axisLabel: {
          distance: -50,
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10,
          formatter: '{value}%'
        },
        anchor: {
          show: true,
          showAbove: true,
          size: 20,
          itemStyle: {
            borderWidth: 2,
            borderColor: '#409EFF',
            color: '#fff'
          }
        },
        pointer: {
          itemStyle: {
            color: '#409EFF',
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 5
          }
        },
        detail: {
          valueAnimation: true,
          width: '60%',
          lineHeight: 40,
          borderRadius: 8,
          offsetCenter: [0, '-15%'],
          fontSize: 20,
          fontWeight: 'bold',
          formatter: '{value}%',
          color: '#409EFF',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderColor: 'rgba(255, 255, 255, 0.3)',
          borderWidth: 1
        },
        data: [
          {
            value: rate,
            name: '归寝率'
          }
        ],
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicOut'
      }
    ]
  }
  
  gaugeChart.setOption(option, true)
}
*/

// 监听统计数据变化
const updateStatistics = () => {
  nextTick(() => {
    updateCharts()
  })
}

onMounted(() => {
  updateCurrentTime()
  // 每秒更新时间
  const timeInterval = setInterval(updateCurrentTime, 1000)
  
  nextTick(() => {
    initCharts()
  })
  
  // 监听窗口大小变化
  const resizeHandler = () => {
    setTimeout(() => {
      if (chart) {
        chart.resize()
        updateChart()
      }
      // 其他图表暂时注释，因为模板中只有一个图表容器
      // if (mainChart) {
      //   mainChart.resize()
      //   updateMainChart()
      // }
      // if (trendChart) {
      //   trendChart.resize()
      //   updateTrendChart()
      // }
      // if (gaugeChart) {
      //   gaugeChart.resize()
      //   updateGaugeChart()
      // }
    }, 100)
  }
  window.addEventListener('resize', resizeHandler)
  
  // 使用ResizeObserver监听容器尺寸变化
  if (chartRef.value && window.ResizeObserver) {
    const resizeObserver = new ResizeObserver(() => {
      if (chart) {
        chart.resize()
        updateChart()
      }
    })
    resizeObserver.observe(chartRef.value)

    onUnmounted(() => {
      resizeObserver.disconnect()
    })
  }

  // 其他图表的ResizeObserver暂时注释
  // const containers = [mainChartRef.value, trendChartRef.value, gaugeChartRef.value]
  // const charts = [mainChart, trendChart, gaugeChart]
  // const updateFunctions = [updateMainChart, updateTrendChart, updateGaugeChart]
  
  // 监听统计数据变化，重新渲染图表
  const updateHandler = () => {
    nextTick(() => {
      updateCharts()
    })
  }
  
  onUnmounted(() => {
    clearInterval(timeInterval)
    window.removeEventListener('resize', resizeHandler)
    if (chart) chart.dispose()
    // 其他图表的清理暂时注释
    // if (mainChart) mainChart.dispose()
    // if (trendChart) trendChart.dispose()
    // if (gaugeChart) gaugeChart.dispose()
  })
})

// 暴露更新方法
defineExpose({
  updateStatistics
})
</script>

<style scoped lang="scss">
.statistics-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 100vh; // 确保不超出视口高度
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md; // 减小间距
  flex-shrink: 0; // 防止头部被压缩
  
  .section-title {
    margin: 0;
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }
  
  .update-time {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    color: $text-secondary;
    font-size: $font-size-sm;
  }
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: $spacing-sm;
  margin-bottom: $spacing-sm; // 进一步减小间距
  flex-shrink: 0;
  height: auto; // 自适应高度
  max-height: 25%; // 限制最大高度为父容器的25%
}

.stat-card {
  padding: $spacing-xs $spacing-sm; // 进一步减小内边距
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  min-height: 60px; // 进一步减小高度
  max-height: 80px; // 限制最大高度
  
  .stat-icon {
    width: 50px; // 减小图标尺寸
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px; // 减小字体大小
    flex-shrink: 0; // 防止图标被压缩
    
    &.total {
      background: linear-gradient(135deg, #409EFF, #66B1FF);
      color: white;
    }
    
    &.in-dormitory {
      background: linear-gradient(135deg, #67C23A, #85CE61);
      color: white;
    }
    
    &.out-dormitory {
      background: linear-gradient(135deg, #E6A23C, #EEBC6C);
      color: white;
    }
    
    &.return-rate {
      background: linear-gradient(135deg, #F56C6C, #F89898);
      color: white;
    }

    &.no-record {
      background: linear-gradient(135deg, #909399, #B1B3B8);
      color: white;
    }
  }
  
  .stat-content {
    flex: 1;
    
    .stat-value {
      font-size: $font-size-display;
      font-weight: 700;
      color: $text-primary;
      line-height: 1;
      margin-bottom: $spacing-xs;
    }
    
    .stat-label {
      font-size: $font-size-lg;
      color: $text-secondary;
      font-weight: 500;
    }
  }
}

// 移除复杂的图表布局，使用单一图表容器

.chart-container {
  flex: 1; // 占用剩余空间
  padding: $spacing-sm;
  min-height: 200px;
  max-height: calc(75vh - 200px); // 限制最大高度，预留给统计卡片和标题的空间
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(103, 194, 58, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
    pointer-events: none;
  }

  .chart-header {
    text-align: center;
    margin-bottom: $spacing-xs;
    position: relative;
    z-index: 2;
    flex-shrink: 0;
    height: 50px; // 固定标题区域高度
    display: flex;
    flex-direction: column;
    justify-content: center;

    .chart-title {
      margin: 0;
      font-size: $font-size-md;
      font-weight: 600;
      color: $text-primary;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-xs;
      line-height: 1.2;
    }

    .chart-subtitle {
      font-size: $font-size-sm;
      color: $text-secondary;
      opacity: 0.8;
      margin-top: 2px;
      line-height: 1;
    }
  }

  .chart {
    flex: 1;
    width: 100%;
    height: calc(100% - 50px); // 减去标题高度
    min-height: 150px;
    max-height: calc(75vh - 250px); // 确保不超出容器
    position: relative;
    z-index: 1;
  }
}

@media (max-width: 1200px) {
  .statistics-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: $spacing-sm;
  }

  .stat-card {
    min-height: 70px;
    padding: $spacing-sm;
    
    .stat-icon {
      width: 50px;
      height: 50px;
      font-size: 20px;
    }
    
    .stat-content {
      .stat-value {
        font-size: $font-size-xl;
      }
    }
  }
  
  .chart-container {
    min-height: 180px;
    max-height: calc(70vh - 150px);

    .chart-header {
      height: 40px;

      .chart-title {
        font-size: $font-size-sm;
      }

      .chart-subtitle {
        font-size: $font-size-xs;
      }
    }

    .chart {
      height: calc(100% - 40px);
      min-height: 140px;
    }
  }
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
    max-height: 35%;
  }

  .stat-card {
    min-height: 50px;
    max-height: 60px;
    padding: $spacing-xs;

    .stat-icon {
      width: 40px;
      height: 40px;
      font-size: 16px;
    }

    .stat-content {
      .stat-value {
        font-size: $font-size-md;
      }

      .stat-label {
        font-size: $font-size-sm;
      }
    }
  }

  .chart-container {
    min-height: 160px;
    max-height: calc(65vh - 120px);

    .chart-header {
      height: 35px;

      .chart-title {
        font-size: $font-size-xs;
      }

      .chart-subtitle {
        display: none;
      }
    }

    .chart {
      height: calc(100% - 35px);
      min-height: 125px;
    }
  }
}
</style>
