import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { PerfectScrollbarPlugin } from 'vue3-perfect-scrollbar'
import 'vue3-perfect-scrollbar/style.css'
import App from './App.vue'
import router from './router'
import './styles/global.scss'

const app = createApp(App)

// 注册Element Plus
app.use(ElementPlus)

// 注册Vue Router
app.use(router)

// 注册Perfect Scrollbar
app.use(PerfectScrollbarPlugin, {
  componentName: 'PerfectScrollbar'
})

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
