@use './variables.scss' as *;

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, $bg-primary 0%, $bg-secondary 100%);
  color: $text-primary;
  overflow: hidden;
}

// 卡片样式
.dashboard-card {
  background: $bg-card;
  border: 1px solid $border-color;
  border-radius: $border-radius-lg;
  backdrop-filter: blur(10px);
  box-shadow: $shadow-medium;
  transition: all 0.3s ease;
  
  &:hover {
    background: $bg-card-hover;
    box-shadow: $shadow-heavy;
  }
}

// 标题样式
.dashboard-title {
  font-size: $font-size-title;
  font-weight: 600;
  color: $text-primary;
  text-align: center;
  margin-bottom: $spacing-lg;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-title {
  font-size: $font-size-xl;
  font-weight: 500;
  color: $text-primary;
  margin-bottom: $spacing-md;
  padding-left: $spacing-md;
  border-left: 4px solid $primary-color;
}

// 数据展示样式
.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-lg;
  
  .data-value {
    font-size: $font-size-display;
    font-weight: 700;
    color: $primary-color;
    margin-bottom: $spacing-sm;
    text-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }
  
  .data-label {
    font-size: $font-size-lg;
    color: $text-secondary;
    font-weight: 500;
  }
}

// 状态指示器
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: $spacing-sm;
  
  &.online {
    background-color: $success-color;
    box-shadow: 0 0 6px $success-color;
  }
  
  &.offline {
    background-color: $danger-color;
    box-shadow: 0 0 6px $danger-color;
  }
}

// Perfect Scrollbar 统一样式配置
.ps {
  position: relative;
  overflow: hidden;
  
  &__rail-y {
    width: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    right: 2px;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }
  
  &__thumb-y {
    background-color: rgba(64, 158, 255, 0.6);
    border-radius: 4px;
    width: 6px;
    right: 1px;
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.8);
    }
  }
  
  &__rail-x {
    height: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    bottom: 2px;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }
  
  &__thumb-x {
    background-color: rgba(64, 158, 255, 0.6);
    border-radius: 4px;
    height: 6px;
    bottom: 1px;
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.8);
    }
  }
}

// 滚动动画
@keyframes scroll-up {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

.scroll-item {
  animation: scroll-up 8s linear infinite;
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

// 渐变文字
.gradient-text {
  background: linear-gradient(45deg, $primary-color, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
