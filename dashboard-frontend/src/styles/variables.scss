// 颜色变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 背景色
$bg-primary: #0c1426;
$bg-secondary: #1a2332;
$bg-card: rgba(255, 255, 255, 0.05);
$bg-card-hover: rgba(255, 255, 255, 0.08);

// 文字颜色
$text-primary: #ffffff;
$text-secondary: rgba(255, 255, 255, 0.8);
$text-muted: rgba(255, 255, 255, 0.6);

// 边框颜色
$border-color: rgba(255, 255, 255, 0.1);
$border-color-light: rgba(255, 255, 255, 0.05);

// 阴影
$shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$shadow-medium: 0 4px 20px 0 rgba(0, 0, 0, 0.2);
$shadow-heavy: 0 8px 30px 0 rgba(0, 0, 0, 0.3);

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 圆角
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-title: 32px;
$font-size-display: 48px;
