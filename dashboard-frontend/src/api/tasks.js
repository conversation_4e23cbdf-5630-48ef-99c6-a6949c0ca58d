import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 与dormitory.js保持一致
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('任务管理API请求:', config.url, config.params)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('任务管理API响应:', response.data)
    return response.data
  },
  error => {
    console.error('任务管理API响应错误:', error)
    console.error('请求URL:', error.config?.url)
    console.error('响应状态:', error.response?.status)
    console.error('响应数据:', error.response?.data)

    // 如果是404错误，提供更详细的信息
    if (error.response?.status === 404) {
      console.error('404错误 - 可能的原因:')
      console.error('1. 后端服务未启动')
      console.error('2. 后端服务端口不是8080')
      console.error('3. API路径不匹配')
      console.error('4. Controller方法不存在')
    }

    return Promise.reject(error)
  }
)



/**
 * 定时任务管理API
 */
export const taskAPI = {
  // 获取任务仪表板数据
  getDashboard() {
    return api.get('/task-management/dashboard')
  },

  // 获取任务列表（分页）
  getTasks(page = 0, size = 20) {
    return api.get('/task-management/tasks', {
      params: { page, size }
    })
  },

  // 获取任务详细信息
  getTaskDetail(taskName) {
    return api.get(`/task-management/tasks/${taskName}`)
  },

  // 切换任务启用状态
  toggleTask(taskName) {
    return api.post(`/task-management/tasks/${taskName}/toggle`)
  },

  // 手动执行任务
  executeTask(taskName) {
    return api.post(`/task-management/tasks/${taskName}/execute`)
  },

  // 获取任务执行日志
  getTaskLogs(taskName = null, page = 0, size = 20) {
    if (taskName) {
      // 获取特定任务的日志
      return api.get(`/task-management/tasks/${taskName}/logs`, {
        params: { page, size }
      })
    } else {
      // 获取所有任务的日志，使用我们新添加的API
      return api.get('/task-management/logs', {
        params: { page, size }
      })
    }
  },

  // 获取最近执行日志
  getRecentLogs(limit = 10) {
    return api.get('/task-management/logs/recent', {
      params: { limit }
    })
  },

  // 更新任务配置
  updateTaskConfig(taskName, config) {
    return api.put(`/task-management/tasks/${taskName}/config`, config)
  },

  // 获取任务统计信息
  getTaskStats(taskName, days = 7) {
    return api.get(`/task-management/tasks/${taskName}/stats`, {
      params: { days }
    })
  },

  // 导出任务执行报告
  exportTaskReport(taskName, startDate, endDate, format = 'excel') {
    return api.get(`/task-management/tasks/${taskName}/export`, {
      params: { startDate, endDate, format },
      responseType: 'blob'
    })
  },

  // 批量操作任务
  batchOperation(operation, taskNames) {
    return api.post('/task-management/tasks/batch', {
      operation, // 'enable', 'disable', 'execute'
      taskNames
    })
  },

  // 获取任务执行趋势
  getTaskTrends(days = 30) {
    return api.get('/task-management/trends', {
      params: { days }
    })
  },

  // 获取系统资源使用情况
  getSystemResources() {
    return api.get('/task-management/system/resources')
  },

  // 获取任务告警信息
  getTaskAlerts() {
    return api.get('/task-management/alerts')
  },

  // 清除任务告警
  clearTaskAlert(alertId) {
    return api.delete(`/task-management/alerts/${alertId}`)
  },

  // 获取任务依赖关系
  getTaskDependencies() {
    return api.get('/task-management/dependencies')
  },

  // 设置任务依赖
  setTaskDependency(taskName, dependsOn) {
    return api.post(`/task-management/tasks/${taskName}/dependency`, {
      dependsOn
    })
  }
}

export default taskAPI
