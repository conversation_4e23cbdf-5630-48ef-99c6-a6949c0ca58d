<template>
  <div class="dashboard-app">
    <!-- 头部标题 -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="main-title gradient-text">
            <el-icon><Monitor /></el-icon>
            宿舍归寝情况大屏展示系统
          </h1>
          <div class="subtitle">
            {{ currentDate }} {{ currentTime }}
          </div>
        </div>
        
        <div class="status-section">
          <div class="connection-status">
            <span class="status-indicator" :class="connectionStatus"></span>
            {{ connectionStatus === 'online' ? '系统正常' : '连接异常' }}
          </div>

          <div class="websocket-status">
            <span class="status-indicator" :class="websocketStatus"></span>
            实时推送: {{ getWebSocketStatusText() }}
          </div>
          
          <!-- 优化后的楼栋选择器 -->
          <div class="building-selector-optimized">
            <el-select
              v-model="selectedBuildingCode"
              placeholder="选择楼栋"
              size="large"
              :loading="buildingsLoading"
              @change="onBuildingChange"
              clearable
              style="width: 200px;"
            >
              <el-option
                key="all"
                label="全部楼栋"
                value=""
              />
              <el-option
                v-for="building in buildings"
                :key="building.buildingCode"
                :label="building.buildingName"
                :value="building.buildingCode"
              />
            </el-select>
          </div>
          
          <!-- 精简的操作按钮 -->
          <div class="action-buttons">
            <el-button
              type="primary"
              :icon="Refresh"
              @click="forceRefreshData"
              size="large"
              :loading="loading"
              circle
              title="刷新数据"
            />

            <el-button
              type="info"
              :icon="User"
              @click="goToStudentStatus"
              size="large"
              circle
              title="学生状态管理"
            />
          </div>
        </div>
      </div>
    </header>
    
    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <!-- 统计面板 -->
      <section class="statistics-section">
        <StatisticsPanel 
          ref="statisticsPanelRef"
          :statistics="statistics" 
        />
      </section>
      
      <!-- 记录面板 -->
      <section class="records-section">
        <RecordsPanel 
          ref="recordsPanelRef"
          :records="allRecords" 
        />
      </section>
    </main>
    
    <!-- 底部信息 -->
    <footer class="dashboard-footer">
      <div class="footer-content">
        <div class="system-info">
          <span>最后更新: {{ lastUpdateTime }}</span>
        </div>
        <div class="copyright">
          © 2025 宿舍管理系统 - 实时数据展示
        </div>
      </div>
    </footer>
    
    <!-- 加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>正在加载数据...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Monitor, Loading, User, Refresh } from '@element-plus/icons-vue'
import StatisticsPanel from '../components/StatisticsPanel.vue'
import RecordsPanel from '../components/RecordsPanel.vue'
import { dormitoryAPI } from '../api/dormitory.js'

const router = useRouter()

// 响应式数据
const loading = ref(true)
const connectionStatus = ref('online')
const currentDate = ref('')
const currentTime = ref('')
const lastUpdateTime = ref('')
const refreshInterval = 30000 // 30秒刷新一次

// 楼栋选择相关
const buildings = ref([])
const selectedBuildingCode = ref('') // 改为单选
const buildingsLoading = ref(false)

// 统计数据
const statistics = reactive({
  totalPersons: 0,
  inDormitoryPersons: 0,
  outDormitoryPersons: 0,
  personsWithoutRecords: 0,
  returnRate: 0,
  date: ''
})

// 记录数据
const allRecords = ref([])
const maxRecordsCount = 50 // 最大显示记录数

// 组件引用
const statisticsPanelRef = ref(null)
const recordsPanelRef = ref(null)

// WebSocket相关
let websocket = null
const websocketUrl = ref('')
const websocketStatus = ref('disconnected') // disconnected, connecting, connected, error

// 定时器
let timeTimer = null

// 导航到学生状态页面
const goToStudentStatus = () => {
  router.push('/students')
}

// 导航到寝室管理页面
const goToDormitoryManagement = () => {
  router.push('/dormitories')
}

// 获取WebSocket状态文本
const getWebSocketStatusText = () => {
  const statusMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'error': '连接错误'
  }
  return statusMap[websocketStatus.value] || '未知状态'
}

// 加载楼栋列表
const loadBuildings = async () => {
  try {
    buildingsLoading.value = true
    console.log('正在加载大屏楼栋列表...')
    const response = await dormitoryAPI.getDashboardBuildings()

    if (response.success && response.data) {
      buildings.value = response.data
      console.log('大屏楼栋列表加载成功:', buildings.value.length, '个楼栋')
    } else {
      console.warn('大屏楼栋列表响应异常:', response)
      buildings.value = []
    }
  } catch (error) {
    console.error('加载大屏楼栋列表失败:', error)
    buildings.value = []
  } finally {
    buildingsLoading.value = false
  }
}

// 楼栋选择变化处理（优化版本：自动查询）
const onBuildingChange = async (buildingCode) => {
  console.log('楼栋选择变化:', buildingCode)
  selectedBuildingCode.value = buildingCode || ''

  // 自动查询对应楼栋的数据
  if (buildingCode) {
    // 查询指定楼栋
    await queryDashboardDataUnified([buildingCode], false)
    const selectedBuilding = buildings.value.find(b => b.buildingCode === buildingCode)
    ElMessage.success(`已切换到 ${selectedBuilding?.buildingName || buildingCode}`)
  } else {
    // 查询全部楼栋
    await queryDashboardDataUnified(null, false)
    ElMessage.success('已切换到全部楼栋')
  }
}

// 强制刷新数据（保留现有功能）
const forceRefreshData = async () => {
  const buildingCodes = selectedBuildingCode.value ? [selectedBuildingCode.value] : null
  const buildingName = selectedBuildingCode.value ? 
    buildings.value.find(b => b.buildingCode === selectedBuildingCode.value)?.buildingName || selectedBuildingCode.value :
    '全部楼栋'
  
  const message = `正在强制刷新${buildingName}数据...`
  
  console.log(message)
  ElMessage.info(message)

  // 使用统一接口进行强制刷新
  await queryDashboardDataUnified(buildingCodes, true)
}
const queryDashboardDataUnified = async (buildingCodes, forceRefresh) => {
  try {
    loading.value = true
    console.log('调用统一查询接口:', { buildingCodes, forceRefresh })

    const response = await dormitoryAPI.queryDashboardData(buildingCodes, forceRefresh)

    if (response.success && response.data) {
      // 更新统计数据
      Object.assign(statistics, response.data)

      // 优化：不再查询记录数据，记录通过WebSocket实时推送
      // 记录数据由WebSocket维护，这里不做处理

      lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
      connectionStatus.value = 'online'

      console.log('统一查询成功:', {
        buildingCodes,
        totalPersons: response.data.totalPersons,
        queryMethod: response.data.queryMethod,
        dataSource: response.data.dataSource,
        queryDuration: response.data.queryDuration + 'ms'
      })

      // 显示成功消息
      const message = buildingCodes && buildingCodes.length > 0
        ? `查询成功：${buildingCodes.length}个楼栋，共${response.data.totalPersons}人`
        : `查询成功：全部楼栋，共${response.data.totalPersons}人`
      ElMessage.success(message)

    } else {
      console.warn('统一查询响应异常:', response)
      connectionStatus.value = 'offline'
      ElMessage.error('查询失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('统一查询失败:', error)
    connectionStatus.value = 'offline'
    ElMessage.error('网络错误，查询失败')
  } finally {
    loading.value = false
  }
}

// WebSocket连接管理
const initWebSocket = () => {
  // 构建WebSocket URL - 开发环境连接到后端8080端口
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const hostname = window.location.hostname
  // 开发环境：前端3000端口，后端8080端口
  // 生产环境：可能是同一端口或通过代理
  const port = window.location.port === '3000' ? '8080' : window.location.port
  websocketUrl.value = `${protocol}//${hostname}:${port}/ws/dashboard/records`

  console.log('初始化WebSocket连接:', websocketUrl.value)
  connectWebSocket()
}

const connectWebSocket = () => {
  try {
    websocketStatus.value = 'connecting'
    websocket = new WebSocket(websocketUrl.value)

    websocket.onopen = () => {
      console.log('WebSocket连接成功')
      websocketStatus.value = 'connected'
      connectionStatus.value = 'online'
    }

    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('收到WebSocket消息:', data)
        handleWebSocketMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    websocket.onclose = (event) => {
      console.log('WebSocket连接关闭:', event.code, event.reason)
      websocketStatus.value = 'disconnected'

      // 自动重连（5秒后）
      setTimeout(() => {
        if (websocketStatus.value === 'disconnected') {
          console.log('尝试重新连接WebSocket...')
          connectWebSocket()
        }
      }, 5000)
    }

    websocket.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      websocketStatus.value = 'error'
    }

  } catch (error) {
    console.error('创建WebSocket连接失败:', error)
    websocketStatus.value = 'error'
  }
}

const handleWebSocketMessage = (data) => {
  if (data.type === 'new_record' && data.record) {
    // 收到新的进出记录
    addNewRecord(data.record)
  } else if (data.type === 'batch_records' && data.records) {
    // 收到批量记录（初始化时）
    setBatchRecords(data.records)
  } else if (data.type === 'ping') {
    // 心跳消息
    console.log('收到WebSocket心跳')
  }
}

const addNewRecord = (newRecord) => {
  console.log('收到新的进出记录:', newRecord)

  // 楼栋筛选：如果选择了特定楼栋，只显示对应楼栋的记录
  if (selectedBuildingCode.value) {
    const recordBuildingName = newRecord.buildingName
    const selectedBuilding = buildings.value.find(b => b.buildingCode === selectedBuildingCode.value)
    const selectedBuildingName = selectedBuilding?.buildingName
    
    if (recordBuildingName !== selectedBuildingName) {
      console.log('记录不属于选中楼栋，跳过显示:', recordBuildingName, '当前选中:', selectedBuildingName)
      return
    }
  }

  // 添加到记录列表开头
  allRecords.value.unshift(newRecord)

  // 限制记录数量
  if (allRecords.value.length > maxRecordsCount) {
    allRecords.value = allRecords.value.slice(0, maxRecordsCount)
  }

  // 显示通知（包含楼栋信息）
  const buildingInfo = newRecord.buildingName ? ` (${newRecord.buildingName})` : ''
  ElMessage.success(`新进出记录: ${newRecord.personName} ${newRecord.lastInOrOutDesc}${buildingInfo}`)
}

const setBatchRecords = (records) => {
  console.log('设置批量记录:', records.length, '条')
  allRecords.value = records.slice(0, maxRecordsCount)
}

const disconnectWebSocket = () => {
  if (websocket) {
    websocket.close()
    websocket = null
    websocketStatus.value = 'disconnected'
  }
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false
  })
}

// 加载实时数据（统计信息+最新记录）- 基于Redis缓存的高性能接口
const loadRealtimeData = async () => {
  try {
    console.log('正在加载实时数据（基于Redis缓存）...', { selectedBuildingCode: selectedBuildingCode.value })

    // 如果选择了楼栋，使用分别加载的方式（因为实时接口暂不支持楼栋筛选）
    if (selectedBuildingCode.value) {
      return false // 强制使用分别加载方式
    }
    
    const response = await dormitoryAPI.getRealtimeData(30)
    
    if (response.success && response.data) {
      // 更新统计数据
      Object.assign(statistics, response.data.statistics)
      
      // 更新记录数据
      allRecords.value = response.data.records || []
      
      console.log('实时数据加载成功:', {
        statistics: response.data.statistics,
        recordCount: response.data.recordCount,
        queryMethod: response.data.statistics?.queryMethod,
        dataSource: response.data.dataSource,
        queryDuration: response.data.queryDuration + 'ms'
      })
      
      connectionStatus.value = 'online'
      return true
    } else {
      console.warn('实时数据响应异常:', response)
      connectionStatus.value = 'offline'
      return false
    }
  } catch (error) {
    console.error('加载实时数据失败:', error)
    connectionStatus.value = 'offline'
    return false
  }
}

// 加载统计数据（独立接口，支持楼栋筛选）
const loadStatistics = async () => {
  try {
    console.log('正在加载统计数据（基于Redis缓存）...', { selectedBuildingCode: selectedBuildingCode.value })

    // 根据是否选择楼栋决定调用哪个接口
    const response = selectedBuildingCode.value
      ? await dormitoryAPI.getStatisticsByBuildings([selectedBuildingCode.value])
      : await dormitoryAPI.getStatistics()
    
    if (response.success && response.data) {
      Object.assign(statistics, response.data)
      console.log('统计数据加载成功:', statistics)
      connectionStatus.value = 'online'
    } else {
      console.warn('统计数据响应异常:', response)
      connectionStatus.value = 'offline'
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    connectionStatus.value = 'offline'
  }
}

// 加载进出记录（独立接口）
const loadRecords = async () => {
  try {
    console.log('正在加载进出记录（基于Redis缓存）...')
    const response = await dormitoryAPI.getRecords(30)

    if (response.success && response.data) {
      allRecords.value = response.data
      console.log('进出记录加载成功，共', response.data.length, '条, 数据源:', response.dataSource)
    } else {
      console.warn('进出记录响应异常:', response)
    }

  } catch (error) {
    console.error('加载进出记录失败:', error)
  }
}

// 加载所有数据（基于Redis缓存）
const loadAllData = async () => {
  try {
    loading.value = true
    
    // 优先使用高性能实时接口（一次请求获取所有数据）
    const realtimeSuccess = await loadRealtimeData()
    
    // 如果实时接口失败，分别加载统计和记录数据
    if (!realtimeSuccess) {
      console.warn('实时接口失败，分别加载统计和记录数据')
      await Promise.all([
        loadStatistics(),
        loadRecords()
      ])
    }
    
    lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
    console.log('所有数据加载完成（基于Redis缓存）')
    
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始加载数据（优化版本：使用统一接口 + WebSocket）
const initializeData = async () => {
  // 先加载楼栋列表
  await loadBuildings()

  // 然后使用统一接口加载统计数据（不加载记录）
  await queryDashboardDataUnified(null, false)

  // 初始化WebSocket连接，用于接收实时进出记录
  initWebSocket()

  console.log('数据初始化完成（统计数据 + WebSocket实时记录）')
}

// 启动时间更新
const startTimeUpdate = () => {
  updateCurrentTime()
  timeTimer = setInterval(updateCurrentTime, 1000)
}

// 停止时间更新
const stopTimeUpdate = () => {
  if (timeTimer) {
    clearInterval(timeTimer)
    timeTimer = null
  }
}

// 组件挂载
onMounted(() => {
  console.log('大屏系统启动...')
  
  // 启动时间更新
  startTimeUpdate()
  
  // 初始化数据（移除自动刷新）
  initializeData()
  
  // 清理函数
  onUnmounted(() => {
    stopTimeUpdate()
    disconnectWebSocket()
    console.log('大屏系统已停止')
  })
})
</script>

<style scoped lang="scss">
.dashboard-app {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, $bg-primary 0%, $bg-secondary 100%);
  overflow: hidden;
}

.dashboard-header {
  padding: $spacing-md $spacing-lg; /* 减小头部高度 */
  border-bottom: 1px solid $border-color;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .title-section {
    .main-title {
      font-size: $font-size-title;
      font-weight: 700;
      margin: 0;
      display: flex;
      align-items: center;
      gap: $spacing-md;
      text-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }
    
    .subtitle {
      font-size: $font-size-lg;
      color: $text-secondary;
      margin-top: $spacing-sm;
      font-weight: 400;
    }
  }
  
  .status-section {
    display: flex;
    align-items: center;
    gap: $spacing-lg;
    text-align: right;
    flex-wrap: wrap;
    
    .connection-status,
    .websocket-status {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      font-size: $font-size-md;
      color: $text-secondary;

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;

        &.online,
        &.connected {
          background-color: $success-color;
          box-shadow: 0 0 8px $success-color;
        }

        &.offline,
        &.disconnected {
          background-color: $danger-color;
          box-shadow: 0 0 8px $danger-color;
        }

        &.connecting {
          background-color: $warning-color;
          box-shadow: 0 0 8px $warning-color;
          animation: pulse 1.5s infinite;
        }

        &.error {
          background-color: $danger-color;
          box-shadow: 0 0 8px $danger-color;
          animation: blink 1s infinite;
        }
      }
    }

    .websocket-status {
      font-size: $font-size-sm;
    }
    
    .building-selector-optimized {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      :deep(.el-select) {
        .el-input {
          .el-input__wrapper {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: $text-primary;

            &:hover {
              border-color: $primary-color;
            }

            &.is-focus {
              border-color: $primary-color;
              box-shadow: 0 0 0 1px $primary-color;
            }
          }

          .el-input__inner {
            color: $text-primary;

            &::placeholder {
              color: $text-secondary;
            }
          }
        }

        .el-select__tags {
          .el-tag {
            background: rgba(64, 158, 255, 0.2);
            border-color: $primary-color;
            color: $text-primary;

            .el-tag__close {
              color: $text-primary;

              &:hover {
                color: $primary-color;
              }
            }
          }
        }
      }
    }
    
    .action-buttons {
      display: flex;
      gap: $spacing-md;
      align-items: center;

      .el-button.is-circle {
        width: 48px;
        height: 48px;
        
        &:hover {
          transform: scale(1.05);
          transition: transform 0.2s ease;
        }
      }
    }
  }
}

.dashboard-main {
  flex: 1;
  display: grid;
  grid-template-rows: 45% 55%; /* 优化：给实时进出记录更多空间 */
  gap: $spacing-md;
  padding: $spacing-lg;
  overflow: hidden;
  min-height: 0; /* 重要：允许grid子项收缩 */
}

.statistics-section,
.records-section {
  min-height: 0; // 重要：允许grid子项收缩
}

.statistics-section {
  overflow: hidden; // 防止内容溢出
}

.dashboard-footer {
  padding: $spacing-sm $spacing-lg; /* 减小底部高度 */
  border-top: 1px solid $border-color;
  background: rgba(255, 255, 255, 0.02);
  
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: $font-size-sm;
    color: $text-muted;
  }
  
  .system-info {
    display: flex;
    gap: $spacing-lg;
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(12, 20, 38, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
  
  .loading-content {
    text-align: center;
    color: $text-primary;
    
    .loading-icon {
      font-size: 48px;
      color: $primary-color;
      animation: spin 1s linear infinite;
      margin-bottom: $spacing-lg;
    }
    
    p {
      font-size: $font-size-lg;
      margin: 0;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard-header {
    padding: $spacing-md $spacing-lg;
    
    .main-title {
      font-size: $font-size-xl;
    }
  }
  
  .dashboard-main {
    padding: $spacing-lg;
    gap: $spacing-lg;
  }
}

@media (max-height: 800px) {
  .dashboard-main {
    grid-template-rows: auto 1fr;
  }
  
  .statistics-section {
    // 在小屏幕上进一步限制统计面板高度
    max-height: 50vh;
  }
}
</style>
