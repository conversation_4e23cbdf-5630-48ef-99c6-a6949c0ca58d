<template>
  <div id="app">
    <!-- 导航栏 -->
    <nav class="app-nav" v-if="showNavigation">
      <div class="nav-content">
        <div class="nav-left">
          <router-link to="/" class="nav-brand">
            <el-icon><Monitor /></el-icon>
            宿舍管理系统
          </router-link>
        </div>

        <div class="nav-right">
          <router-link
            to="/"
            class="nav-link"
            :class="{ active: $route.path === '/' }"
          >
            <el-icon><DataBoard /></el-icon>
            大屏展示
          </router-link>

          <router-link
            to="/students"
            class="nav-link"
            :class="{ active: $route.path === '/students' }"
          >
            <el-icon><User /></el-icon>
            学生状态
          </router-link>

          <router-link
            to="/tasks"
            class="nav-link"
            :class="{ active: $route.path === '/tasks' }"
          >
            <el-icon><Timer /></el-icon>
            任务管理
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 路由视图 -->
    <router-view />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Monitor, DataBoard, User, Timer } from '@element-plus/icons-vue'

const route = useRoute()

// 控制导航栏显示
const showNavigation = computed(() => {
  // 在大屏页面隐藏导航栏，在其他页面显示导航栏
  return route.path !== '/'
})

</script>

<style scoped lang="scss">
#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.app-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(12, 20, 38, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid $border-color;

  .nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-md $spacing-lg;
    max-width: 1200px;
    margin: 0 auto;
  }

  .nav-brand {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    font-size: $font-size-lg;
    font-weight: 700;
    color: $text-primary;
    text-decoration: none;

    &:hover {
      color: $primary-color;
    }
  }

  .nav-right {
    display: flex;
    gap: $spacing-lg;
  }

  .nav-link {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    padding: $spacing-sm $spacing-md;
    color: $text-secondary;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      color: $primary-color;
      background: rgba(64, 158, 255, 0.1);
    }

    &.active {
      color: $primary-color;
      background: rgba(64, 158, 255, 0.2);
    }
  }
}

// 为有导航栏的页面添加顶部间距
:deep(.student-status-view) {
  padding-top: 80px;
}
</style>
