{"name": "dormitory-dashboard", "version": "1.0.0", "description": "宿舍归寝情况大屏展示系统", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "echarts": "^5.4.3", "element-plus": "^2.3.8", "vue": "^3.3.4", "vue-router": "^4.5.1", "vue3-perfect-scrollbar": "^2.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "sass": "^1.89.2", "terser": "^5.43.1", "vite": "^4.4.5"}, "keywords": ["vue3", "dashboard", "dormitory", "realtime"], "author": "System", "license": "MIT"}