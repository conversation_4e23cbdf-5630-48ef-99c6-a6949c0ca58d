<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>宿舍归寝情况大屏展示系统</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
      overflow: hidden;
    }
    
    #app {
      width: 100vw;
      height: 100vh;
    }
    
    /* 隐藏原生滚动条，使用Perfect Scrollbar统一样式 */
    ::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  </style>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>
