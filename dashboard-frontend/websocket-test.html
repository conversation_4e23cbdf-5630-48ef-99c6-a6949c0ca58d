<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket实时推送测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .records {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #fafafa;
        }
        .record {
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-left: 4px solid #007bff;
            border-radius: 3px;
        }
        .record.enter {
            border-left-color: #28a745;
        }
        .record.leave {
            border-left-color: #dc3545;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        select {
            padding: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket实时推送测试</h1>
        
        <div class="controls">
            <button id="connectBtn" class="btn-primary">连接WebSocket</button>
            <button id="disconnectBtn" class="btn-danger">断开连接</button>
            <button id="testPushBtn" class="btn-success">推送测试记录</button>
            
            <label for="buildingSelect">楼栋筛选:</label>
            <select id="buildingSelect">
                <option value="ALL">全部楼栋</option>
                <option value="1号楼">1号楼</option>
                <option value="2号楼">2号楼</option>
                <option value="3号楼">3号楼</option>
                <option value="4号楼">4号楼</option>
                <option value="5号楼">5号楼</option>
            </select>
        </div>
        
        <div id="status" class="status disconnected">
            状态: 未连接
        </div>
        
        <h3>实时记录 (最多显示10条)</h3>
        <div id="records" class="records">
            <p>等待连接...</p>
        </div>
    </div>

    <script>
        let websocket = null;
        let records = [];
        const maxRecords = 10;
        let selectedBuilding = 'ALL';

        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const testPushBtn = document.getElementById('testPushBtn');
        const statusDiv = document.getElementById('status');
        const recordsDiv = document.getElementById('records');
        const buildingSelect = document.getElementById('buildingSelect');

        // 楼栋筛选变化
        buildingSelect.addEventListener('change', function() {
            selectedBuilding = this.value;
            console.log('选择楼栋:', selectedBuilding);
            updateRecordsDisplay();
        });

        // 连接WebSocket
        connectBtn.addEventListener('click', function() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                return;
            }

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const wsUrl = `${protocol}//${host}/ws/dashboard/records`;

            console.log('连接WebSocket:', wsUrl);
            websocket = new WebSocket(wsUrl);

            websocket.onopen = function() {
                console.log('WebSocket连接成功');
                updateStatus('已连接', true);
                recordsDiv.innerHTML = '<p>已连接，等待数据...</p>';
            };

            websocket.onmessage = function(event) {
                console.log('收到消息:', event.data);
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (e) {
                    console.error('解析消息失败:', e);
                }
            };

            websocket.onclose = function() {
                console.log('WebSocket连接关闭');
                updateStatus('连接关闭', false);
            };

            websocket.onerror = function(error) {
                console.error('WebSocket错误:', error);
                updateStatus('连接错误', false);
            };
        });

        // 断开连接
        disconnectBtn.addEventListener('click', function() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        });

        // 推送测试记录
        testPushBtn.addEventListener('click', function() {
            fetch('/api/websocket/test/push')
                .then(response => response.json())
                .then(data => {
                    console.log('推送测试记录结果:', data);
                })
                .catch(error => {
                    console.error('推送测试记录失败:', error);
                });
        });

        // 处理WebSocket消息
        function handleMessage(message) {
            if (message.type === 'new_record') {
                addRecord(message.record);
            } else if (message.type === 'batch_records') {
                message.records.forEach(record => addRecord(record));
            } else if (message.type === 'heartbeat') {
                console.log('收到心跳消息');
            }
        }

        // 添加记录
        function addRecord(record) {
            // 楼栋筛选
            if (selectedBuilding !== 'ALL' && record.buildingName !== selectedBuilding) {
                return;
            }

            records.unshift(record);
            if (records.length > maxRecords) {
                records = records.slice(0, maxRecords);
            }
            updateRecordsDisplay();
        }

        // 更新记录显示
        function updateRecordsDisplay() {
            const filteredRecords = selectedBuilding === 'ALL' 
                ? records 
                : records.filter(r => r.buildingName === selectedBuilding);

            if (filteredRecords.length === 0) {
                recordsDiv.innerHTML = '<p>暂无记录</p>';
                return;
            }

            const html = filteredRecords.map(record => {
                const className = record.lastInOrOut === 1 ? 'enter' : 'leave';
                return `
                    <div class="record ${className}">
                        <strong>${record.personName}</strong> (${record.personCode}) 
                        <span style="color: ${record.lastInOrOut === 1 ? '#28a745' : '#dc3545'}">
                            ${record.lastInOrOutDesc}
                        </span>
                        <br>
                        楼栋: ${record.buildingName || '未知'} | 
                        时间: ${record.lastPassTimeStr} | 
                        设备: ${record.lastDeviceName || '未知'}
                    </div>
                `;
            }).join('');

            recordsDiv.innerHTML = html;
        }

        // 更新连接状态
        function updateStatus(message, connected) {
            statusDiv.textContent = `状态: ${message}`;
            statusDiv.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }
    </script>
</body>
</html>
