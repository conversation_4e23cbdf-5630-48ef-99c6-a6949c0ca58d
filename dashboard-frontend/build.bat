@echo off
echo ========================================
echo 宿舍归寝情况大屏展示系统 - 生产构建
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo 正在检查项目依赖...
if not exist "node_modules" (
    echo 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

echo 正在清理旧的构建文件...
if exist "dist" rmdir /s /q "dist"

echo 正在构建生产版本...
npm run build

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 构建成功！
    echo ========================================
    echo 构建文件位置: dist/
    echo.
    echo 部署说明:
    echo 1. 将 dist 目录下的所有文件复制到Web服务器
    echo 2. 配置Web服务器代理 /api 到后端服务
    echo 3. 确保后端服务正常运行
    echo.
    echo 本地预览构建结果:
    echo npm run preview
    echo.
) else (
    echo.
    echo 构建失败，请检查错误信息
)

pause
