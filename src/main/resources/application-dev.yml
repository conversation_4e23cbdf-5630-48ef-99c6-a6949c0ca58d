# ==================== 开发环境配置 ====================
# 说明：开发环境专用配置，包含开发调试相关设置

server:
  port: 8080

# ==================== 数据源配置 ====================
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: jdbc:postgresql://*************:5432/transfer
          username: postgres
          password: a@123456
          driver-class-name: org.postgresql.Driver
          hikari:
            maximum-pool-size: 10
            minimum-idle: 2
            connection-timeout: 20000
            idle-timeout: 300000
            max-lifetime: 1200000
            leak-detection-threshold: 60000
        fastgate:
          url: *********************************************
          username: postgres
          password: a@123456
          driver-class-name: org.postgresql.Driver
          hikari:
            maximum-pool-size: 15
            minimum-idle: 3
            connection-timeout: 20000
            idle-timeout: 300000
            max-lifetime: 1200000
            leak-detection-threshold: 60000

  # Redis配置 - 开发环境
  redis:
    host: localhost
    port: 6379
    database: 0
    password: # 开发环境无密码
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
    # 键过期时间配置
    expire:
      default: 3600 # 1小时
      statistics: 300 # 5分钟

# ==================== FastGate平台配置 - 开发环境 ====================
fastgate:
  passwd: Jkxy12345_  # 使用实际密码
  url: http://127.0.0.1:8088/fastgate  # 使用实际URL
  
  api:
    addperson: /api/resource/v1/person
    delperson: /api/resource/v1/person
    getPersonList: /api/resource/v1/person
    getpicture: /api/resource/v1/person/picture/search
    addpicture: /api/resource/v1/person/picture
    delpicture: /api/resource/v1/person/picture

# ==================== Mock数据配置 - 开发环境 ====================
app:
  mock:
    enabled: true
    person:
      count: 100
    face:
      photo:
        count: 50

# ==================== 异步处理配置 - 开发环境 ====================
async:
  persistence:
    enabled: true
    thread:
      core: 2
      max: 5
    batch:
      size: 100
      timeout: 5000
    retry:
      max: 3
      delay: 1000
    max:
      tps: 500
    queue:
      max: 5000

# ==================== 数据库监控配置 - 开发环境 ====================
db:
  monitor:
    enabled: true
    check:
      interval:
        seconds: 60
    max:
      connections: 20
      tps: 1000
      response:
        time: 3000

# ==================== 日志配置 - 开发环境 ====================
logging:
  level:
    root: info
    fastgatedemo.demo: info
    fastgatedemo.demo.service.ViewMessageProcessService: info
    fastgatedemo.demo.service.StudentStatusCacheService: info
    fastgatedemo.demo.service.AsyncBatchPersistenceService: info
    fastgatedemo.demo.service.DatabasePressureMonitorService: info
    com.zaxxer.hikari.HikariConfig: info
    org.springframework.data.redis: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/fastgate-demo-dev.log
    max-size: 100MB
    max-history: 7

# ==================== 开发工具配置 ====================
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always