# ==================== 核心配置文件 ====================
# 说明：主配置文件，包含公共配置
# 环境特定配置请查看 application-dev.yml 和 application-prod.yml

# ==================== Spring配置 ====================
spring:
  profiles:
    active: dev # 默认激活开发环境，生产环境需要通过启动参数指定
  
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQL95Dialect
    hibernate:
      ddl-auto: none
    open-in-view: false
  
  # Thymeleaf模板配置
  thymeleaf:
    enabled: true
    prefix: classpath:/templates/
    suffix: .html

# ==================== FastGate平台配置 ====================
fastgate:
  user: admin
  passwd: Jkxy12345_  # 统一使用实际密码
  url: http://127.0.0.1:8088/fastgate  # 统一使用实际URL
  person:
    picPath: /home/<USER>/image/sdpicture/

# ==================== 人脸照片同步修复功能配置 ====================
face:
  photo:
    sync:
      fix:
        enabled: true
        batch-size: 10

# ==================== 公共日志配置 ====================
logging:
  level:
    org.springframework: warn
    org.hibernate: warn
    com.zaxxer.hikari: warn