<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人员管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .sync-status-0 { color: #dc3545; }
        .sync-status-1 { color: #198754; }
        .card-header { background-color: #f8f9fa; }

        /* 定时任务相关样式 */
        .task-row {
            cursor: pointer;
        }
        .task-row:hover {
            background-color: #f8f9fa;
        }
        .log-detail {
            font-size: 0.9em;
        }
        .refresh-btn {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title mb-0">速通门人员管理系统</h3>
                    </div>
                    <div class="card-body">
                        
                        <!-- 成功/错误消息 -->
                        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show">
                            <span th:text="${successMessage}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show">
                            <span th:text="${errorMessage}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>

                        <!-- 导航标签页 -->
                        <ul class="nav nav-tabs" id="managementTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="person-tab" data-bs-toggle="tab" data-bs-target="#person-content" type="button" role="tab">
                                    <i class="fas fa-users"></i> 人员管理
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="dormitory-tab" data-bs-toggle="tab" data-bs-target="#dormitory-content" type="button" role="tab">
                                    <i class="fas fa-building"></i> 宿舍管理
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="dormitory-status-tab" data-bs-toggle="tab" data-bs-target="#dormitory-status-content" type="button" role="tab" onclick="handleDormitoryStatusTabClick()">
                                    <i class="fas fa-bed"></i> 未归寝室
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="task-tab" data-bs-toggle="tab" data-bs-target="#task-content" type="button" role="tab" onclick="handleTaskTabClick()">
                                    <i class="fas fa-clock"></i> 定时任务
                                </button>
                            </li>
                        </ul>

                        <!-- 标签页内容 -->
                        <div class="tab-content" id="managementTabContent">
                            <!-- 人员管理标签页 -->
                            <div class="tab-pane fade show active" id="person-content" role="tabpanel">
                                
                        <!-- 统计信息卡片 -->
                        <div class="row mb-4" th:if="${statistics}">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-primary" th:text="${statistics.totalPersons}">0</h5>
                                        <p class="card-text">总人数</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success" th:text="${statistics.syncedPersons}">0</h5>
                                        <p class="card-text">已同步</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning" th:text="${statistics.unsyncedPersons}">0</h5>
                                        <p class="card-text">未同步</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 筛选和操作区域 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">数据筛选</h5>
                            </div>
                            <div class="card-body">
                                <!-- 筛选表单 -->
                                <form method="get" th:action="@{/}" class="row g-3">
                                    <input type="hidden" name="page" value="0"/>
                                    <input type="hidden" name="size" th:value="${pageSize}"/>
                                    <div class="col-md-3">
                                        <label class="form-label">人员姓名</label>
                                        <input type="text" name="name" class="form-control" 
                                               th:value="${filterName}" placeholder="输入人员姓名">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">同步状态</label>
                                        <select name="syncFlag" class="form-select">
                                            <option value="">全部</option>
                                            <option value="1" th:selected="${filterSyncFlag == 1}">已同步</option>
                                            <option value="0" th:selected="${filterSyncFlag == 0}">未同步</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                        <a href="/" class="btn btn-secondary">
                                            <i class="fas fa-refresh"></i> 重置
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                                
                        <!-- 操作按钮区 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-success ms-2" data-bs-toggle="modal" data-bs-target="#syncPersonsByBuildingModal">
                                    <i class="fas fa-building"></i> 按楼栋同步人员
                                </button>
                                <button type="button" class="btn btn-outline-primary ms-2" data-bs-toggle="modal" data-bs-target="#importExcelModal">
                                    <i class="fas fa-file-excel"></i> 导入Excel人员
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <span class="badge bg-info">当前页: <span th:text="${#lists.size(persons)}">0</span> 人</span>
                                <span class="badge bg-primary ms-1">总计: <span th:text="${totalElements}">0</span> 人</span>
                            </div>
                        </div>

                        <!-- 人员列表表格 -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>序号</th>
                                        <th>人员姓名</th>
                                        <th>人员编码</th>
                                        <th>卡号</th>
                                        <th>部门编码</th>
                                        <th>性别</th>
                                        <th>电话</th>
                                        <th>同步状态</th>
                                        <th>更新时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${#lists.isEmpty(persons)}">
                                        <td colspan="10" class="text-center">暂无人员数据</td>
                                    </tr>
                                    <tr th:each="person,iterStat : ${persons}">
                                        <td th:text="${currentPage * pageSize + iterStat.count}">1</td>
                                        <td th:text="${person.personName}">张三</td>
                                        <td th:text="${person.personCode}">P001</td>
                                        <td th:text="${person.cardnum}">A123456</td>
                                        <td th:text="${person.departmentCode}">D001</td>
                                        <td th:text="${person.gender == 1 ? '男' : '女'}">男</td>
                                        <td th:text="${person.telephone}">13800138000</td>
                                        <td>
                                            <span th:class="${'sync-status-' + (person.syncFlag != null ? person.syncFlag : 0)}"
                                                  th:text="${person.syncFlag == 1 ? '已同步' : '未同步'}">未同步</span>
                                        </td>
                                        <td th:text="${person.updateTime}">2024-01-01</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-success" 
                                                    th:onclick="'syncPerson(\'' + ${person.seqid} + '\')'">
                                                同步
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger ms-1" 
                                                    th:onclick="'deletePerson(\'' + ${person.seqid} + '\')'">
                                                删除
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary ms-1" 
                                                    th:onclick="|updatePersonFacePhoto('${person.personCode}', '${person.personName}')|"
                                                    title="更新人脸照片">
                                                <i class="fas fa-camera"></i> 更新照片
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页导航 -->
                        <nav aria-label="分页导航" th:if="${totalPages > 1}">
                            <ul class="pagination justify-content-center">
                                <!-- 首页 -->
                                <li class="page-item" th:classappend="${!hasPrevious} ? 'disabled'">
                                    <a class="page-link" th:href="@{/(page=0, size=${pageSize}, name=${filterName}, syncFlag=${filterSyncFlag})}">首页</a>
                                </li>
                                <!-- 上一页 -->
                                <li class="page-item" th:classappend="${!hasPrevious} ? 'disabled'">
                                    <a class="page-link" th:href="@{/(page=${currentPage - 1}, size=${pageSize}, name=${filterName}, syncFlag=${filterSyncFlag})}">上一页</a>
                                </li>
                                
                                <!-- 页码显示逻辑 -->
                                <th:block th:with="startPage=${currentPage - 2 < 0 ? 0 : currentPage - 2}, 
                                                 endPage=${currentPage + 2 >= totalPages ? totalPages - 1 : currentPage + 2}">
                                    <li th:each="pageNum : ${#numbers.sequence(startPage, endPage)}"
                                        class="page-item" th:classappend="${pageNum == currentPage} ? 'active'">
                                        <a class="page-link" 
                                           th:href="@{/(page=${pageNum}, size=${pageSize}, name=${filterName}, syncFlag=${filterSyncFlag})}"
                                           th:text="${pageNum + 1}">1</a>
                                    </li>
                                </th:block>
                                
                                <!-- 下一页 -->
                                <li class="page-item" th:classappend="${!hasNext} ? 'disabled'">
                                    <a class="page-link" th:href="@{/(page=${currentPage + 1}, size=${pageSize}, name=${filterName}, syncFlag=${filterSyncFlag})}">下一页</a>
                                </li>
                                <!-- 尾页 -->
                                <li class="page-item" th:classappend="${!hasNext} ? 'disabled'">
                                    <a class="page-link" th:href="@{/(page=${totalPages - 1}, size=${pageSize}, name=${filterName}, syncFlag=${filterSyncFlag})}">尾页</a>
                                </li>
                            </ul>
                        </nav>

                        <!-- 分页信息显示 -->
                        <div class="text-center text-muted mt-3" th:if="${totalElements > 0}">
                            <small>
                                第 <span th:text="${currentPage + 1}">1</span> 页，共 <span th:text="${totalPages}">1</span> 页，
                                总计 <span th:text="${totalElements}">0</span> 条记录，
                                当前显示第 <span th:text="${currentPage * pageSize + 1}">1</span> - 
                                <span th:text="${currentPage * pageSize + #lists.size(persons)}">10</span> 条
                            </small>
                        </div>
                                
                            </div>

                            <!-- 未归寝室人员查询标签页 -->
                            <div class="tab-pane fade" id="dormitory-status-content" role="tabpanel">

                                <!-- 查询条件区域 -->
                                <div class="card mb-4 mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-search text-primary"></i> 查询条件
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-3">
                                                <label class="form-label">查询日期</label>
                                                <input type="date" id="queryDate" class="form-control" />
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">查询类型</label>
                                                <select id="queryType" class="form-select">
                                                    <option value="not-returned">未归寝室</option>
                                                    <option value="returned">已归寝室</option>
                                                    <option value="statistics">统计信息</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3 d-flex align-items-end">
                                                <button type="button" class="btn btn-primary me-2" onclick="queryDormitoryStatus()">
                                                    <i class="fas fa-search"></i> 查询
                                                </button>
                                                <button type="button" class="btn btn-secondary" onclick="resetDormitoryQuery()">
                                                    <i class="fas fa-refresh"></i> 重置
                                                </button>
                                            </div>
                                            <div class="col-md-3 d-flex align-items-end">
                                                <button type="button" class="btn btn-info" onclick="refreshDormitoryStatus()">
                                                    <i class="fas fa-sync"></i> 刷新
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 统计信息卡片 -->
                                <div class="row mb-4" id="dormitoryStatsCards" style="display: none;">
                                    <div class="col-md-3">
                                        <div class="card text-center border-primary">
                                            <div class="card-body">
                                                <h4 class="text-primary mb-1" id="totalPersonsCount">0</h4>
                                                <p class="card-text">总人数</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-danger">
                                            <div class="card-body">
                                                <h4 class="text-danger mb-1" id="notReturnedCount">0</h4>
                                                <p class="card-text">未归寝室</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-success">
                                            <div class="card-body">
                                                <h4 class="text-success mb-1" id="returnedCount">0</h4>
                                                <p class="card-text">已归寝室</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-info">
                                            <div class="card-body">
                                                <h4 class="text-info mb-1" id="returnRate">0%</h4>
                                                <p class="card-text">归宿率</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 查询结果区域 -->
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-list text-success"></i>
                                            <span id="resultTitle">查询结果</span>
                                        </h5>
                                        <div>
                                            <span class="badge bg-primary" id="resultCount">0 人</span>
                                            <span class="badge bg-info ms-1" id="queryDateBadge">今日</span>
                                        </div>
                                    </div>
                                    <div class="card-body">

                                        <!-- 加载状态 -->
                                        <div id="dormitoryLoadingState" class="text-center py-5" style="display: none;">
                                            <div class="spinner-border text-primary mb-3" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <p class="text-muted">正在查询寝室归宿状态...</p>
                                        </div>

                                        <!-- 空状态 -->
                                        <div id="dormitoryEmptyState" class="text-center py-5">
                                            <i class="fas fa-bed fa-3x text-muted mb-3"></i>
                                            <h6 class="text-muted">请选择查询条件并点击查询按钮</h6>
                                            <p class="text-muted small">支持查询未归寝室人员、已归寝室人员和统计信息</p>
                                        </div>

                                        <!-- 结果表格 -->
                                        <div class="table-responsive" id="dormitoryResultTable" style="display: none;">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-dark">
                                                    <tr id="dormitoryTableHeader">
                                                        <!-- 表头将由JavaScript动态生成 -->
                                                    </tr>
                                                </thead>
                                                <tbody id="dormitoryTableBody">
                                                    <!-- 表格内容将由JavaScript动态生成 -->
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- 分页控件 -->
                                        <div class="row mt-3" id="dormitoryPaginationContainer" style="display: none;">
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center">
                                                    <label class="form-label me-2">每页显示:</label>
                                                    <select class="form-select" id="dormitoryPageSize" style="width: 100px;" onchange="changeDormitoryPageSize()">
                                                        <option value="10">10</option>
                                                        <option value="20" selected>20</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                    <span class="ms-2 text-muted">条记录</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <nav aria-label="寝室归宿状态分页">
                                                    <ul class="pagination justify-content-end mb-0" id="dormitoryPagination">
                                                        <!-- 分页按钮将由JavaScript动态生成 -->
                                                    </ul>
                                                </nav>
                                            </div>
                                        </div>

                                        <!-- 分页信息显示 -->
                                        <div class="text-center text-muted mt-3" id="dormitoryPageInfo" style="display: none;">
                                            <small id="dormitoryPageInfoText">
                                                <!-- 分页信息将由JavaScript动态生成 -->
                                            </small>
                                        </div>

                                    </div>
                                </div>

                            </div>

                                        <!-- 宿舍管理标签页 -->
            <div class="tab-pane fade" id="dormitory-content" role="tabpanel">
                
                <!-- 宿舍管理子标签 -->
                <ul class="nav nav-pills mt-3 mb-3" id="dormitorySubTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="dormitory-info-tab" data-bs-toggle="pill" data-bs-target="#dormitory-info-content" type="button" role="tab">
                            <i class="fas fa-building"></i> 宿舍信息
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="dormitory-relation-tab" data-bs-toggle="pill" data-bs-target="#dormitory-relation-content" type="button" role="tab">
                            <i class="fas fa-users"></i> 人员宿舍关联
                        </button>
                    </li>

                </ul>

                <!-- 宿舍管理子标签内容 -->
                <div class="tab-content" id="dormitorySubTabContent">
                    
                    <!-- 宿舍信息子页面 -->
                    <div class="tab-pane fade show active" id="dormitory-info-content" role="tabpanel">
                        
                        <!-- 宿舍操作按钮区 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-success" onclick="loadDormitoryList()">
                                    <i class="fas fa-sync"></i> 刷新宿舍列表
                                </button>
                                <button type="button" class="btn btn-outline-warning ms-2" data-bs-toggle="modal" data-bs-target="#syncBuildingsModal">
                                    <i class="fas fa-building"></i> 楼栋选择同步
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" data-bs-toggle="modal" data-bs-target="#syncDepartmentsModal">
                                    <i class="fas fa-sitemap"></i> 楼栋部门同步
                                </button>


                            </div>
                            <div class="col-md-6 text-end">
                                <span class="badge bg-primary">中间库宿舍: <span id="dormitoryCount">0</span> 间</span>
                            </div>
                        </div>

                        <!-- 宿舍列表表格 -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>序号</th>
                                        <th>宿舍编码</th>
                                        <th>宿舍名称</th>
                                        <th>楼栋</th>
                                        <th>楼层</th>
                                        <th>房间号</th>
                                        <th>床位数</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="dormitoryTableBody">
                                    <tr>
                                        <td colspan="10" class="text-center">
                                            <div class="text-muted">
                                                <i class="fas fa-info-circle"></i> 请点击"刷新宿舍列表"按钮加载数据
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页控件 -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <label class="form-label me-2">每页显示:</label>
                                    <select class="form-select" id="pageSize" style="width: 80px;" onchange="changePageSize()">
                                        <option value="10">10</option>
                                        <option value="20" selected>20</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span class="ms-2 text-muted">条记录</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <nav aria-label="宿舍列表分页">
                                    <ul class="pagination justify-content-end mb-0" id="dormitoryPagination">
                                        <!-- 分页按钮将由JavaScript动态生成 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                        
                        <!-- 分页信息显示 -->
                        <div class="row mt-2">
                            <div class="col-md-12">
                                <div class="text-muted text-center">
                                    <small id="dormitoryPageInfo">点击"刷新宿舍列表"开始加载数据</small>
                                </div>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 人员宿舍关联子页面 -->
                    <div class="tab-pane fade" id="dormitory-relation-content" role="tabpanel">
                        
                        <!-- 人员宿舍关联操作按钮区 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-success" onclick="refreshRelationList()">
                                    <i class="fas fa-sync"></i> 刷新关联列表
                                </button>
                                <button type="button" class="btn btn-warning ms-2" onclick="batchBindAllRelations()">
                                    <i class="fas fa-upload"></i> 批量绑定所有关联
                                </button>
                                <button type="button" class="btn btn-info ms-2" data-bs-toggle="modal" data-bs-target="#bindPersonDormitoryModal">
                                    <i class="fas fa-plus"></i> 单个绑定
                                </button>
                                <button type="button" class="btn btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#buildingBindModal" onclick="openBuildingBindModal()">
                                    <i class="fas fa-building"></i> 按楼栋绑定
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="manualSyncUnsyncedRelations()">
                                    <i class="fas fa-sync"></i> 同步未同步记录
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <span class="badge bg-success">关联记录: <span id="relationCount">0</span> 条</span>
                                <span class="badge bg-warning ms-2">未同步: <span id="unsyncedCount">0</span> 条</span>
                                <span class="badge bg-info ms-2">已同步: <span id="syncedCount">0</span> 条</span>
                            </div>
                        </div>

                        <!-- 人员宿舍关联列表表格 -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>序号</th>
                                        <th>人员编码</th>
                                        <th>宿舍编码</th>
                                        <th>宿舍名称</th>
                                        <th>楼栋</th>
                                        <th>楼层</th>
                                        <th>床位号</th>
                                        <th>分配日期</th>
                                        <th>同步状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="relationTableBody">
                                    <tr>
                                        <td colspan="10" class="text-center">
                                            <div class="text-muted">
                                                <i class="fas fa-info-circle"></i> 请点击"刷新关联列表"按钮加载数据
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页控件 -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <label for="relationPageSize" class="form-label me-2 mb-0">每页显示:</label>
                                    <select class="form-select form-select-sm" id="relationPageSize" style="width: auto;" onchange="changeRelationPageSize()">
                                        <option value="10" selected>10条</option>
                                        <option value="20">20条</option>
                                        <option value="50">50条</option>
                                        <option value="100">100条</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <small class="text-muted" id="relationPageInfo">暂无数据</small>
                            </div>
                        </div>

                        <!-- 分页导航 -->
                        <nav aria-label="人员宿舍关联分页导航" class="mt-3">
                            <ul class="pagination justify-content-center" id="relationPagination">
                                <!-- 分页按钮将由JavaScript动态生成 -->
                            </ul>
                        </nav>
                        
                    </div>

                    <!-- 楼栋人员同步子页面 -->
                    <div class="tab-pane fade" id="building-person-sync-content" role="tabpanel">
                        
                        <!-- 楼栋选择区域 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-building"></i> 楼栋选择
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">选择楼栋</label>
                                        <select class="form-select" id="buildingSelect" onchange="onBuildingChange()">
                                            <option value="">请选择楼栋</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">楼栋操作</label>
                                        <div class="d-flex gap-2">
                                                                        <button type="button" class="btn btn-primary" onclick="loadBuildingListForSelect()">
                                <i class="fas fa-sync"></i> 刷新楼栋
                            </button>
                                            <button type="button" class="btn btn-info" onclick="loadBuildingPersons()" id="loadPersonsBtn" disabled>
                                                <i class="fas fa-users"></i> 查看人员
                                            </button>
                                            <button type="button" class="btn btn-success" onclick="syncBuildingPersons()" id="syncBuildingBtn" disabled>
                                                <i class="fas fa-upload"></i> 同步人员
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 楼栋统计信息 -->
                        <div class="card mb-4" id="buildingStatsCard" style="display: none;">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-bar"></i> 楼栋统计信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row" id="buildingStatsContent">
                                    <!-- 统计信息将由JavaScript动态填充 -->
                                </div>
                            </div>
                        </div>

                        <!-- 楼层选择区域 -->
                        <div class="card mb-4" id="floorSelectCard" style="display: none;">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-layer-group"></i> 楼层操作
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">选择楼层</label>
                                        <select class="form-select" id="floorSelect" onchange="onFloorChange()">
                                            <option value="">请选择楼层</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">楼层操作</label>
                                        <div class="d-flex gap-2">
                                            <button type="button" class="btn btn-info" onclick="loadFloorPersons()" id="loadFloorPersonsBtn" disabled>
                                                <i class="fas fa-users"></i> 查看楼层人员
                                            </button>
                                            <button type="button" class="btn btn-success" onclick="syncFloorPersons()" id="syncFloorBtn" disabled>
                                                <i class="fas fa-upload"></i> 同步楼层人员
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 人员列表显示区域 -->
                        <div class="card" id="personsListCard" style="display: none;">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-users"></i> 人员列表
                                    <span class="badge bg-primary ms-2" id="personsCountBadge">0</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- 人员列表表格 -->
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>序号</th>
                                                <th>人员姓名</th>
                                                <th>人员编码</th>
                                                <th>宿舍编码</th>
                                                <th>宿舍名称</th>
                                                <th>楼层</th>
                                                <th>房间号</th>
                                                <th>性别</th>
                                                <th>电话</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="buildingPersonsTableBody">
                                            <!-- 人员数据将由JavaScript动态填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 同步结果显示区域 -->
                        <div class="card mt-4" id="syncResultCard" style="display: none;">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-clipboard-check"></i> 同步结果
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="syncResultContent">
                                    <!-- 同步结果将由JavaScript动态填充 -->
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    
                </div>
                
            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Excel人员导入模态框 -->
    <div class="modal fade" id="importExcelModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Excel人员导入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 导入步骤导航 -->
                    <ul class="nav nav-pills mb-4" id="importStepsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="step1-tab" data-bs-toggle="pill" data-bs-target="#step1-content" type="button" role="tab">
                                <i class="fas fa-upload"></i> 1. 文件上传
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link disabled" id="step2-tab" data-bs-toggle="pill" data-bs-target="#step2-content" type="button" role="tab">
                                <i class="fas fa-search"></i> 2. 数据匹配
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link disabled" id="step3-tab" data-bs-toggle="pill" data-bs-target="#step3-content" type="button" role="tab">
                                <i class="fas fa-list"></i> 3. 结果查看
                            </button>
                        </li>
                    </ul>

                    <!-- 步骤内容 -->
                    <div class="tab-content" id="importStepsTabContent">
                        
                        <!-- 步骤1：文件上传 -->
                        <div class="tab-pane fade show active" id="step1-content" role="tabpanel">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="fw-bold mb-3">
                                        <i class="fas fa-file-excel text-success"></i> 选择Excel文件
                                    </h6>
                                    
                                    <!-- 文件上传区域 -->
                                    <div class="border border-dashed rounded p-4 text-center mb-3" id="uploadArea" 
                                         style="border-color: #dee2e6 !important; min-height: 150px;">
                                        <input type="file" id="excelFileInput" accept=".xls,.xlsx" style="display: none;">
                                        <div id="uploadContent">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                            <p class="mb-2">点击选择文件或拖拽文件到此处</p>
                                            <p class="text-muted small">支持 .xls 和 .xlsx 格式，文件大小不超过5MB</p>
                                            <button type="button" class="btn btn-outline-primary" onclick="selectExcelFile()">
                                                <i class="fas fa-folder-open"></i> 选择文件
                                            </button>
                                        </div>
                                        <div id="uploadProgress" style="display: none;">
                                            <div class="spinner-border text-primary mb-2" role="status"></div>
                                            <p class="mb-0">正在上传和解析文件...</p>
                                        </div>
                                    </div>

                                    <!-- 文件信息显示 -->
                                    <div id="fileInfo" class="alert alert-info" style="display: none;">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>文件信息：</strong>
                                        <span id="fileInfoText"></span>
                                    </div>

                                    <!-- 数据预览表格 -->
                                    <div id="previewSection" style="display: none;">
                                        <h6 class="fw-bold mb-3">数据预览 (前10行)</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>行号</th>
                                                        <th>姓名</th>
                                                        <th>工号</th>
                                                        <th>部门编码</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="previewTableBody">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <h6 class="fw-bold mb-3">
                                        <i class="fas fa-info-circle text-info"></i> Excel格式说明
                                    </h6>
                                    <div class="border rounded p-3">
                                        <p class="mb-2"><strong>必需列：</strong></p>
                                        <ul class="list-unstyled mb-3">
                                            <li><i class="fas fa-check text-success"></i> 姓名</li>
                                            <li><i class="fas fa-check text-success"></i> 工号</li>
                                            <li><i class="fas fa-check text-success"></i> 部门编码</li>
                                        </ul>
                                        
                                        <p class="mb-2"><strong>格式要求：</strong></p>
                                        <ul class="list-unstyled mb-3">
                                            <li><i class="fas fa-dot-circle text-primary"></i> 第一行为表头</li>
                                            <li><i class="fas fa-dot-circle text-primary"></i> 姓名和工号不能为空</li>
                                            <li><i class="fas fa-dot-circle text-primary"></i> 最多1000行数据</li>
                                        </ul>
                                        
                                        <div class="alert alert-warning p-2 small">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <strong>注意：</strong>工号将用于匹配中间库人员信息
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤2：数据匹配 -->
                        <div class="tab-pane fade" id="step2-content" role="tabpanel">
                            <div class="text-center mb-4">
                                <h6 class="fw-bold">
                                    <i class="fas fa-sync fa-spin text-primary"></i> 正在进行人员匹配...
                                </h6>
                                <div class="progress mx-auto" style="width: 60%;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         id="matchProgress" role="progressbar" style="width: 0%"></div>
                                </div>
                                <p class="text-muted mt-2" id="matchProgressText">准备开始匹配...</p>
                            </div>
                            
                            <div id="matchResultSummary" style="display: none;">
                                <div class="row text-center mb-4">
                                    <div class="col-md-3">
                                        <div class="card border-primary">
                                            <div class="card-body">
                                                <h3 class="text-primary mb-1" id="totalCountStat">0</h3>
                                                <small class="text-muted">总计</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-success">
                                            <div class="card-body">
                                                <h3 class="text-success mb-1" id="matchedCountStat">0</h3>
                                                <small class="text-muted">匹配成功</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-info">
                                            <div class="card-body">
                                                <h3 class="text-info mb-1" id="syncedCountStat">0</h3>
                                                <small class="text-muted">已同步</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card border-warning">
                                            <div class="card-body">
                                                <h3 class="text-warning mb-1" id="unmatchedCountStat">0</h3>
                                                <small class="text-muted">未匹配</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤3：结果查看 -->
                        <div class="tab-pane fade" id="step3-content" role="tabpanel">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="filterResults('all')">
                                            全部
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="filterResults('matched')">
                                            匹配成功
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="filterResults('synced')">
                                            已同步
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="filterResults('unmatched')">
                                            未匹配
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button type="button" class="btn btn-success btn-sm" onclick="syncUnsynced()">
                                        <i class="fas fa-sync"></i> 同步未同步人员
                                    </button>
                                </div>
                            </div>
                            
                            <div class="table-responsive" style="max-height: 400px;">
                                <table class="table table-sm table-hover">
                                    <thead class="table-dark sticky-top">
                                        <tr>
                                            <th>行号</th>
                                            <th>姓名</th>
                                            <th>工号</th>
                                            <th>部门编码</th>
                                            <th>匹配状态</th>
                                            <th>匹配姓名</th>
                                            <th>数据来源</th>
                                            <th>同步状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="nextStepBtn" onclick="nextStep()" style="display: none;">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                    <button type="button" class="btn btn-success" id="startMatchBtn" onclick="startMatching()" style="display: none;">
                        <i class="fas fa-search"></i> 开始匹配
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 按楼栋同步人员模态框 -->
    <div class="modal fade" id="syncPersonsByBuildingModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">按楼栋同步人员</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- 左侧：楼栋选择区域 -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-building text-primary"></i> 选择楼栋和楼层
                            </h6>
                            
                            <div class="border rounded p-3 mb-3" style="max-height: 400px; overflow-y: auto;">
                                <div id="buildingPersonStructureContainer">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i> 
                                    点击楼栋展开楼层选择，勾选要同步的楼层
                                </small>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllFloors()">
                                        全选楼层
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="clearAllFloors()">
                                        清除选择
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：人员预览区域 -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-users text-success"></i> 人员预览
                                <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="previewSelectedPersons()">
                                    <i class="fas fa-search"></i> 预览选中人员
                                </button>
                            </h6>
                            
                            <div class="border rounded p-3 mb-3" style="max-height: 400px; overflow-y: auto;">
                                <div id="personPreviewContainer">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-user-friends fa-2x mb-2"></i>
                                        <p>请先选择楼栋和楼层，然后点击"预览选中人员"查看人员列表</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="personPreviewSummary" class="alert alert-info d-none" role="alert">
                                <i class="fas fa-info-circle"></i>
                                <strong>预览统计：</strong>
                                <span id="previewSummaryText">选中 0 个楼栋，0 个楼层，0 名人员</span>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-warning mb-0">
                                <i class="fas fa-exclamation-triangle"></i> 
                                <strong>重要说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>同步时将把人员的 areaCode 字段设置为楼栋编码</li>
                                    <li>只同步选择楼层中有人员关联的宿舍</li>
                                    <li>已同步人员会被重新同步（覆盖更新）</li>
                                    <li>同步过程可能需要较长时间，请耐心等待</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="syncSelectedPersonsByBuilding()" id="syncPersonsByBuildingBtn">
                        <i class="fas fa-sync"></i> 开始同步选中人员
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增人员模态框 -->
    <div class="modal fade" id="addPersonModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form th:action="@{/person/save}" method="post" th:object="${personInfo}">
                    <div class="modal-header">
                        <h5 class="modal-title">新增人员信息</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">人员姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" th:field="*{personName}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">人员编码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" th:field="*{personCode}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">卡号</label>
                                <input type="text" class="form-control" th:field="*{cardnum}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">卡类型</label>
                                <input type="text" class="form-control" th:field="*{cardtype}" value="1">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">部门编码</label>
                                <input type="text" class="form-control" th:field="*{departmentCode}" value="iccsid">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">人员类型</label>
                                <input type="text" class="form-control" th:field="*{personType}" value="6">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">性别</label>
                                <select class="form-select" th:field="*{gender}">
                                    <option value="1">男</option>
                                    <option value="0">女</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">电话</label>
                                <input type="text" class="form-control" th:field="*{telephone}">
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">身份证号</label>
                                <input type="text" class="form-control" th:field="*{idcard}">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 新增宿舍模态框 -->
    <div class="modal fade" id="addDormitoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">测试宿舍添加</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dormitoryForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">宿舍名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dormitoryName" name="dormitoryName" required placeholder="请输入宿舍名称">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">宿舍编码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dormitoryCode" name="dormitoryCode" required placeholder="请输入宿舍编码">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">楼层数 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dormitoryFloor" name="dormitoryFloor" required placeholder="请输入楼层数" value="6">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">房间数 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dormitoryAmount" name="dormitoryAmount" required placeholder="请输入房间数" value="4">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">父级编码</label>
                                <input type="text" class="form-control" id="parentcode" name="parentcode" value="area" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">资源类型</label>
                                <input type="number" class="form-control" id="restype" name="restype" value="3" readonly>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">标签</label>
                                <input type="number" class="form-control" id="label" name="label" value="1" readonly>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-info" onclick="addDormitory()">添加宿舍</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 楼栋选择同步模态框 -->
    <div class="modal fade" id="syncBuildingsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">楼栋选择同步</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-bold">选择要同步的楼栋：</span>
                                <div>
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="selectAllBuildings()">
                                        <i class="fas fa-check-square"></i> 全选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllBuildings()">
                                        <i class="fas fa-square"></i> 清空
                                    </button>
                                </div>
                            </div>
                            <div id="buildingsListContainer" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem;">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> 正在加载楼栋列表...
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> 
                                <strong>说明：</strong>选择要同步的楼栋，系统将按楼栋汇总宿舍信息并同步到EGS平台。
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="syncSelectedBuildings()">
                        <i class="fas fa-upload"></i> 开始同步选择的楼栋
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 楼栋部门同步模态框 -->
    <div class="modal fade" id="syncDepartmentsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">楼栋部门同步</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-bold">选择要同步为部门的楼栋：</span>
                                <div>
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="selectAllDepartmentBuildings()">
                                        <i class="fas fa-check-square"></i> 全选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllDepartmentBuildings()">
                                        <i class="fas fa-square"></i> 清空
                                    </button>
                                </div>
                            </div>
                            <div id="departmentBuildingsListContainer" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem;">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> 正在加载楼栋列表...
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> 
                                <strong>说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>每个选中的楼栋将作为一个部门同步到EGS平台</li>
                                    <li>部门名称 = 楼栋名称</li>
                                    <li>部门编码 = 楼栋编码</li>
                                    <li>上级部门固定为 iccsid</li>
                                    <li>已存在的部门可能会创建失败（正常现象）</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="syncSelectedDepartments()">
                        <i class="fas fa-sitemap"></i> 开始同步选择的楼栋
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 人员宿舍绑定模态框 -->
    <div class="modal fade" id="bindPersonDormitoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">人员宿舍绑定</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="bindPersonDormitoryForm">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">楼栋编码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="buildnum" name="buildnum" required placeholder="请输入楼栋编码">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">楼层 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="floor" name="floor" required placeholder="请输入楼层">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">标签（固定值）</label>
                                <input type="number" class="form-control" id="label" name="label" value="-1" readonly disabled>
                                <small class="form-text text-muted">EGS平台要求的固定值</small>
                            </div>
                            <div class="col-md-12 mb-3">
                                <button type="button" class="btn btn-info" onclick="loadRoomList()">
                                    <i class="fas fa-search"></i> 查询房间列表
                                </button>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">选择房间</label>
                                <div id="roomListContainer">
                                    <div class="text-muted">请先查询房间列表</div>
                                </div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">人员编码 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="personCodes" rows="3" 
                                         placeholder="请输入人员编码，多个编码用逗号或换行分隔&#10;例如：STU006,STU005,STU010,STU009"></textarea>
                                <small class="form-text text-muted">支持逗号分隔或换行分隔的多个人员编码</small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="bindPersonToDormitory()">批量绑定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 按楼层同步人员模态框 -->
    <div class="modal fade" id="syncByFloorModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">按楼层同步人员</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="syncByFloorForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">楼栋编码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="floorBuildnum" name="floorBuildnum" 
                                       required placeholder="请输入楼栋编码">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">楼层 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="floorNumber" name="floorNumber" 
                                       required placeholder="请输入楼层">
                            </div>
                            <div class="col-md-12 mb-3">
                                <button type="button" class="btn btn-info" onclick="loadFloorPersons()">
                                    <i class="fas fa-search"></i> 查询该楼层的人员
                                </button>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">该楼层的人员列表</label>
                                <div id="floorPersonsContainer" style="max-height: 300px; overflow-y: auto;">
                                    <div class="text-muted">请先查询楼层人员</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="syncPersonsByFloor()">
                        <i class="fas fa-sync"></i> 同步该楼层所有人员
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 按楼栋绑定模态框 -->
    <div class="modal fade" id="buildingBindModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">按楼栋绑定人员宿舍</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- 楼栋选择区域 -->
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-building text-primary"></i> 楼栋选择
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <label class="form-label">选择楼栋</label>
                                            <select class="form-select" id="buildingSelectForBind" onchange="selectBuildingForBind()">
                                                <option value="">请选择楼栋</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">&nbsp;</label>
                                            <div>
                                                <button type="button" class="btn btn-primary" onclick="loadBuildingStatsForBind()">
                                                    <i class="fas fa-sync"></i> 刷新楼栋列表
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 楼栋统计信息区域 -->
                        <div class="col-md-12 mb-4" id="buildingStatsForBind" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-chart-bar text-info"></i> 楼栋统计信息
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row" id="buildingStatsContentForBind">
                                        <!-- 统计信息将由JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 楼层选择区域 -->
                        <div class="col-md-12 mb-4" id="floorSelectForBind" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-layer-group text-success"></i> 楼层选择
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="floorButtonsForBind">
                                        <!-- 楼层按钮将由JavaScript动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 房间人员分配表格区域 -->
                        <div class="col-md-12" id="roomPersonsForBind" style="display: none;">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-users text-warning"></i> 房间人员分配 
                                        <span id="selectedFloorInfoForBind" class="text-muted"></span>
                                    </h6>
                                    <div>
                                        <button type="button" class="btn btn-success btn-sm" onclick="bindSelectedFloor()" id="bindFloorBtnForBind">
                                            <i class="fas fa-link"></i> 绑定整个楼层
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover table-sm">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>房间号</th>
                                                    <th>床位数</th>
                                                    <th>当前人数</th>
                                                    <th>入住人员</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="roomPersonsTableForBind">
                                                <!-- 房间人员数据将由JavaScript动态填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 定时任务标签页 -->
    <div class="tab-pane fade" id="task-content" role="tabpanel">
        <div class="container-fluid mt-3">
            <!-- 手动加载按钮 -->
            <div class="alert alert-info d-flex justify-content-between align-items-center">
                <span>如果数据没有自动加载，请点击右侧按钮手动加载</span>
                <div>
                    <button class="btn btn-primary btn-sm me-2" onclick="handleTaskTabClick()">
                        <i class="fas fa-sync"></i> 手动加载数据
                    </button>
                    <button class="btn btn-warning btn-sm me-2" onclick="testManualSync()">
                        <i class="fas fa-play"></i> 测试手动同步
                    </button>
                    <button class="btn btn-info btn-sm me-2" onclick="checkTaskStatus()">
                        <i class="fas fa-info-circle"></i> 检查任务状态
                    </button>
                    <button class="btn btn-success btn-sm me-2" onclick="toggleSyncTask()">
                        <i class="fas fa-toggle-on"></i> 切换任务状态
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="showSyncPreview()">
                        <i class="fas fa-eye"></i> 同步预览
                    </button>
                </div>
            </div>

            <!-- 仪表板 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">总任务数</h6>
                                    <h3 id="totalTasks">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-tasks fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">启用任务</h6>
                                    <h3 id="enabledTasks">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-play fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">运行中任务</h6>
                                    <h3 id="runningTasks">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-spinner fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">禁用任务</h6>
                                    <h3 id="disabledTasks">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-pause fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 每日出入记录统计 -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">今日校验成功人数</h6>
                                    <h3 id="todaySuccessCount">-</h3>
                                    <small id="todayStatsTime" class="opacity-75">数据加载中...</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                                <div>
                                    <button class="btn btn-light btn-sm" onclick="manualQueryDailyAccessStats()">
                                        <i class="fas fa-calculator"></i> 手动统计
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 人脸照片同步问题查看区域 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-user-check"></i> 人脸照片同步状态检查
                            </h5>
                            <div>
                                <span class="badge bg-warning me-2" id="issueCountBadge">-</span>
                                <button class="btn btn-sm btn-outline-primary" onclick="manualCheckFacePhoto()">
                                    <i class="fas fa-search"></i> 手动检查
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="showFacePhotoIssues()">
                                    <i class="fas fa-eye"></i> 查看问题数据
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-info-circle"></i> 
                                        检查已同步的人员是否存在人脸照片数据缺失或同步标识异常的问题
                                    </p>
                                    <div id="facePhotoCheckStatus" class="text-muted">
                                        点击"手动检查"或"查看问题数据"按钮开始检查...
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="d-flex justify-content-end align-items-center">
                                        <div class="me-3">
                                            <small class="text-muted">最后检查时间</small>
                                            <div id="lastCheckTime" class="fw-bold">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 每日出入记录统计详细区域 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line"></i> 每日出入记录成功人数统计
                            </h5>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="manualQueryDailyAccessStats()">
                                    <i class="fas fa-calculator"></i> 手动统计
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="showDailyStatsDetail()">
                                    <i class="fas fa-chart-bar"></i> 查看详情
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-info-circle"></i> 
                                        统计当天所有校验成功的出入记录总人数（每天晚上8点自动执行）
                                    </p>
                                    <div id="dailyStatsStatus" class="text-muted">
                                        点击"手动统计"按钮开始统计当天数据...
                                    </div>
                                    <!-- 结果通知区域 -->
                                    <div id="dailyStatsNotification" class="mt-3" style="display: none;">
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="d-flex justify-content-end align-items-center">
                                        <div class="me-3">
                                            <small class="text-muted">最后统计时间</small>
                                            <div id="lastStatsTime" class="fw-bold">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="row">
                <!-- 任务列表 -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i> 定时任务列表
                            </h5>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshTasks()">
                                    <i class="fas fa-sync" id="refreshIcon"></i> 刷新
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="cleanupLogs()">
                                    <i class="fas fa-trash"></i> 清理日志
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>任务名称</th>
                                            <th>描述</th>
                                            <th>状态</th>
                                            <th>最后执行</th>
                                            <th>执行状态</th>
                                            <th>成功率</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="taskTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 侧边栏 -->
                <div class="col-md-4">
                    <!-- 最近执行日志 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-history"></i> 最近执行日志
                            </h6>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <div id="recentLogs">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 异常任务 -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-triangle"></i> 需要关注的任务
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="alertTasks">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务详情模态框 -->
    <div class="modal fade" id="taskDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle"></i> 任务详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="taskDetailContent">
                    <!-- 任务详情内容将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 人脸照片同步问题模态框 -->
    <div class="modal fade" id="facePhotoIssuesModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-times"></i> 人脸照片同步问题数据
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i>
                                显示已同步的人员但存在人脸照片数据缺失或同步标识异常的记录
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshFacePhotoIssues()">
                                <i class="fas fa-sync"></i> 刷新数据
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>人员编码</th>
                                    <th>姓名</th>
                                    <th>部门</th>
                                    <th>联系电话</th>
                                    <th>问题类型</th>
                                    <th>问题描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="facePhotoIssuesTableBody">
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <nav aria-label="分页导航">
                        <ul class="pagination justify-content-center" id="facePhotoPagination">
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    </div> <!-- 结束标签页内容 -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 处理定时任务标签页点击
        function handleTaskTabClick() {
            console.log('定时任务标签页被直接点击');

            // 延迟执行，确保标签页切换完成
            setTimeout(() => {
                console.log('开始加载定时任务数据...');
                loadTaskDashboard();
                loadTasks();
                loadRecentLogs();
                loadFacePhotoStats(); // 加载人脸照片统计信息
                loadDailyAccessStatsResults(); // 加载每日出入记录统计信息
            }, 300);
        }

        // 计算未同步人员数量
        document.addEventListener('DOMContentLoaded', function() {
            const unsyncedRows = document.querySelectorAll('.sync-status-0');
            document.getElementById('unsyncedCount').textContent = unsyncedRows.length;
            
            // 监听标签页切换事件，自动加载宿舍列表
            const dormitoryTab = document.getElementById('dormitory-tab');
            dormitoryTab.addEventListener('shown.bs.tab', function() {
                loadDormitoryList();
            });

            // 监听定时任务标签页切换事件 - 简化版本
            const taskTab = document.getElementById('task-tab');
            if (taskTab) {
                console.log('找到定时任务标签页元素，设置事件监听器');

                // 主要事件监听器
                taskTab.addEventListener('shown.bs.tab', function(e) {
                    console.log('定时任务标签页被激活 (shown.bs.tab)');
                    handleTaskTabClick();
                });

            } else {
                console.error('找不到定时任务标签页元素');
            }
        });

        // 同步单个人员
        function syncPerson(personId) {
            if (confirm('确定要同步此人员到速通门系统吗？')) {
                fetch(`/api/person/${personId}/sync`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('同步成功！');
                        location.reload();
                    } else {
                        alert('同步失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('同步过程中发生错误');
                });
            }
        }

        // 批量同步所有人员
        function syncAllPersons() {
            if (confirm('确定要批量同步所有未同步人员吗？')) {
                fetch('/api/persons/sync-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('批量同步过程中发生错误');
                });
            }
        }

        // 删除人员
        function deletePerson(personId) {
            if (confirm('确定要删除此人员吗？')) {
                fetch(`/api/person/${personId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除过程中发生错误');
                });
            }
        }
        
        // 更新人员人脸照片
        function updatePersonFacePhoto(personCode, personName) {
            if (!confirm(`确定要更新人员 ${personName} (${personCode}) 的人脸照片吗？`)) {
                return;
            }
            
            // 获取按钮元素
            const buttonElement = event.target.closest('button');
            const originalHtml = buttonElement.innerHTML;
            
            // 更新按钮状态为加载中
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            // 调用API
            fetch('/task-management/api/face-photo-issues/sync-single', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `personCode=${encodeURIComponent(personCode)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示成功消息
                    showFacePhotoUpdateMessage(`${personName} (${personCode}) 人脸照片更新成功！`, 'success');
                    
                    // 恢复按钮状态
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = originalHtml;
                } else {
                    // 显示失败消息
                    showFacePhotoUpdateMessage(`${personName} (${personCode}) 人脸照片更新失败: ${data.message}`, 'danger');
                    
                    // 恢复按钮状态
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = originalHtml;
                }
            })
            .catch(error => {
                // 网络错误，恢复按钮
                buttonElement.disabled = false;
                buttonElement.innerHTML = originalHtml;
                showFacePhotoUpdateMessage(`${personName} (${personCode}) 人脸照片更新失败: ${error.message}`, 'danger');
            });
        }

        // 全局变量保存分页状态
        let currentPage = 0;
        let currentPageSize = 20;
        let totalPages = 0;
        let totalRecords = 0;

        // 加载宿舍列表（分页版本）
        function loadDormitoryList(page = 0, size = null) {
            console.log('开始加载宿舍列表...', { page, size });
            
            // 如果没有指定size，使用当前设置的pageSize
            if (size === null) {
                size = parseInt(document.getElementById('pageSize').value) || 20;
            }
            
            // 显示加载状态
            const tableBody = document.getElementById('dormitoryTableBody');
            tableBody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center">
                        <div class="text-muted">
                            <i class="fas fa-spinner fa-spin"></i> 正在加载宿舍数据...
                        </div>
                    </td>
                </tr>
            `;
            
            // 清空分页控件
            document.getElementById('dormitoryPagination').innerHTML = '';
            document.getElementById('dormitoryPageInfo').textContent = '正在加载...';
            
            fetch(`/dormitory/list/page?page=${page}&size=${size}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('宿舍列表响应:', data);
                
                if (data.ErrCode === 200) {
                    const dormitories = data.data || [];
                    
                    // 更新全局分页状态
                    currentPage = data.page;
                    currentPageSize = data.size;
                    totalPages = data.totalPages;
                    totalRecords = data.total;
                    
                    // 更新表格和分页控件
                    updateDormitoryTable(dormitories, data.page, data.size);
                    updatePagination(data);
                    updatePageInfo(data);
                    
                    // 更新宿舍总数显示
                    document.getElementById('dormitoryCount').textContent = data.total;
                } else {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="10" class="text-center text-danger">
                                <i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.ErrMsg}
                            </td>
                        </tr>
                    `;
                    document.getElementById('dormitoryPageInfo').textContent = '加载失败';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> 网络错误: ${error.message}
                        </td>
                    </tr>
                `;
                document.getElementById('dormitoryPageInfo').textContent = '网络错误';
            });
        }

        // 更新宿舍表格（分页版本）
        function updateDormitoryTable(dormitories, page, size) {
            const tableBody = document.getElementById('dormitoryTableBody');
            
            if (dormitories.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center">
                            <div class="text-muted">
                                <i class="fas fa-info-circle"></i> 暂无宿舍数据
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            dormitories.forEach((dormitory, index) => {
                const createTime = dormitory.createTime ? new Date(dormitory.createTime).toLocaleString() : '';
                const statusText = dormitory.status === 1 ? '启用' : '禁用';
                const statusClass = dormitory.status === 1 ? 'text-success' : 'text-danger';
                
                // 计算全局序号
                const globalIndex = page * size + index + 1;
                
                html += `
                    <tr>
                        <td>${globalIndex}</td>
                        <td>${dormitory.dormitoryCode || ''}</td>
                        <td>${dormitory.roomName || ''}</td>
                        <td>${dormitory.buildingCode || ''}</td>
                        <td>${dormitory.floor || ''}</td>
                        <td>${dormitory.roomNum || ''}</td>
                        <td>${dormitory.bedCount || ''}</td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                        <td>${createTime}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="syncSingleDormitory('${dormitory.id}')">
                                同步到EGS
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }

        // 更新分页控件
        function updatePagination(pageData) {
            const pagination = document.getElementById('dormitoryPagination');
            const { page, totalPages, first, last, hasPrevious, hasNext } = pageData;
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let html = '';
            
            // 上一页按钮
            html += `
                <li class="page-item ${!hasPrevious ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${page - 1}); return false;">上一页</a>
                </li>
            `;
            
            // 页码按钮
            const startPage = Math.max(0, page - 2);
            const endPage = Math.min(totalPages - 1, page + 2);
            
            // 如果起始页不是第一页，显示第一页和省略号
            if (startPage > 0) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(0); return false;">1</a></li>`;
                if (startPage > 1) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }
            
            // 显示当前页附近的页码
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="goToPage(${i}); return false;">${i + 1}</a>
                    </li>
                `;
            }
            
            // 如果结束页不是最后一页，显示省略号和最后一页
            if (endPage < totalPages - 1) {
                if (endPage < totalPages - 2) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages - 1}); return false;">${totalPages}</a></li>`;
            }
            
            // 下一页按钮
            html += `
                <li class="page-item ${!hasNext ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${page + 1}); return false;">下一页</a>
                </li>
            `;
            
            pagination.innerHTML = html;
        }

        // 更新分页信息
        function updatePageInfo(pageData) {
            const { page, size, total, totalPages } = pageData;
            const startRecord = page * size + 1;
            const endRecord = Math.min((page + 1) * size, total);
            
            const info = `显示第 ${startRecord}-${endRecord} 条记录，共 ${total} 条记录，第 ${page + 1}/${totalPages} 页`;
            document.getElementById('dormitoryPageInfo').textContent = info;
        }

        // 跳转到指定页面
        function goToPage(page) {
            if (page < 0 || page >= totalPages) {
                return;
            }
            loadDormitoryList(page, currentPageSize);
        }

        // 改变每页显示条数
        function changePageSize() {
            const newSize = parseInt(document.getElementById('pageSize').value);
            currentPageSize = newSize;
            // 重新加载第一页
            loadDormitoryList(0, newSize);
        }

        // 添加宿舍
        function addDormitory() {
            const form = document.getElementById('dormitoryForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const dormitoryName = document.getElementById('dormitoryName').value;
            const dormitoryCode = document.getElementById('dormitoryCode').value;
            const dormitoryFloor = document.getElementById('dormitoryFloor').value;
            const dormitoryAmount = document.getElementById('dormitoryAmount').value;
            const parentcode = document.getElementById('parentcode').value;
            const restype = parseInt(document.getElementById('restype').value);
            const label = parseInt(document.getElementById('label').value);

            // 构建请求数据，按照指定格式
            const requestData = {
                res: {
                    Name: dormitoryName,
                    Code: dormitoryCode,
                    Parentcode: parentcode,
                    Restype: restype
                },
                Floor: dormitoryFloor,
                Amount: dormitoryAmount,
                Label: label
            };

            console.log('发送宿舍添加请求:', requestData);

            fetch('/fastgate/dormitory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('宿舍添加响应:', data);
                if (data.ErrCode === 200) {
                    alert('宿舍添加成功！\n' +
                          '宿舍编码: ' + data.data.Code + '\n' +
                          '宿舍名称: ' + data.data.Name + '\n' +
                          '响应消息: ' + data.ErrMsg);
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addDormitoryModal'));
                    modal.hide();
                    // 清空表单
                    form.reset();
                    // 重置默认值
                    document.getElementById('dormitoryFloor').value = '6';
                    document.getElementById('dormitoryAmount').value = '4';
                    document.getElementById('parentcode').value = 'area';
                    document.getElementById('restype').value = '3';
                    document.getElementById('label').value = '1';
                } else {
                    alert('宿舍添加失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('宿舍添加过程中发生错误：' + error.message);
            });
        }

        // 同步单个宿舍到EGS平台
        function syncSingleDormitory(dormitoryId) {
            if (confirm('确定要将此宿舍同步到EGS平台吗？')) {
                console.log('开始同步宿舍:', dormitoryId);
                
                fetch(`/dormitory/sync/${dormitoryId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('宿舍同步响应:', data);
                    
                    if (data.ErrCode === 200) {
                        alert('宿舍同步成功！\n' + data.ErrMsg);
                    } else {
                        alert('宿舍同步失败：' + data.ErrMsg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('宿舍同步过程中发生错误：' + error.message);
                });
            }
        }

        // 批量同步所有宿舍到EGS平台
        function syncAllDormitories() {
            if (confirm('确定要批量同步所有宿舍到EGS平台吗？\n此操作可能需要较长时间，请耐心等待。')) {
                console.log('开始批量同步宿舍...');
                
                // 显示进度提示
                const originalText = event.target.innerHTML;
                event.target.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
                event.target.disabled = true;
                
                fetch('/dormitory/sync/all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('批量同步响应:', data);
                    
                    // 恢复按钮状态
                    event.target.innerHTML = originalText;
                    event.target.disabled = false;
                    
                    if (data.ErrCode === 200) {
                        let message = '批量同步完成！\n\n' + data.ErrMsg;
                        
                        // 如果有详细统计数据，显示更详细的信息
                        if (data.data) {
                            const stats = data.data;
                            message += `\n\n详细统计:`;
                            message += `\n总计: ${stats.total} 条`;
                            message += `\n成功: ${stats.success} 条`;
                            message += `\n失败: ${stats.fail} 条`;
                        }
                        
                        alert(message);
                    } else {
                        alert('批量同步失败：' + data.ErrMsg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    event.target.innerHTML = originalText;
                    event.target.disabled = false;
                    alert('批量同步过程中发生错误：' + error.message);
                });
            }
        }

        // 加载房间列表
        function loadRoomList() {
            const buildnum = document.getElementById('buildnum').value;
            const floor = document.getElementById('floor').value;
            // 始终使用-1作为label的默认值
            const label = -1;

            if (!buildnum || !floor) {
                alert('请先输入楼栋编码和楼层');
                return;
            }

            console.log('开始查询房间列表:', { buildnum, floor, label });

            const requestData = {
                buildnum: buildnum,
                floor: parseInt(floor),
                label: label
            };

            // 显示加载状态
            const container = document.getElementById('roomListContainer');
            container.innerHTML = '<div class="text-muted"><i class="fas fa-spinner fa-spin"></i> 正在查询房间信息...</div>';

            fetch('/fastgate/dormitory/member', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('房间列表响应:', data);

                if (data.ErrCode === 200) {
                    const rooms = data.data || [];
                    displayRoomList(rooms);
                } else {
                    container.innerHTML = `<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> 查询失败: ${data.ErrMsg}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                container.innerHTML = `<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> 网络错误: ${error.message}</div>`;
            });
        }

        // 显示房间列表
        function displayRoomList(rooms) {
            const container = document.getElementById('roomListContainer');

            if (rooms.length === 0) {
                container.innerHTML = '<div class="text-muted"><i class="fas fa-info-circle"></i> 未找到房间信息</div>';
                return;
            }

            let html = '<div class="row">';
            rooms.forEach(room => {
                const roomId = room.seqid; // 注意这里使用seqid作为roomId
                const roomName = room.roomname || `${room.roomnum}`;
                const personsCount = room.persons ? room.persons.length : 0;
                
                html += `
                    <div class="col-md-6 mb-2">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="selectedRoom" 
                                   id="room_${roomId}" value="${roomId}" data-room-name="${roomName}">
                            <label class="form-check-label" for="room_${roomId}">
                                ${roomName} (房间号: ${room.roomnum}, 当前人数: ${personsCount})
                            </label>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            container.innerHTML = html;
        }

        // 绑定人员到宿舍
        function bindPersonToDormitory() {
            // 获取选中的房间
            const selectedRoom = document.querySelector('input[name="selectedRoom"]:checked');
            if (!selectedRoom) {
                alert('请先选择一个房间');
                return;
            }

            const roomId = parseInt(selectedRoom.value);
            const roomName = selectedRoom.getAttribute('data-room-name');

            // 获取人员编码
            const personCodesText = document.getElementById('personCodes').value.trim();
            if (!personCodesText) {
                alert('请输入人员编码');
                return;
            }

            // 解析人员编码（支持逗号分隔和换行分隔）
            const codes = personCodesText
                .split(/[,\n\r]+/)
                .map(code => code.trim())
                .filter(code => code.length > 0);

            if (codes.length === 0) {
                alert('请输入有效的人员编码');
                return;
            }

            if (confirm(`确定要将 ${codes.length} 个人员绑定到房间 "${roomName}" 吗？\n\n人员编码: ${codes.join(', ')}`)) {
                console.log('开始批量绑定人员宿舍:', { codes, roomId });

                const requestData = {
                    code: codes,
                    roomId: roomId
                };

                // 显示进度提示
                const bindButton = event.target;
                const originalText = bindButton.innerHTML;
                bindButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 绑定中...';
                bindButton.disabled = true;

                fetch('/fastgate/dormitory/batchBingPersonDor', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    console.log('批量绑定响应:', data);

                    // 恢复按钮状态
                    bindButton.innerHTML = originalText;
                    bindButton.disabled = false;

                    if (data.ErrCode === 200) {
                        alert(`人员宿舍绑定成功！\n\n${data.ErrMsg}`);
                        
                        // 关闭模态框并清空表单
                        const modal = bootstrap.Modal.getInstance(document.getElementById('bindPersonDormitoryModal'));
                        modal.hide();
                        clearBindForm();
                    } else {
                        alert('人员宿舍绑定失败：' + data.ErrMsg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    bindButton.innerHTML = originalText;
                    bindButton.disabled = false;
                    alert('人员宿舍绑定过程中发生错误：' + error.message);
                });
            }
        }

        // 清空绑定表单
        function clearBindForm() {
            document.getElementById('bindPersonDormitoryForm').reset();
            document.getElementById('label').value = '-1';
            document.getElementById('roomListContainer').innerHTML = '<div class="text-muted">请先查询房间列表</div>';
            document.getElementById('personCodes').value = '';
        }

        // 全局变量存储分页信息
        let relationCurrentPage = 0;
        let relationCurrentPageSize = 10;
        let relationTotalPages = 0;
        let relationTotalElements = 0;

        // 加载人员宿舍关联列表（分页版本）
        function loadPersonDormitoryRelations(page = 0, size = null) {
            console.log('开始加载人员宿舍关联列表...', { page, size });

            // 如果没有指定size，使用当前设置的pageSize
            if (size === null) {
                size = parseInt(document.getElementById('relationPageSize').value) || 10;
            }

            // 更新全局变量
            relationCurrentPage = page;
            relationCurrentPageSize = size;

            // 显示加载状态
            const tableBody = document.getElementById('relationTableBody');
            tableBody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center">
                        <div class="text-muted">
                            <i class="fas fa-spinner fa-spin"></i> 正在加载关联数据...
                        </div>
                    </td>
                </tr>
            `;

            // 清空分页控件
            document.getElementById('relationPagination').innerHTML = '';
            document.getElementById('relationPageInfo').textContent = '正在加载...';

            fetch(`/fastgate/dormitory/relations/page?page=${page}&size=${size}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('人员宿舍关联数据响应:', data);

                if (data.ErrCode === 200) {
                    const relations = data.data || [];
                    displayPersonDormitoryRelations(relations, page, size);

                    // 更新分页信息
                    relationTotalPages = data.totalPages || 0;
                    relationTotalElements = data.total || 0;

                    // 更新统计数字
                    document.getElementById('relationCount').textContent = relationTotalElements;

                    // 渲染分页控件
                    renderRelationPagination(data);
                    updateRelationPageInfo(data);
                } else {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="10" class="text-center">
                                <div class="text-danger">
                                    <i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.ErrMsg}
                                </div>
                            </td>
                        </tr>
                    `;
                    // 清空分页信息
                    document.getElementById('relationPagination').innerHTML = '';
                    document.getElementById('relationPageInfo').textContent = '加载失败';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center">
                            <div class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i> 网络错误: ${error.message}
                            </div>
                        </td>
                    </tr>
                `;
                // 清空分页信息
                document.getElementById('relationPagination').innerHTML = '';
                document.getElementById('relationPageInfo').textContent = '网络错误';
            });
        }

        // 显示人员宿舍关联列表（分页版本）
        function displayPersonDormitoryRelations(relations, page = 0, size = 10) {
            const tableBody = document.getElementById('relationTableBody');

            if (relations.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center">
                            <div class="text-muted">
                                <i class="fas fa-info-circle"></i> 暂无人员宿舍关联数据
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            relations.forEach((relation, index) => {
                // 计算全局序号：当前页 * 每页大小 + 当前索引 + 1
                const globalIndex = page * size + index + 1;

                // 同步状态显示
                const syncFlag = relation.sync_flag || 0;
                const syncStatusHtml = syncFlag === 1
                    ? '<span class="badge bg-success"><i class="fas fa-check"></i> 已同步</span>'
                    : '<span class="badge bg-warning"><i class="fas fa-clock"></i> 未同步</span>';

                html += `
                    <tr>
                        <td>${globalIndex}</td>
                        <td>${relation.person_code || '-'}</td>
                        <td>${relation.dormitory_code || '-'}</td>
                        <td>${relation.room_name || '-'}</td>
                        <td>${relation.building_code || '-'}</td>
                        <td>${relation.floor || '-'}</td>
                        <td>${relation.bed_no || '-'}</td>
                        <td>${relation.assign_date || '-'}</td>
                        <td>${syncStatusHtml}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="bindSingleRelation('${relation.person_code}', '${relation.dormitory_code}', '${relation.building_code}', ${relation.floor})">
                                <i class="fas fa-link"></i> 单独绑定
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableBody.innerHTML = html;
        }

        // 渲染人员宿舍关联分页控件
        function renderRelationPagination(pageData) {
            const { page, size, total, totalPages } = pageData;
            const pagination = document.getElementById('relationPagination');

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let html = '';

            // 首页按钮
            html += `
                <li class="page-item ${page === 0 ? 'disabled' : ''}">
                    <a class="page-link" href="javascript:void(0)" onclick="goToRelationPage(0)">首页</a>
                </li>
            `;

            // 上一页按钮
            html += `
                <li class="page-item ${page === 0 ? 'disabled' : ''}">
                    <a class="page-link" href="javascript:void(0)" onclick="goToRelationPage(${page - 1})">上一页</a>
                </li>
            `;

            // 页码按钮（显示当前页前后2页）
            const startPage = Math.max(0, page - 2);
            const endPage = Math.min(totalPages - 1, page + 2);

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="javascript:void(0)" onclick="goToRelationPage(${i})">${i + 1}</a>
                    </li>
                `;
            }

            // 下一页按钮
            html += `
                <li class="page-item ${page >= totalPages - 1 ? 'disabled' : ''}">
                    <a class="page-link" href="javascript:void(0)" onclick="goToRelationPage(${page + 1})">下一页</a>
                </li>
            `;

            // 末页按钮
            html += `
                <li class="page-item ${page >= totalPages - 1 ? 'disabled' : ''}">
                    <a class="page-link" href="javascript:void(0)" onclick="goToRelationPage(${totalPages - 1})">末页</a>
                </li>
            `;

            pagination.innerHTML = html;
        }

        // 更新人员宿舍关联分页信息
        function updateRelationPageInfo(pageData) {
            const { page, size, total, totalPages } = pageData;
            const startRecord = page * size + 1;
            const endRecord = Math.min((page + 1) * size, total);

            const info = `显示第 ${startRecord}-${endRecord} 条记录，共 ${total} 条记录，第 ${page + 1}/${totalPages} 页`;
            document.getElementById('relationPageInfo').textContent = info;
        }

        // 跳转到指定页面
        function goToRelationPage(page) {
            if (page < 0 || page >= relationTotalPages) {
                return;
            }
            loadPersonDormitoryRelations(page, relationCurrentPageSize);
        }

        // 改变每页显示条数
        function changeRelationPageSize() {
            const newSize = parseInt(document.getElementById('relationPageSize').value);
            relationCurrentPageSize = newSize;
            // 重新加载第一页
            loadPersonDormitoryRelations(0, newSize);
        }

        // 刷新关联列表（保持当前页面状态）
        function refreshRelationList() {
            loadPersonDormitoryRelations(relationCurrentPage, relationCurrentPageSize);
        }

        // 手动同步未同步的关联关系
        function manualSyncUnsyncedRelations() {
            if (confirm('确定要同步所有未同步的人员宿舍关联关系到EGS平台吗？\n\n这将会将未同步的关联关系同步到EGS平台。')) {
                console.log('开始手动同步未同步的关联关系');

                // 显示进度提示
                const syncButton = event.target;
                const originalText = syncButton.innerHTML;
                syncButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
                syncButton.disabled = true;

                fetch('/fastgate/dormitory/relations/sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('手动同步响应:', data);

                    // 恢复按钮状态
                    syncButton.innerHTML = originalText;
                    syncButton.disabled = false;

                    if (data.ErrCode === 200) {
                        const syncData = data.data;
                        let message = `同步完成！\n\n${data.ErrMsg}`;
                        if (syncData) {
                            message += `\n\n详细信息:`;
                            message += `\n- 总记录数: ${syncData.totalCount}`;
                            message += `\n- 成功: ${syncData.successCount}`;
                            message += `\n- 失败: ${syncData.failedCount}`;
                        }
                        alert(message);

                        // 刷新关联列表和统计信息
                        loadPersonDormitoryRelations(relationCurrentPage, relationCurrentPageSize);
                        loadSyncStatistics();
                    } else {
                        alert('同步失败：' + data.ErrMsg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    syncButton.innerHTML = originalText;
                    syncButton.disabled = false;
                    alert('同步过程中发生错误：' + error.message);
                });
            }
        }

        // 加载同步统计信息
        function loadSyncStatistics() {
            fetch('/fastgate/dormitory/relations/sync/statistics', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('同步统计信息响应:', data);

                if (data.ErrCode === 200 && data.data) {
                    const stats = data.data;
                    document.getElementById('syncedCount').textContent = stats.syncedCount || 0;
                    document.getElementById('unsyncedCount').textContent = stats.unsyncedCount || 0;
                }
            })
            .catch(error => {
                console.error('获取同步统计信息错误:', error);
            });
        }

        // 批量绑定所有人员宿舍关联
        function batchBindAllRelations() {
            if (confirm('确定要批量绑定所有人员宿舍关联到EGS平台吗？\n\n这将会将中间库中的所有关联关系同步到EGS平台。')) {
                console.log('开始批量绑定所有人员宿舍关联');

                // 显示进度提示
                const bindButton = event.target;
                const originalText = bindButton.innerHTML;
                bindButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 批量绑定中...';
                bindButton.disabled = true;

                fetch('/fastgate/dormitory/batch-bind-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('批量绑定所有关联响应:', data);

                    // 恢复按钮状态
                    bindButton.innerHTML = originalText;
                    bindButton.disabled = false;

                    if (data.ErrCode === 200) {
                        let message = '批量绑定完成！\n\n' + data.ErrMsg;
                        alert(message);
                        
                        // 刷新关联列表（保持当前页面）
                        loadPersonDormitoryRelations(relationCurrentPage, relationCurrentPageSize);
                    } else {
                        alert('批量绑定失败：' + data.ErrMsg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    bindButton.innerHTML = originalText;
                    bindButton.disabled = false;
                    alert('批量绑定过程中发生错误：' + error.message);
                });
            }
        }

        // 单独绑定某个关联关系
        function bindSingleRelation(personCode, dormitoryCode, buildingCode, floor) {
            if (confirm(`确定要绑定人员 ${personCode} 到宿舍 ${dormitoryCode} 吗？`)) {
                console.log('开始单独绑定:', { personCode, dormitoryCode, buildingCode, floor });

                // 这里可以实现单独绑定的逻辑
                // 暂时使用批量绑定接口模拟
                alert('单独绑定功能开发中，请使用批量绑定功能。');
            }
        }

        // 监听人员宿舍关联Tab切换事件
        document.addEventListener('DOMContentLoaded', function() {
            const relationTab = document.getElementById('dormitory-relation-tab');
            if (relationTab) {
                relationTab.addEventListener('shown.bs.tab', function() {
                    loadPersonDormitoryRelations(0, 10); // 切换Tab时重置到第一页
                    loadSyncStatistics(); // 加载同步统计信息
                });
            }
        });

        // === 按楼层同步人员相关函数 ===

        // 查询该楼层的人员
        function loadFloorPersons() {
            const buildnum = document.getElementById('floorBuildnum').value;
            const floor = document.getElementById('floorNumber').value;

            if (!buildnum || !floor) {
                alert('请先输入楼栋编码和楼层');
                return;
            }

            console.log('开始查询楼层人员:', { buildnum, floor });

            const container = document.getElementById('floorPersonsContainer');
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i> 正在查询楼层人员...
                </div>
            `;

            const requestData = {
                buildnum: buildnum,
                floor: parseInt(floor),
                label: -1  // 固定值
            };

            fetch('/fastgate/dormitory/member', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼层人员查询响应:', data);

                if (data.ErrCode === 200) {
                    const rooms = data.data || [];
                    displayFloorPersons(rooms, buildnum, floor);
                } else {
                    container.innerHTML = `
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> 查询失败: ${data.ErrMsg}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                container.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle"></i> 网络错误: ${error.message}
                    </div>
                `;
            });
        }

        // 显示楼层人员列表
        function displayFloorPersons(rooms, buildnum, floor) {
            const container = document.getElementById('floorPersonsContainer');

            if (rooms.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle"></i> 该楼层暂无房间或人员信息
                    </div>
                `;
                return;
            }

            let totalPersons = 0;
            let allPersonCodes = [];
            let html = `<div class="alert alert-info mb-3">
                <strong>${buildnum} 楼 ${floor} 层人员信息</strong>
            </div>`;

            rooms.forEach(room => {
                const roomName = room.roomname || `房间${room.roomnum || '未知'}`;
                const persons = room.persons || [];
                totalPersons += persons.length;

                html += `
                    <div class="card mb-2">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-door-open"></i> ${roomName}</span>
                            <span class="badge bg-primary">${persons.length} 人</span>
                        </div>
                `;

                if (persons.length > 0) {
                    html += `<div class="card-body p-2">`;
                    persons.forEach(person => {
                        const personCode = person.personcode || person.code || '未知编码';
                        const personName = person.personname || person.name || '未知姓名';
                        allPersonCodes.push(personCode);
                        
                        html += `
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span>
                                    <i class="fas fa-user"></i> ${personName}
                                    <small class="text-muted">(${personCode})</small>
                                </span>
                            </div>
                        `;
                    });
                    html += `</div>`;
                } else {
                    html += `<div class="card-body p-2 text-muted">该房间暂无人员</div>`;
                }

                html += `</div>`;
            });

            html += `
                <div class="alert alert-success mt-3">
                    <strong>统计信息：</strong>
                    <br>房间数：${rooms.length} 个
                    <br>总人数：${totalPersons} 人
                    <br>待同步人员编码：${allPersonCodes.join(', ')}
                </div>
            `;

            // 将人员编码保存到全局变量，供同步时使用
            window.currentFloorPersonCodes = allPersonCodes;
            
            container.innerHTML = html;
        }

        // 同步该楼层所有人员
        function syncPersonsByFloor() {
            const buildnum = document.getElementById('floorBuildnum').value;
            const floor = document.getElementById('floorNumber').value;

            if (!buildnum || !floor) {
                alert('请先输入楼栋编码和楼层');
                return;
            }

            if (!window.currentFloorPersonCodes || window.currentFloorPersonCodes.length === 0) {
                alert('请先查询该楼层的人员信息');
                return;
            }

            const personCodes = window.currentFloorPersonCodes;

            if (confirm(`确定要同步 ${buildnum} 楼 ${floor} 层的所有人员吗？\n\n共 ${personCodes.length} 个人员：\n${personCodes.join(', ')}\n\n系统将批量同步这些人员到EGS平台。`)) {
                console.log('开始同步楼层人员:', { buildnum, floor, personCodes });

                // 显示进度提示
                const syncButton = event.target;
                const originalText = syncButton.innerHTML;
                syncButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
                syncButton.disabled = true;

                const requestData = {
                    personCodes: personCodes
                };

                fetch('/api/persons/sync-batch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    console.log('楼层人员同步响应:', data);

                    // 恢复按钮状态
                    syncButton.innerHTML = originalText;
                    syncButton.disabled = false;

                    if (data.success) {
                        let message = `楼层人员同步完成！\n\n楼栋：${buildnum}\n楼层：${floor}\n${data.message}`;
                        
                        alert(message);
                        
                        // 关闭模态框并清空表单
                        const modal = bootstrap.Modal.getInstance(document.getElementById('syncByFloorModal'));
                        modal.hide();
                        document.getElementById('syncByFloorForm').reset();
                        document.getElementById('floorPersonsContainer').innerHTML = '<div class="text-muted">请先查询楼层人员</div>';
                        window.currentFloorPersonCodes = [];
                        
                        // 刷新当前页面以更新人员状态
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        alert('楼层人员同步失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    syncButton.innerHTML = originalText;
                    syncButton.disabled = false;
                    alert('楼层人员同步过程中发生错误：' + error.message);
                });
            }
        }

        // === 同步部门相关函数 ===

        // 监听楼栋部门同步模态框显示事件，自动加载楼栋列表
        document.addEventListener('DOMContentLoaded', function() {
            const syncDepartmentsModal = document.getElementById('syncDepartmentsModal');
            if (syncDepartmentsModal) {
                syncDepartmentsModal.addEventListener('show.bs.modal', function() {
                    loadDepartmentBuildingList();
                });
            }
        });

        // 加载部门同步楼栋列表
        function loadDepartmentBuildingList() {
            console.log('开始加载部门同步楼栋列表');

            const container = document.getElementById('departmentBuildingsListContainer');
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i> 正在加载楼栋列表...
                </div>
            `;

            fetch('/dormitory/buildings', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('部门同步楼栋列表响应:', data);

                if (data.ErrCode === 200) {
                    const buildings = data.data || [];
                    displayDepartmentBuildingsList(buildings);
                } else {
                    container.innerHTML = `
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.ErrMsg}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                container.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle"></i> 网络错误: ${error.message}
                    </div>
                `;
            });
        }

        // 显示部门同步楼栋列表
        function displayDepartmentBuildingsList(buildings) {
            const container = document.getElementById('departmentBuildingsListContainer');

            if (buildings.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle"></i> 暂无楼栋数据
                    </div>
                `;
                return;
            }

            let html = '';
            buildings.forEach((building, index) => {
                const buildingName = building.buildingName || building.buildingCode || '未知';
                const roomCount = building.roomCount || 0;

                html += `
                    <div class="form-check border rounded p-3 mb-2">
                        <input class="form-check-input department-building-checkbox" type="checkbox" 
                               value="${building.buildingCode}" 
                               id="dept_building_${index}">
                        <label class="form-check-label w-100" for="dept_building_${index}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong><i class="fas fa-sitemap text-secondary"></i> ${buildingName}</strong>
                                    <br>
                                    <small class="text-muted">编码: ${building.buildingCode}</small>
                                    <br>
                                    <small class="text-info">将创建部门: ${buildingName}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary">${roomCount} 间房</span>
                                </div>
                            </div>
                        </label>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 全选部门楼栋
        function selectAllDepartmentBuildings() {
            const checkboxes = document.querySelectorAll('.department-building-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        // 清空部门楼栋选择
        function clearAllDepartmentBuildings() {
            const checkboxes = document.querySelectorAll('.department-building-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        // 同步选择的楼栋为部门
        function syncSelectedDepartments() {
            const checkboxes = document.querySelectorAll('.department-building-checkbox:checked');
            const selectedBuildingCodes = Array.from(checkboxes).map(cb => cb.value);

            if (selectedBuildingCodes.length === 0) {
                alert('请至少选择一个楼栋进行部门同步！');
                return;
            }

            const buildingNames = Array.from(checkboxes).map(cb => {
                const label = cb.nextElementSibling;
                const buildingName = label.querySelector('strong').textContent.replace(/🏢\s*/, '').replace(/^\s*/, ''); // 移除图标
                return buildingName;
            });

            if (confirm(`确定要将以下 ${selectedBuildingCodes.length} 个楼栋同步为部门吗？\n\n${buildingNames.join('\n')}\n\n系统将在EGS平台中为每个楼栋创建对应的部门。`)) {
                console.log('开始同步选择的楼栋为部门:', selectedBuildingCodes);

                // 显示进度提示
                const syncButton = event.target;
                const originalText = syncButton.innerHTML;
                syncButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
                syncButton.disabled = true;

                const requestData = {
                    buildingCodes: selectedBuildingCodes
                };

                fetch('/dormitory/sync/departments', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    console.log('部门同步响应:', data);

                    // 恢复按钮状态
                    syncButton.innerHTML = originalText;
                    syncButton.disabled = false;

                    if (data.ErrCode === 200) {
                        let message = '楼栋部门同步完成！\n\n' + data.ErrMsg;
                        
                        // 如果有详细数据，显示统计信息
                        if (data.data) {
                            const stats = data.data;
                            message += `\n\n统计信息:`;
                            message += `\n- 选择楼栋: ${stats.selectedBuildings} 个`;
                            message += `\n- 实际处理: ${stats.actualBuildings} 个`;
                            message += `\n- 成功: ${stats.success} 个`;
                            message += `\n- 失败: ${stats.fail} 个`;
                            
                            if (stats.successBuildings && stats.successBuildings.length > 0) {
                                message += `\n\n成功的楼栋:\n${stats.successBuildings.join('\n')}`;
                            }
                            
                            if (stats.failBuildings && stats.failBuildings.length > 0) {
                                message += `\n\n失败的楼栋:\n${stats.failBuildings.join('\n')}`;
                            }
                        }
                        
                        alert(message);
                        
                        // 关闭模态框并清空选择
                        const modal = bootstrap.Modal.getInstance(document.getElementById('syncDepartmentsModal'));
                        modal.hide();
                        clearAllDepartmentBuildings();
                    } else {
                        alert('楼栋部门同步失败：' + data.ErrMsg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    syncButton.innerHTML = originalText;
                    syncButton.disabled = false;
                    alert('楼栋部门同步过程中发生错误：' + error.message);
                });
            }
        }

        // === 楼栋选择同步相关函数 ===

        // 监听楼栋选择同步模态框显示事件，自动加载楼栋列表
        document.addEventListener('DOMContentLoaded', function() {
            const syncBuildingsModal = document.getElementById('syncBuildingsModal');
            if (syncBuildingsModal) {
                syncBuildingsModal.addEventListener('show.bs.modal', function() {
                    loadBuildingListForSync();
                });
            }
        });

        // 加载楼栋列表（用于楼栋选择同步弹窗）
        function loadBuildingListForSync() {
            console.log('开始加载楼栋列表到同步弹窗');

            const container = document.getElementById('buildingsListContainer');
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i> 正在加载楼栋列表...
                </div>
            `;

            fetch('/dormitory/buildings', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼栋列表响应:', data);

                if (data.ErrCode === 200) {
                    const buildings = data.data || [];
                    displayBuildingsList(buildings);
                } else {
                    container.innerHTML = `
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.ErrMsg}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                container.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle"></i> 网络错误: ${error.message}
                    </div>
                `;
            });
        }

        // 显示楼栋列表
        function displayBuildingsList(buildings) {
            const container = document.getElementById('buildingsListContainer');

            if (buildings.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle"></i> 暂无楼栋数据
                    </div>
                `;
                return;
            }

            let html = '';
            buildings.forEach((building, index) => {
                const buildingName = building.buildingName || building.buildingCode || '未知';
                const roomCount = building.roomCount || 0;
                const validFloorRooms = building.validFloorRooms || 0;
                const maxFloor = building.maxFloor ? `最高${building.maxFloor}层` : '未知楼层';
                
                // 判断是否有楼层数据缺失
                const hasFloorIssue = validFloorRooms === 0 || validFloorRooms < roomCount;
                const issueClass = hasFloorIssue ? 'border-warning' : '';
                const issueIcon = hasFloorIssue ? '<i class="fas fa-exclamation-triangle text-warning" title="该楼栋存在楼层数据缺失"></i> ' : '';

                html += `
                    <div class="form-check border rounded p-3 mb-2 ${issueClass}">
                        <input class="form-check-input" type="checkbox" 
                               value="${building.buildingCode}" 
                               id="building_${index}">
                        <label class="form-check-label w-100" for="building_${index}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>${issueIcon}${buildingName}</strong>
                                    <br>
                                    <small class="text-muted">编码: ${building.buildingCode}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary">${roomCount} 间房</span>
                                    <br>
                                    <small class="text-muted">${maxFloor}</small>
                                    ${hasFloorIssue ? '<br><small class="text-warning">楼层数据缺失</small>' : ''}
                                </div>
                            </div>
                        </label>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 全选楼栋
        function selectAllBuildings() {
            const checkboxes = document.querySelectorAll('#buildingsListContainer input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        // 清空楼栋选择
        function clearAllBuildings() {
            const checkboxes = document.querySelectorAll('#buildingsListContainer input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        // 同步选择的楼栋
        function syncSelectedBuildings() {
            const checkboxes = document.querySelectorAll('#buildingsListContainer input[type="checkbox"]:checked');
            const selectedBuildingCodes = Array.from(checkboxes).map(cb => cb.value);

            if (selectedBuildingCodes.length === 0) {
                alert('请至少选择一个楼栋进行同步！');
                return;
            }

            const buildingNames = Array.from(checkboxes).map(cb => {
                const label = cb.nextElementSibling;
                const buildingName = label.querySelector('strong').textContent.replace(/^[⚠️\s]*/, ''); // 移除警告图标
                return buildingName;
            });

            if (confirm(`确定要同步以下 ${selectedBuildingCodes.length} 个楼栋吗？\n\n${buildingNames.join('\n')}\n\n系统将按楼栋汇总宿舍信息并同步到EGS平台。`)) {
                console.log('开始同步选择的楼栋:', selectedBuildingCodes);

                // 显示进度提示
                const syncButton = event.target;
                const originalText = syncButton.innerHTML;
                syncButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
                syncButton.disabled = true;

                const requestData = {
                    buildingCodes: selectedBuildingCodes
                };

                fetch('/dormitory/sync/buildings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    console.log('楼栋选择同步响应:', data);

                    // 恢复按钮状态
                    syncButton.innerHTML = originalText;
                    syncButton.disabled = false;

                    if (data.ErrCode === 200) {
                        let message = '楼栋选择同步完成！\n\n' + data.ErrMsg;
                        
                        // 如果有详细数据，显示统计信息
                        if (data.data) {
                            const stats = data.data;
                            message += `\n\n统计信息:`;
                            message += `\n- 选择楼栋: ${stats.selectedBuildings} 个`;
                            message += `\n- 实际同步: ${stats.actualBuildings} 个`;
                            message += `\n- 总房间数: ${stats.totalRooms} 间`;
                            message += `\n- 成功: ${stats.success} 个`;
                            message += `\n- 失败: ${stats.fail} 个`;
                        }
                        
                        alert(message);
                        
                        // 关闭模态框并清空选择
                        const modal = bootstrap.Modal.getInstance(document.getElementById('syncBuildingsModal'));
                        modal.hide();
                        clearAllBuildings();
                        
                        // 刷新宿舍列表
                        loadDormitoryList(currentPage, currentPageSize);
                    } else {
                        alert('楼栋选择同步失败：' + data.ErrMsg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    syncButton.innerHTML = originalText;
                    syncButton.disabled = false;
                    alert('楼栋选择同步过程中发生错误：' + error.message);
                });
            }
        }

        // ===== 楼栋人员同步相关函数 =====

        // 加载楼栋列表到选择框（用于楼栋人员同步功能）
        function loadBuildingListForSelect() {
            console.log('开始加载楼栋列表到选择框');
            
            const buildingSelect = document.getElementById('buildingSelect');
            
            // 重置选择框
            buildingSelect.innerHTML = '<option value="">正在加载...</option>';
            
            fetch('/dormitory/buildings', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼栋列表响应:', data);
                
                // 清空选择框
                buildingSelect.innerHTML = '<option value="">请选择楼栋</option>';
                
                if (data.ErrCode === 200) {
                    const buildings = data.data || [];
                    buildings.forEach(building => {
                        const option = document.createElement('option');
                        option.value = building.buildingCode;
                        option.textContent = `${building.buildingName || building.buildingCode} (${building.roomCount || 0}间房)`;
                        buildingSelect.appendChild(option);
                    });
                    
                    if (buildings.length === 0) {
                        buildingSelect.innerHTML = '<option value="">暂无楼栋数据</option>';
                    }
                } else {
                    buildingSelect.innerHTML = '<option value="">加载失败</option>';
                    alert('加载楼栋列表失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                buildingSelect.innerHTML = '<option value="">网络错误</option>';
                alert('加载楼栋列表时发生网络错误：' + error.message);
            });
        }

        // 楼栋选择变化事件
        function onBuildingChange() {
            const buildingSelect = document.getElementById('buildingSelect');
            const selectedBuilding = buildingSelect.value;
            
            // 控制按钮状态
            const loadPersonsBtn = document.getElementById('loadPersonsBtn');
            const syncBuildingBtn = document.getElementById('syncBuildingBtn');
            
            if (selectedBuilding) {
                loadPersonsBtn.disabled = false;
                syncBuildingBtn.disabled = false;
            } else {
                loadPersonsBtn.disabled = true;
                syncBuildingBtn.disabled = true;
            }
            
            // 隐藏相关卡片
            document.getElementById('buildingStatsCard').style.display = 'none';
            document.getElementById('floorSelectCard').style.display = 'none';
            document.getElementById('personsListCard').style.display = 'none';
            document.getElementById('syncResultCard').style.display = 'none';
            
            // 清空楼层选择框
            document.getElementById('floorSelect').innerHTML = '<option value="">请选择楼层</option>';
            document.getElementById('loadFloorPersonsBtn').disabled = true;
            document.getElementById('syncFloorBtn').disabled = true;
        }

        // 加载楼栋人员
        function loadBuildingPersons() {
            const buildingSelect = document.getElementById('buildingSelect');
            const selectedBuilding = buildingSelect.value;
            
            if (!selectedBuilding) {
                alert('请先选择楼栋！');
                return;
            }
            
            console.log('开始加载楼栋人员:', selectedBuilding);
            
            // 显示加载状态
            const btn = document.getElementById('loadPersonsBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';
            btn.disabled = true;
            
            // 同时加载楼栋统计信息和人员列表
            Promise.all([
                fetch(`/api/building/statistics?buildingCode=${encodeURIComponent(selectedBuilding)}`),
                fetch(`/api/building/persons?buildingCode=${encodeURIComponent(selectedBuilding)}`),
                fetch(`/api/building/floors?buildingCode=${encodeURIComponent(selectedBuilding)}`)
            ])
            .then(responses => Promise.all(responses.map(r => r.json())))
            .then(([statsData, personsData, floorsData]) => {
                console.log('楼栋统计信息:', statsData);
                console.log('楼栋人员列表:', personsData);
                console.log('楼栋楼层列表:', floorsData);
                
                // 恢复按钮状态
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                // 显示统计信息
                if (statsData.ErrCode === 200) {
                    displayBuildingStats(statsData.data);
                }
                
                // 显示人员列表
                if (personsData.ErrCode === 200) {
                    displayPersonsList(personsData.data, personsData.total || 0);
                } else {
                    alert('加载人员列表失败：' + personsData.ErrMsg);
                }
                
                // 显示楼层选择
                if (floorsData.ErrCode === 200) {
                    displayFloorsList(floorsData.data);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                btn.innerHTML = originalText;
                btn.disabled = false;
                alert('加载楼栋人员时发生错误：' + error.message);
            });
        }

        // 显示楼栋统计信息
        function displayBuildingStats(stats) {
            const statsContent = document.getElementById('buildingStatsContent');
            const statsCard = document.getElementById('buildingStatsCard');
            
            const html = `
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">${stats.totalPersons || 0}</h5>
                            <p class="card-text">总人数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">${stats.totalDormitories || 0}</h5>
                            <p class="card-text">宿舍间数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">${stats.totalFloors || 0}</h5>
                            <p class="card-text">楼层数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">${stats.buildingCode || '未知'}</h5>
                            <p class="card-text">楼栋编码</p>
                        </div>
                    </div>
                </div>
            `;
            
            statsContent.innerHTML = html;
            statsCard.style.display = 'block';
        }

        // 显示楼层列表
        function displayFloorsList(floors) {
            const floorSelect = document.getElementById('floorSelect');
            const floorCard = document.getElementById('floorSelectCard');
            
            // 清空楼层选择框
            floorSelect.innerHTML = '<option value="">请选择楼层</option>';
            
            if (floors && floors.length > 0) {
                floors.forEach(floor => {
                    const option = document.createElement('option');
                    option.value = floor.floor;
                    option.textContent = `第${floor.floor}层 (${floor.personCount || 0}人)`;
                    floorSelect.appendChild(option);
                });
                
                floorCard.style.display = 'block';
            } else {
                floorCard.style.display = 'none';
            }
        }

        // 显示人员列表
        function displayPersonsList(persons, total) {
            const tableBody = document.getElementById('buildingPersonsTableBody');
            const personsCard = document.getElementById('personsListCard');
            const countBadge = document.getElementById('personsCountBadge');
            
            // 更新人员数量
            countBadge.textContent = total;
            
            if (!persons || persons.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center text-muted">
                            <i class="fas fa-info-circle"></i> 该楼栋暂无人员信息
                        </td>
                    </tr>
                `;
            } else {
                let html = '';
                persons.forEach((person, index) => {
                    html += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${person.personName || '未知'}</td>
                            <td>${person.personCode || '未知'}</td>
                            <td>${person.dormitoryCode || '未知'}</td>
                            <td>${person.dormitoryName || '未知'}</td>
                            <td>${person.floor || '未知'}</td>
                            <td>${person.roomNumber || '未知'}</td>
                            <td>${person.gender === 1 ? '男' : (person.gender === 0 ? '女' : '未知')}</td>
                            <td>${person.telephone || '未知'}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-success" onclick="syncSinglePerson('${person.personCode}')">
                                    <i class="fas fa-sync"></i> 同步
                                </button>
                            </td>
                        </tr>
                    `;
                });
                tableBody.innerHTML = html;
            }
            
            personsCard.style.display = 'block';
        }

        // 楼层选择变化事件
        function onFloorChange() {
            const floorSelect = document.getElementById('floorSelect');
            const selectedFloor = floorSelect.value;
            
            // 控制楼层操作按钮状态
            const loadFloorPersonsBtn = document.getElementById('loadFloorPersonsBtn');
            const syncFloorBtn = document.getElementById('syncFloorBtn');
            
            if (selectedFloor) {
                loadFloorPersonsBtn.disabled = false;
                syncFloorBtn.disabled = false;
            } else {
                loadFloorPersonsBtn.disabled = true;
                syncFloorBtn.disabled = true;
            }
        }

        // 加载楼层人员
        function loadFloorPersons() {
            const buildingSelect = document.getElementById('buildingSelect');
            const floorSelect = document.getElementById('floorSelect');
            const selectedBuilding = buildingSelect.value;
            const selectedFloor = floorSelect.value;
            
            if (!selectedBuilding || !selectedFloor) {
                alert('请先选择楼栋和楼层！');
                return;
            }
            
            console.log('开始加载楼层人员:', selectedBuilding, selectedFloor);
            
            // 显示加载状态
            const btn = document.getElementById('loadFloorPersonsBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';
            btn.disabled = true;
            
            fetch(`/api/building/floor/persons?buildingCode=${encodeURIComponent(selectedBuilding)}&floor=${selectedFloor}`)
            .then(response => response.json())
            .then(data => {
                console.log('楼层人员列表:', data);
                
                // 恢复按钮状态
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                if (data.ErrCode === 200) {
                    displayPersonsList(data.data, data.total || 0);
                } else {
                    alert('加载楼层人员失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                btn.innerHTML = originalText;
                btn.disabled = false;
                alert('加载楼层人员时发生错误：' + error.message);
            });
        }

        // 同步楼栋人员
        function syncBuildingPersons() {
            const buildingSelect = document.getElementById('buildingSelect');
            const selectedBuilding = buildingSelect.value;
            
            if (!selectedBuilding) {
                alert('请先选择楼栋！');
                return;
            }
            
            const buildingText = buildingSelect.options[buildingSelect.selectedIndex].text;
            
            if (!confirm(`确定要同步楼栋 "${buildingText}" 的所有人员到EGS平台吗？\n\n同步过程可能需要一些时间，请耐心等待。`)) {
                return;
            }
            
            console.log('开始同步楼栋人员:', selectedBuilding);
            
            // 显示同步状态
            const btn = document.getElementById('syncBuildingBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
            btn.disabled = true;
            
            fetch(`/api/building/sync?buildingCode=${encodeURIComponent(selectedBuilding)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼栋人员同步响应:', data);
                
                // 恢复按钮状态
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                if (data.ErrCode === 200) {
                    displaySyncResult(data.data, '楼栋人员同步');
                    // 重新加载人员列表以更新同步状态
                    loadBuildingPersons();
                } else {
                    alert('楼栋人员同步失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                btn.innerHTML = originalText;
                btn.disabled = false;
                alert('楼栋人员同步时发生错误：' + error.message);
            });
        }

        // 同步楼层人员
        function syncFloorPersons() {
            const buildingSelect = document.getElementById('buildingSelect');
            const floorSelect = document.getElementById('floorSelect');
            const selectedBuilding = buildingSelect.value;
            const selectedFloor = floorSelect.value;
            
            if (!selectedBuilding || !selectedFloor) {
                alert('请先选择楼栋和楼层！');
                return;
            }
            
            const buildingText = buildingSelect.options[buildingSelect.selectedIndex].text;
            const floorText = floorSelect.options[floorSelect.selectedIndex].text;
            
            if (!confirm(`确定要同步楼栋 "${buildingText}" 的 "${floorText}" 人员到EGS平台吗？\n\n同步过程可能需要一些时间，请耐心等待。`)) {
                return;
            }
            
            console.log('开始同步楼层人员:', selectedBuilding, selectedFloor);
            
            // 显示同步状态
            const btn = document.getElementById('syncFloorBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
            btn.disabled = true;
            
            fetch(`/api/building/floor/sync?buildingCode=${encodeURIComponent(selectedBuilding)}&floor=${selectedFloor}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼层人员同步响应:', data);
                
                // 恢复按钮状态
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                if (data.ErrCode === 200) {
                    displaySyncResult(data.data, '楼层人员同步');
                    // 重新加载楼层人员以更新同步状态
                    loadFloorPersons();
                } else {
                    alert('楼层人员同步失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                btn.innerHTML = originalText;
                btn.disabled = false;
                alert('楼层人员同步时发生错误：' + error.message);
            });
        }

        // 显示同步结果
        function displaySyncResult(result, syncType) {
            const resultContent = document.getElementById('syncResultContent');
            const resultCard = document.getElementById('syncResultCard');
            
            const successCount = result.successCount || 0;
            const failCount = result.failCount || 0;
            const totalCount = successCount + failCount;
            const successPersons = result.successPersons || [];
            const failPersons = result.failPersons || [];
            
            let html = `
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">${totalCount}</h5>
                                <p class="card-text">总计处理</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">${successCount}</h5>
                                <p class="card-text">成功同步</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">${failCount}</h5>
                                <p class="card-text">同步失败</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">${((successCount / totalCount) * 100 || 0).toFixed(1)}%</h5>
                                <p class="card-text">成功率</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            if (failCount > 0 && failPersons.length > 0) {
                html += `
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> 同步失败的人员：</h6>
                        <ul class="mb-0">
                `;
                failPersons.forEach(person => {
                    html += `<li>${person.personName} (${person.personCode}) - ${person.error || '未知错误'}</li>`;
                });
                html += `
                        </ul>
                    </div>
                `;
            }
            
            if (successCount > 0) {
                html += `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> ${syncType}完成！成功同步 ${successCount} 人，失败 ${failCount} 人。
                    </div>
                `;
            }
            
            resultContent.innerHTML = html;
            resultCard.style.display = 'block';
            
            // 滚动到结果区域
            resultCard.scrollIntoView({ behavior: 'smooth' });
        }

        // 同步单个人员
        function syncSinglePerson(personCode) {
            if (!personCode) {
                alert('人员编码不能为空！');
                return;
            }
            
            if (!confirm(`确定要同步人员 "${personCode}" 到EGS平台吗？`)) {
                return;
            }
            
            console.log('开始同步单个人员:', personCode);
            
            // 这里可以调用单个人员同步的API
            // 暂时使用现有的同步接口，实际应该有专门的单个人员同步接口
            alert('单个人员同步功能开发中...');
        }

        // 页面加载完成后自动加载楼栋列表
        document.addEventListener('DOMContentLoaded', function() {
            // 当切换到楼栋人员同步标签时自动加载楼栋列表
            const buildingSyncTab = document.getElementById('building-person-sync-tab');
            if (buildingSyncTab) {
                buildingSyncTab.addEventListener('click', function() {
                    // 延迟加载，确保标签页已切换
                    setTimeout(() => {
                        loadBuildingListForSelect();
                    }, 100);
                });
            }
        });
        
        // === 按楼栋同步人员相关函数 ===

        // 监听按楼栋同步人员模态框显示事件
        document.addEventListener('DOMContentLoaded', function() {
            const syncPersonsByBuildingModal = document.getElementById('syncPersonsByBuildingModal');
            if (syncPersonsByBuildingModal) {
                syncPersonsByBuildingModal.addEventListener('show.bs.modal', function() {
                    loadBuildingPersonStructure();
                });
            }
        });

        // 加载楼栋人员结构信息
        function loadBuildingPersonStructure() {
            console.log('开始加载楼栋人员结构信息');

            const container = document.getElementById('buildingPersonStructureContainer');
            container.innerHTML = `
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            `;

            fetch('/api/person/buildings/structure', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼栋人员结构响应:', data);

                if (data.success) {
                    const buildings = data.data || [];
                    displayBuildingPersonStructure(buildings);
                } else {
                    container.innerHTML = `
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                container.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle"></i> 网络错误: ${error.message}
                    </div>
                `;
            });
        }

        // 显示楼栋人员结构
        function displayBuildingPersonStructure(buildings) {
            const container = document.getElementById('buildingPersonStructureContainer');

            if (buildings.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle"></i> 暂无楼栋人员数据
                    </div>
                `;
                return;
            }

            let html = '';
            buildings.forEach((building, buildingIndex) => {
                const buildingName = building.buildingName || building.buildingCode || '未知';
                const totalPersons = building.totalPersons || 0;
                const floors = building.floors || [];
                
                // 楼栋级别的复选框
                html += `
                    <div class="card mb-3">
                        <div class="card-header" data-bs-toggle="collapse" data-bs-target="#building_${buildingIndex}" style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <input type="checkbox" class="form-check-input me-2 building-checkbox" 
                                           data-building-code="${building.buildingCode}" 
                                           data-building-index="${buildingIndex}"
                                           onclick="toggleBuildingSelection(this, event)">
                                    <strong>${buildingName}</strong>
                                    <small class="text-muted ms-2">编码: ${building.buildingCode}</small>
                                </div>
                                <div>
                                    <span class="badge bg-success">${totalPersons} 人</span>
                                    <span class="badge bg-info ms-1">${floors.length} 层</span>
                                    <i class="fas fa-chevron-down ms-2"></i>
                                </div>
                            </div>
                        </div>
                        <div class="collapse" id="building_${buildingIndex}">
                            <div class="card-body">
                `;
                
                // 楼层列表
                if (floors.length > 0) {
                    floors.forEach((floor, floorIndex) => {
                        const floorName = floor.floorName || '未分层';
                        const personCount = floor.personCount || 0;
                        const dormitoryCount = floor.dormitoryCount || 0;
                        
                        html += `
                            <div class="form-check mb-2">
                                <input class="form-check-input floor-checkbox" type="checkbox" 
                                       data-building-code="${building.buildingCode}" 
                                       data-building-index="${buildingIndex}"
                                       data-floor="${floor.floor}"
                                       value="${floor.floor}"
                                       id="floor_${buildingIndex}_${floorIndex}"
                                       onclick="updateBuildingCheckboxState(${buildingIndex})">
                                <label class="form-check-label w-100" for="floor_${buildingIndex}_${floorIndex}">
                                    <div class="d-flex justify-content-between">
                                        <span>${floorName}</span>
                                        <div>
                                            <span class="badge bg-outline-success">${personCount} 人</span>
                                            <span class="badge bg-outline-secondary ms-1">${dormitoryCount} 间</span>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        `;
                    });
                } else {
                    html += `
                        <div class="text-center text-muted">
                            <i class="fas fa-info-circle"></i> 该楼栋暂无楼层数据
                        </div>
                    `;
                }
                
                html += `
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 切换楼栋选择状态
        function toggleBuildingSelection(buildingCheckbox, event) {
            event.stopPropagation(); // 阻止冒泡，避免触发collapse
            
            const buildingIndex = buildingCheckbox.dataset.buildingIndex;
            const floorCheckboxes = document.querySelectorAll(`[data-building-index="${buildingIndex}"].floor-checkbox`);
            
            // 设置所有楼层复选框状态
            floorCheckboxes.forEach(checkbox => {
                checkbox.checked = buildingCheckbox.checked;
            });
        }

        // 更新楼栋复选框状态
        function updateBuildingCheckboxState(buildingIndex) {
            const buildingCheckbox = document.querySelector(`[data-building-index="${buildingIndex}"].building-checkbox`);
            const floorCheckboxes = document.querySelectorAll(`[data-building-index="${buildingIndex}"].floor-checkbox`);
            
            const checkedFloors = Array.from(floorCheckboxes).filter(cb => cb.checked);
            
            if (checkedFloors.length === 0) {
                buildingCheckbox.checked = false;
                buildingCheckbox.indeterminate = false;
            } else if (checkedFloors.length === floorCheckboxes.length) {
                buildingCheckbox.checked = true;
                buildingCheckbox.indeterminate = false;
            } else {
                buildingCheckbox.checked = false;
                buildingCheckbox.indeterminate = true;
            }
        }

        // 全选楼层
        function selectAllFloors() {
            const floorCheckboxes = document.querySelectorAll('.floor-checkbox');
            floorCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            
            // 更新所有楼栋复选框状态
            const buildingCheckboxes = document.querySelectorAll('.building-checkbox');
            buildingCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                checkbox.indeterminate = false;
            });
        }

        // 清除所有选择
        function clearAllFloors() {
            const floorCheckboxes = document.querySelectorAll('.floor-checkbox');
            const buildingCheckboxes = document.querySelectorAll('.building-checkbox');
            
            floorCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            
            buildingCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                checkbox.indeterminate = false;
            });
        }

        // 预览选中人员
        function previewSelectedPersons() {
            const selectedFloors = getSelectedFloorsData();
            
            if (selectedFloors.length === 0) {
                alert('请先选择要预览的楼栋和楼层！');
                return;
            }
            
            console.log('开始预览选中人员:', selectedFloors);
            
            const container = document.getElementById('personPreviewContainer');
            container.innerHTML = `
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">正在查询人员...</span>
                    </div>
                </div>
            `;
            
            // 逐个楼栋查询人员
            Promise.all(selectedFloors.map(item => {
                return fetch('/api/person/buildings/preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        buildingCode: item.buildingCode,
                        floors: item.floors
                    })
                }).then(response => response.json());
            }))
            .then(responses => {
                console.log('人员预览响应:', responses);
                
                let allPersons = [];
                let successCount = 0;
                
                responses.forEach(data => {
                    if (data.success) {
                        allPersons = allPersons.concat(data.data || []);
                        successCount++;
                    }
                });
                
                displayPersonPreview(allPersons, selectedFloors, successCount);
            })
            .catch(error => {
                console.error('Error:', error);
                container.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle"></i> 查询人员失败: ${error.message}
                    </div>
                `;
            });
        }

        // 显示人员预览
        function displayPersonPreview(persons, selectedFloors, successCount) {
            const container = document.getElementById('personPreviewContainer');
            const summaryContainer = document.getElementById('personPreviewSummary');
            const summaryText = document.getElementById('previewSummaryText');
            
            if (persons.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-user-slash fa-2x mb-2"></i>
                        <p>选中的楼栋/楼层中没有找到人员数据</p>
                    </div>
                `;
                summaryContainer.classList.add('d-none');
                return;
            }
            
            // 更新统计信息
            const buildingCount = selectedFloors.length;
            const floorCount = selectedFloors.reduce((sum, item) => sum + item.floors.length, 0);
            summaryText.textContent = `选中 ${buildingCount} 个楼栋，${floorCount} 个楼层，${persons.length} 名人员`;
            summaryContainer.classList.remove('d-none');
            
            // 显示人员列表
            let html = `
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>姓名</th>
                                <th>编码</th>
                                <th>房间</th>
                                <th>楼层</th>
                                <th>同步状态</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            persons.forEach(person => {
                const syncStatus = person.syncFlag === 1 ? 
                    '<span class="badge bg-success">已同步</span>' : 
                    '<span class="badge bg-warning">未同步</span>';
                    
                html += `
                    <tr>
                        <td>${person.personName || ''}</td>
                        <td><small>${person.personCode || ''}</small></td>
                        <td><small>${person.roomName || ''}</small></td>
                        <td><small>${person.floor || ''}层</small></td>
                        <td>${syncStatus}</td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 获取选中的楼层数据
        function getSelectedFloorsData() {
            const selectedFloors = [];
            const checkedFloorCheckboxes = document.querySelectorAll('.floor-checkbox:checked');
            
            // 按楼栋分组
            const buildingGroups = {};
            checkedFloorCheckboxes.forEach(checkbox => {
                const buildingCode = checkbox.dataset.buildingCode;
                const floor = parseInt(checkbox.dataset.floor);
                
                if (!buildingGroups[buildingCode]) {
                    buildingGroups[buildingCode] = [];
                }
                buildingGroups[buildingCode].push(floor);
            });
            
            // 转换为数组格式
            Object.keys(buildingGroups).forEach(buildingCode => {
                selectedFloors.push({
                    buildingCode: buildingCode,
                    floors: buildingGroups[buildingCode].filter(floor => !isNaN(floor)) // 过滤掉NaN值
                });
            });
            
            return selectedFloors;
        }

        // 开始按楼栋同步人员
        function syncSelectedPersonsByBuilding() {
            const selectedFloors = getSelectedFloorsData();
            
            if (selectedFloors.length === 0) {
                alert('请先选择要同步的楼栋和楼层！');
                return;
            }
            
            const buildingNames = selectedFloors.map(item => {
                const checkbox = document.querySelector(`[data-building-code="${item.buildingCode}"].building-checkbox`);
                return checkbox ? checkbox.parentElement.querySelector('strong').textContent : item.buildingCode;
            });
            
            const totalFloors = selectedFloors.reduce((sum, item) => sum + item.floors.length, 0);
            
            if (confirm(`确定要按楼栋同步人员吗？\n\n选中楼栋: ${buildingNames.join(', ')}\n选中楼层: ${totalFloors} 个\n\n同步过程可能需要较长时间，请耐心等待。`)) {
                console.log('开始按楼栋同步人员:', selectedFloors);
                
                const syncButton = document.getElementById('syncPersonsByBuildingBtn');
                const originalText = syncButton.innerHTML;
                syncButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
                syncButton.disabled = true;
                
                // 逐个楼栋进行同步
                syncBuildingsSequentially(selectedFloors, 0, [], syncButton, originalText);
            }
        }

        // 顺序同步楼栋
        function syncBuildingsSequentially(selectedFloors, index, results, syncButton, originalText) {
            if (index >= selectedFloors.length) {
                // 所有楼栋同步完成
                handleAllBuildingsSyncComplete(results, syncButton, originalText);
                return;
            }
            
            const currentBuilding = selectedFloors[index];
            
            fetch('/api/person/buildings/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(currentBuilding)
            })
            .then(response => response.json())
            .then(data => {
                console.log(`楼栋${currentBuilding.buildingCode}同步响应:`, data);
                results.push({
                    building: currentBuilding,
                    result: data
                });
                
                // 继续下一个楼栋
                syncBuildingsSequentially(selectedFloors, index + 1, results, syncButton, originalText);
            })
            .catch(error => {
                console.error(`楼栋${currentBuilding.buildingCode}同步错误:`, error);
                results.push({
                    building: currentBuilding,
                    result: {
                        success: false,
                        message: `网络错误: ${error.message}`
                    }
                });
                
                // 继续下一个楼栋
                syncBuildingsSequentially(selectedFloors, index + 1, results, syncButton, originalText);
            });
        }

        // 处理所有楼栋同步完成
        function handleAllBuildingsSyncComplete(results, syncButton, originalText) {
            // 恢复按钮状态
            syncButton.innerHTML = originalText;
            syncButton.disabled = false;
            
            // 统计结果
            let totalSuccess = 0;
            let totalFail = 0;
            let totalPersons = 0;
            let successBuildings = 0;
            let failBuildings = 0;
            
            results.forEach(item => {
                if (item.result.success) {
                    successBuildings++;
                    totalSuccess += item.result.successCount || 0;
                    totalFail += item.result.failCount || 0;
                    totalPersons += item.result.totalCount || 0;
                } else {
                    failBuildings++;
                }
            });
            
            // 构造结果消息
            let message = `按楼栋同步人员完成！\n\n`;
            message += `楼栋统计:\n`;
            message += `- 成功楼栋: ${successBuildings} 个\n`;
            message += `- 失败楼栋: ${failBuildings} 个\n`;
            message += `\n人员统计:\n`;
            message += `- 总计人员: ${totalPersons} 人\n`;
            message += `- 同步成功: ${totalSuccess} 人\n`;
            message += `- 同步失败: ${totalFail} 人`;
            
            // 如果有失败的楼栋，显示详情
            const failedBuildings = results.filter(item => !item.result.success);
            if (failedBuildings.length > 0) {
                message += `\n\n失败楼栋详情:\n`;
                failedBuildings.forEach(item => {
                    message += `- ${item.building.buildingCode}: ${item.result.message}\n`;
                });
            }
            
            alert(message);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('syncPersonsByBuildingModal'));
            modal.hide();
            
            // 清空选择
            clearAllFloors();
            
            // 刷新人员列表
            window.location.reload();
        }

        // === 按楼栋绑定功能相关函数 ===

        // 全局变量保存当前选中的楼栋
        let currentSelectedBuildingForBind = null;
        let currentSelectedFloorForBind = null;

        // 打开按楼栋绑定模态框
        function openBuildingBindModal() {
            console.log('打开按楼栋绑定模态框');
            
            // 重置状态
            currentSelectedBuildingForBind = null;
            currentSelectedFloorForBind = null;
            
            // 隐藏相关区域
            document.getElementById('buildingStatsForBind').style.display = 'none';
            document.getElementById('floorSelectForBind').style.display = 'none';
            document.getElementById('roomPersonsForBind').style.display = 'none';
            
            // 加载楼栋列表
            loadBuildingStatsForBind();
        }

        // 加载楼栋列表到选择器
        function loadBuildingStatsForBind() {
            console.log('开始加载楼栋列表到绑定选择器');
            
            const buildingSelect = document.getElementById('buildingSelectForBind');
            buildingSelect.innerHTML = '<option value="">正在加载...</option>';
            
            fetch('/dormitory/buildings', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼栋列表响应:', data);
                
                buildingSelect.innerHTML = '<option value="">请选择楼栋</option>';
                
                if (data.ErrCode === 200) {
                    const buildings = data.data || [];
                    buildings.forEach(building => {
                        const option = document.createElement('option');
                        option.value = building.buildingCode;
                        option.textContent = `${building.buildingName || building.buildingCode} (${building.roomCount || 0}间房)`;
                        buildingSelect.appendChild(option);
                    });
                    
                    if (buildings.length === 0) {
                        buildingSelect.innerHTML = '<option value="">暂无楼栋数据</option>';
                    }
                } else {
                    buildingSelect.innerHTML = '<option value="">加载失败</option>';
                    alert('加载楼栋列表失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                buildingSelect.innerHTML = '<option value="">网络错误</option>';
                alert('加载楼栋列表时发生网络错误：' + error.message);
            });
        }

        // 楼栋选择处理
        function selectBuildingForBind() {
            const buildingSelect = document.getElementById('buildingSelectForBind');
            const selectedBuilding = buildingSelect.value;
            
            if (!selectedBuilding) {
                // 隐藏相关区域
                document.getElementById('buildingStatsForBind').style.display = 'none';
                document.getElementById('floorSelectForBind').style.display = 'none';
                document.getElementById('roomPersonsForBind').style.display = 'none';
                currentSelectedBuildingForBind = null;
                currentSelectedFloorForBind = null;
                return;
            }
            
            currentSelectedBuildingForBind = selectedBuilding;
            currentSelectedFloorForBind = null;
            
            console.log('选择楼栋进行绑定:', selectedBuilding);
            
            // 加载楼栋统计信息
            loadBuildingStatsData(selectedBuilding);
        }

        // 加载楼栋统计数据
        function loadBuildingStatsData(buildingCode) {
            fetch(`/dormitory/building-stats/${buildingCode}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼栋统计信息响应:', data);
                
                if (data.ErrCode === 200) {
                    displayBuildingStatsForBind(data.data);
                } else {
                    alert('加载楼栋统计信息失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('加载楼栋统计信息时发生网络错误：' + error.message);
            });
        }

        // 显示楼栋统计信息
        function displayBuildingStatsForBind(stats) {
            const statsContent = document.getElementById('buildingStatsContentForBind');
            const statsCard = document.getElementById('buildingStatsForBind');
            
            const html = `
                <div class="col-md-3">
                    <div class="card text-center border-primary">
                        <div class="card-body">
                            <h5 class="card-title text-primary">${stats.buildingName || stats.buildingCode}</h5>
                            <p class="card-text">楼栋名称</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-info">
                        <div class="card-body">
                            <h5 class="card-title text-info">${stats.totalFloors || 0}</h5>
                            <p class="card-text">楼层数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-success">
                        <div class="card-body">
                            <h5 class="card-title text-success">${stats.totalRooms || 0}</h5>
                            <p class="card-text">房间数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center border-warning">
                        <div class="card-body">
                            <h5 class="card-title text-warning">${stats.totalPersons || 0}</h5>
                            <p class="card-text">人员数</p>
                        </div>
                    </div>
                </div>
            `;
            
            statsContent.innerHTML = html;
            statsCard.style.display = 'block';
            
            // 显示楼层选择
            displayFloorsForBind(stats.floors || []);
        }

        // 显示楼层选择按钮
        function displayFloorsForBind(floors) {
            const floorButtonsContainer = document.getElementById('floorButtonsForBind');
            const floorCard = document.getElementById('floorSelectForBind');
            
            if (floors.length === 0) {
                floorButtonsContainer.innerHTML = '<div class="text-muted">该楼栋暂无楼层数据</div>';
                floorCard.style.display = 'block';
                return;
            }
            
            let html = '<div class="d-flex flex-wrap gap-2">';
            floors.forEach(floor => {
                const floorNum = floor.floor;
                const roomCount = floor.roomCount || 0;
                const personCount = floor.personCount || 0;
                
                html += `
                    <button type="button" class="btn btn-outline-primary btn-floor-bind" 
                            data-floor="${floorNum}" onclick="loadFloorRoomsForBind(${floorNum})">
                        <div class="text-center">
                            <div class="fw-bold">${floorNum}层</div>
                            <small>${roomCount}房 · ${personCount}人</small>
                        </div>
                    </button>
                `;
            });
            html += '</div>';
            
            floorButtonsContainer.innerHTML = html;
            floorCard.style.display = 'block';
            
            // 隐藏房间信息
            document.getElementById('roomPersonsForBind').style.display = 'none';
        }

        // 加载楼层房间信息
        function loadFloorRoomsForBind(floor) {
            if (!currentSelectedBuildingForBind) {
                alert('请先选择楼栋');
                return;
            }
            
            console.log('加载楼层房间信息:', currentSelectedBuildingForBind, floor);
            
            // 更新当前选中楼层
            currentSelectedFloorForBind = floor;
            
            // 更新楼层按钮状态
            document.querySelectorAll('.btn-floor-bind').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline-primary');
            });
            document.querySelector(`[data-floor="${floor}"]`).classList.remove('btn-outline-primary');
            document.querySelector(`[data-floor="${floor}"]`).classList.add('btn-primary');
            
            // 更新楼层信息显示
            document.getElementById('selectedFloorInfoForBind').textContent = `(${currentSelectedBuildingForBind} - ${floor}层)`;
            
            // 显示加载状态
            const roomPersonsCard = document.getElementById('roomPersonsForBind');
            const tableBody = document.getElementById('roomPersonsTableForBind');
            tableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> 正在加载房间信息...
                    </td>
                </tr>
            `;
            roomPersonsCard.style.display = 'block';
            
            // 加载房间数据
            fetch(`/dormitory/floor-rooms?buildingCode=${encodeURIComponent(currentSelectedBuildingForBind)}&floor=${floor}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼层房间信息响应:', data);
                
                if (data.ErrCode === 200) {
                    displayRoomPersonsForBind(data.data || []);
                } else {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="text-center text-danger">
                                加载失败: ${data.ErrMsg}
                            </td>
                        </tr>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center text-danger">
                            网络错误: ${error.message}
                        </td>
                    </tr>
                `;
            });
        }

        // 显示房间人员分配
        function displayRoomPersonsForBind(rooms) {
            const tableBody = document.getElementById('roomPersonsTableForBind');
            
            if (rooms.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center text-muted">
                            该楼层暂无房间数据
                        </td>
                    </tr>
                `;
                return;
            }
            
            let html = '';
            rooms.forEach(room => {
                const roomName = room.roomName || room.roomNum || '未知';
                const bedCount = room.bedCount || 0;
                const currentPersonCount = room.currentPersonCount || 0;
                const persons = room.persons || [];
                
                // 构建人员列表显示
                let personsDisplay = '';
                if (persons.length === 0) {
                    personsDisplay = '<span class="text-muted">暂无人员</span>';
                } else {
                    const personNames = persons.map(p => `${p.personName}(${p.personCode})`);
                    if (personNames.length <= 3) {
                        personsDisplay = personNames.join(', ');
                    } else {
                        personsDisplay = personNames.slice(0, 2).join(', ') + ` 等${personNames.length}人`;
                    }
                }
                
                // 房间状态
                const statusBadge = currentPersonCount === 0 ? 
                    '<span class="badge bg-secondary">空房</span>' :
                    currentPersonCount >= bedCount ? 
                        '<span class="badge bg-success">已满</span>' :
                        '<span class="badge bg-warning">部分入住</span>';
                
                html += `
                    <tr>
                        <td>
                            <strong>${roomName}</strong>
                            ${statusBadge}
                        </td>
                        <td>${bedCount}</td>
                        <td>${currentPersonCount}</td>
                        <td>
                            <small>${personsDisplay}</small>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-success" 
                                    onclick="bindSingleRoom('${roomName}')"
                                    ${currentPersonCount === 0 ? 'disabled' : ''}>
                                <i class="fas fa-link"></i> 绑定房间
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }

        // 绑定整个楼层
        function bindSelectedFloor() {
            if (!currentSelectedBuildingForBind || !currentSelectedFloorForBind) {
                alert('请先选择楼栋和楼层');
                return;
            }
            
            if (!confirm(`确定要绑定楼栋 ${currentSelectedBuildingForBind} 第 ${currentSelectedFloorForBind} 层的所有人员到EGS平台吗？\n\n绑定过程可能需要一些时间，请耐心等待。`)) {
                return;
            }
            
            console.log('开始绑定楼层:', currentSelectedBuildingForBind, currentSelectedFloorForBind);
            
            // 显示绑定状态
            const btn = document.getElementById('bindFloorBtnForBind');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 绑定中...';
            btn.disabled = true;
            
            fetch('/dormitory/bind-building-floor', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    buildingCode: currentSelectedBuildingForBind,
                    floor: currentSelectedFloorForBind
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼层绑定响应:', data);
                
                // 恢复按钮状态
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                if (data.ErrCode === 200) {
                    alert('楼层绑定成功！\n\n' + data.ErrMsg);
                    // 重新加载房间信息
                    loadFloorRoomsForBind(currentSelectedFloorForBind);
                } else {
                    alert('楼层绑定失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                btn.innerHTML = originalText;
                btn.disabled = false;
                alert('楼层绑定过程中发生错误：' + error.message);
            });
        }

        // 绑定单个房间
        function bindSingleRoom(roomName) {
            if (!currentSelectedBuildingForBind || !currentSelectedFloorForBind) {
                alert('请先选择楼栋和楼层');
                return;
            }
            
            if (!confirm(`确定要绑定房间 ${roomName} 的人员到EGS平台吗？`)) {
                return;
            }
            
            console.log('开始绑定房间:', currentSelectedBuildingForBind, currentSelectedFloorForBind, roomName);
            
            // 显示绑定状态（这里简化处理，实际可以更精细）
            const bindButtons = document.querySelectorAll('button[onclick*="bindSingleRoom"]');
            bindButtons.forEach(btn => btn.disabled = true);
            
            fetch('/dormitory/bind-room', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    buildingCode: currentSelectedBuildingForBind,
                    floor: currentSelectedFloorForBind,
                    roomName: roomName
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('房间绑定响应:', data);
                
                // 恢复按钮状态
                bindButtons.forEach(btn => btn.disabled = false);
                
                if (data.ErrCode === 200) {
                    alert('房间绑定成功！\n\n' + data.ErrMsg);
                    // 重新加载房间信息
                    loadFloorRoomsForBind(currentSelectedFloorForBind);
                } else {
                    alert('房间绑定失败：' + data.ErrMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                bindButtons.forEach(btn => btn.disabled = false);
                alert('房间绑定过程中发生错误：' + error.message);
            });
        }

        // ==================== 定时任务管理相关函数 ====================

        // 加载任务仪表板数据
        function loadTaskDashboard() {
            console.log('开始加载任务仪表板数据...');

            fetch('/task-management/api/dashboard')
                .then(response => {
                    console.log('仪表板API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('仪表板数据:', data);

                    if (data.success !== false) {
                        const totalTasksEl = document.getElementById('totalTasks');
                        const enabledTasksEl = document.getElementById('enabledTasks');
                        const runningTasksEl = document.getElementById('runningTasks');
                        const disabledTasksEl = document.getElementById('disabledTasks');

                        if (totalTasksEl) totalTasksEl.textContent = data.totalTasks || 0;
                        if (enabledTasksEl) enabledTasksEl.textContent = data.enabledTasks || 0;
                        if (runningTasksEl) runningTasksEl.textContent = data.runningTasks || 0;
                        if (disabledTasksEl) disabledTasksEl.textContent = data.disabledTasks || 0;

                        updateAlertTasks(data);
                    } else {
                        console.error('仪表板数据加载失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('加载仪表板数据失败:', error);
                    // 显示错误状态
                    const elements = ['totalTasks', 'enabledTasks', 'runningTasks', 'disabledTasks'];
                    elements.forEach(id => {
                        const el = document.getElementById(id);
                        if (el) el.textContent = '错误';
                    });
                });
        }

        // 加载任务列表
        function loadTasks() {
            console.log('开始加载任务列表...');

            const tbody = document.getElementById('taskTableBody');
            if (tbody) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</td></tr>';
            }

            fetch('/task-management/api/tasks')
                .then(response => {
                    console.log('任务列表API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('任务列表数据:', data);

                    if (data.success) {
                        displayTasks(data.data);
                    } else {
                        showTaskError('加载任务列表失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('加载任务列表失败:', error);
                    showTaskError('网络错误: ' + error.message);
                });
        }

        // 显示任务列表
        function displayTasks(tasks) {
            console.log('显示任务列表，任务数量:', tasks ? tasks.length : 0);

            const tbody = document.getElementById('taskTableBody');
            if (!tbody) {
                console.error('找不到任务表格元素');
                return;
            }

            if (!tasks || tasks.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无任务</td></tr>';
                return;
            }

            let html = '';
            tasks.forEach(task => {
                const enabledBadge = task.enabled
                    ? '<span class="badge bg-success">启用</span>'
                    : '<span class="badge bg-secondary">禁用</span>';

                const statusBadge = getTaskStatusBadge(task.lastExecutionStatus);
                const successRate = task.executionCount > 0
                    ? Math.round((task.successCount / task.executionCount) * 100) + '%'
                    : '-';

                const lastExecution = task.lastExecutionTime
                    ? new Date(task.lastExecutionTime).toLocaleString()
                    : '从未执行';

                html += `
                    <tr onclick="showTaskDetail('${task.taskName}')" style="cursor: pointer;">
                        <td>
                            <strong>${task.taskName || '未知任务'}</strong>
                            <br><small class="text-muted">${task.taskClass || ''}</small>
                        </td>
                        <td>${task.taskDescription || '-'}</td>
                        <td>${enabledBadge}</td>
                        <td><small>${lastExecution}</small></td>
                        <td>${statusBadge}</td>
                        <td>${successRate}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); toggleTask('${task.taskName}')">
                                ${task.enabled ? '禁用' : '启用'}
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="event.stopPropagation(); resetTask('${task.taskName}')">
                                重置
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
            console.log('任务列表显示完成');
        }

        // 获取任务状态徽章
        function getTaskStatusBadge(status) {
            switch (status) {
                case 'SUCCESS':
                    return '<span class="badge bg-success">成功</span>';
                case 'FAILED':
                    return '<span class="badge bg-danger">失败</span>';
                case 'RUNNING':
                    return '<span class="badge bg-warning">运行中</span>';
                default:
                    return '<span class="badge bg-secondary">未知</span>';
            }
        }

        // 加载最近执行日志
        function loadRecentLogs() {
            console.log('开始加载最近执行日志...');

            const container = document.getElementById('recentLogs');
            if (container) {
                container.innerHTML = '<div class="text-center text-muted"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
            }

            fetch('/task-management/api/logs?page=0&size=10')
                .then(response => {
                    console.log('日志API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('日志数据:', data);

                    if (data.success) {
                        displayRecentLogs(data.data);
                    } else {
                        if (container) {
                            container.innerHTML = '<div class="text-center text-danger">加载失败: ' + (data.message || '未知错误') + '</div>';
                        }
                    }
                })
                .catch(error => {
                    console.error('加载最近日志失败:', error);
                    if (container) {
                        container.innerHTML = '<div class="text-center text-danger">网络错误: ' + error.message + '</div>';
                    }
                });
        }

        // 显示最近执行日志
        function displayRecentLogs(logs) {
            console.log('显示最近日志，日志数量:', logs ? logs.length : 0);

            const container = document.getElementById('recentLogs');
            if (!container) {
                console.error('找不到日志容器元素');
                return;
            }

            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="text-center text-muted">暂无执行日志</div>';
                return;
            }

            let html = '';
            logs.forEach(log => {
                const statusIcon = log.executionStatus === 'SUCCESS'
                    ? '<i class="fas fa-check-circle text-success"></i>'
                    : log.executionStatus === 'FAILED'
                    ? '<i class="fas fa-times-circle text-danger"></i>'
                    : '<i class="fas fa-spinner text-warning"></i>';

                const duration = log.executionDuration
                    ? (log.executionDuration / 1000).toFixed(2) + 's'
                    : '-';

                const startTime = log.executionStartTime
                    ? new Date(log.executionStartTime).toLocaleString()
                    : '未知时间';

                html += `
                    <div class="mb-2 p-2 border-start border-3 ${log.executionStatus === 'SUCCESS' ? 'border-success' : log.executionStatus === 'FAILED' ? 'border-danger' : 'border-warning'}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>${log.taskName || '未知任务'}</strong> ${statusIcon}
                                <br><small class="text-muted">${startTime}</small>
                                <br><small class="text-muted">耗时: ${duration}</small>
                            </div>
                        </div>
                        ${log.resultMessage ? '<div class="mt-1"><small>' + log.resultMessage + '</small></div>' : ''}
                        ${log.errorMessage ? '<div class="mt-1 text-danger"><small>' + log.errorMessage + '</small></div>' : ''}
                    </div>
                `;
            });

            container.innerHTML = html;
            console.log('最近日志显示完成');
        }

        // 更新需要关注的任务
        function updateAlertTasks(dashboardData) {
            console.log('更新警告任务，数据:', dashboardData);

            const container = document.getElementById('alertTasks');
            if (!container) {
                console.error('找不到警告任务容器元素');
                return;
            }

            let html = '';

            // 高失败率任务
            if (dashboardData.highFailureTasks && dashboardData.highFailureTasks.length > 0) {
                html += '<div class="alert alert-warning py-2 mb-2"><strong>高失败率任务:</strong></div>';
                dashboardData.highFailureTasks.forEach(task => {
                    html += `<div class="mb-1"><small>${task.taskName || '未知任务'} (失败${task.failureCount || 0}次)</small></div>`;
                });
            }

            // 长时间未执行的任务
            if (dashboardData.staleTasks && dashboardData.staleTasks.length > 0) {
                html += '<div class="alert alert-info py-2 mb-2"><strong>长时间未执行:</strong></div>';
                dashboardData.staleTasks.forEach(task => {
                    html += `<div class="mb-1"><small>${task.taskName || '未知任务'}</small></div>`;
                });
            }

            // 超时任务
            if (dashboardData.timeoutTasks && dashboardData.timeoutTasks.length > 0) {
                html += '<div class="alert alert-danger py-2 mb-2"><strong>超时任务:</strong></div>';
                dashboardData.timeoutTasks.forEach(task => {
                    html += `<div class="mb-1"><small>${task.taskName || '未知任务'}</small></div>`;
                });
            }

            if (html === '') {
                html = '<div class="text-center text-success"><i class="fas fa-check-circle"></i> 所有任务运行正常</div>';
            }

            container.innerHTML = html;
            console.log('警告任务更新完成');
        }

        // 刷新任务列表
        function refreshTasks() {
            const icon = document.getElementById('refreshIcon');
            icon.classList.add('fa-spin');

            loadTaskDashboard();
            loadTasks();
            loadRecentLogs();

            setTimeout(() => {
                icon.classList.remove('fa-spin');
            }, 1000);
        }

        // 切换任务启用状态
        function toggleTask(taskName) {
            if (confirm('确定要切换任务 "' + taskName + '" 的状态吗？')) {
                fetch(`/task-management/api/tasks/${taskName}/toggle`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadTasks();
                        loadTaskDashboard();
                    } else {
                        alert('操作失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('网络错误: ' + error.message);
                });
            }
        }

        // 重置任务统计
        function resetTask(taskName) {
            if (confirm('确定要重置任务 "' + taskName + '" 的统计信息吗？')) {
                fetch(`/task-management/api/tasks/${taskName}/reset`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadTasks();
                    } else {
                        alert('操作失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('网络错误: ' + error.message);
                });
            }
        }

        // 显示任务详情
        function showTaskDetail(taskName) {
            fetch(`/task-management/api/tasks/${taskName}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTaskDetail(data.taskInfo, data.recentLogs);
                        new bootstrap.Modal(document.getElementById('taskDetailModal')).show();
                    } else {
                        alert('获取任务详情失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('网络错误: ' + error.message);
                });
        }

        // 显示任务详情内容
        function displayTaskDetail(task, recentLogs) {
            const content = document.getElementById('taskDetailContent');

            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>任务名称:</td><td>${task.taskName}</td></tr>
                            <tr><td>描述:</td><td>${task.taskDescription || '-'}</td></tr>
                            <tr><td>类名:</td><td>${task.taskClass}</td></tr>
                            <tr><td>方法名:</td><td>${task.taskMethod}</td></tr>
                            <tr><td>执行频率:</td><td>${task.fixedRate ? (task.fixedRate/1000) + '秒' : task.cronExpression || '-'}</td></tr>
                            <tr><td>状态:</td><td>${task.enabled ? '<span class="badge bg-success">启用</span>' : '<span class="badge bg-secondary">禁用</span>'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>执行统计</h6>
                        <table class="table table-sm">
                            <tr><td>总执行次数:</td><td>${task.executionCount}</td></tr>
                            <tr><td>成功次数:</td><td>${task.successCount}</td></tr>
                            <tr><td>失败次数:</td><td>${task.failureCount}</td></tr>
                            <tr><td>成功率:</td><td>${task.executionCount > 0 ? Math.round((task.successCount / task.executionCount) * 100) + '%' : '-'}</td></tr>
                            <tr><td>最后执行:</td><td>${task.lastExecutionTime ? new Date(task.lastExecutionTime).toLocaleString() : '从未执行'}</td></tr>
                            <tr><td>最后状态:</td><td>${getTaskStatusBadge(task.lastExecutionStatus)}</td></tr>
                        </table>
                    </div>
                </div>
            `;

            if (recentLogs && recentLogs.length > 0) {
                html += `
                    <div class="mt-3">
                        <h6>最近执行记录</h6>
                        <div style="max-height: 300px; overflow-y: auto;">
                `;

                recentLogs.forEach(log => {
                    html += `
                        <div class="border-bottom py-2">
                            <div class="d-flex justify-content-between">
                                <span>${getTaskStatusBadge(log.executionStatus)}</span>
                                <small class="text-muted">${new Date(log.executionStartTime).toLocaleString()}</small>
                            </div>
                            ${log.resultMessage ? '<div class="mt-1"><small>' + log.resultMessage + '</small></div>' : ''}
                            ${log.errorMessage ? '<div class="mt-1 text-danger"><small>' + log.errorMessage + '</small></div>' : ''}
                        </div>
                    `;
                });

                html += '</div></div>';
            }

            content.innerHTML = html;
        }

        // 清理历史日志
        function cleanupLogs() {
            const days = prompt('请输入要保留的天数（默认30天）:', '30');
            if (days !== null && !isNaN(days) && parseInt(days) > 0) {
                fetch('/task-management/api/cleanup-logs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'daysToKeep=' + parseInt(days)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadRecentLogs();
                    } else {
                        alert('清理失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('网络错误: ' + error.message);
                });
            }
        }

        // ==================== 每日出入记录统计相关函数 ====================
        
        // 手动触发每日出入记录统计
        function manualQueryDailyAccessStats() {
            // 更新状态显示
            document.getElementById('dailyStatsStatus').innerHTML = 
                '<i class="fas fa-spinner fa-spin"></i> 正在统计当天出入记录...';
            
            // 隐藏之前的通知
            const notification = document.getElementById('dailyStatsNotification');
            if (notification) {
                notification.style.display = 'none';
            }
            
            fetch('/task-management/api/daily-access-stats/manual-check', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新状态显示
                    document.getElementById('dailyStatsStatus').innerHTML = 
                        `<i class="fas fa-check-circle text-success"></i> ${data.message}`;
                    
                    // 更新统计数字
                    document.getElementById('todaySuccessCount').textContent = data.totalSuccessCount;
                    document.getElementById('lastStatsTime').textContent = data.queryTime;
                    document.getElementById('todayStatsTime').textContent = 
                        `更新于 ${data.queryTime}`;
                    
                    // 显示成功通知
                    showDailyStatsNotification('success', 
                        `统计完成！当天校验成功总人数：${data.totalSuccessCount} 人`, 
                        data.statsData);
                        
                } else {
                    document.getElementById('dailyStatsStatus').innerHTML = 
                        `<i class="fas fa-exclamation-triangle text-danger"></i> 统计失败: ${data.message}`;
                    
                    // 显示失败通知
                    showDailyStatsNotification('danger', `统计失败: ${data.message}`);
                }
            })
            .catch(error => {
                document.getElementById('dailyStatsStatus').innerHTML = 
                    `<i class="fas fa-exclamation-triangle text-danger"></i> 网络错误: ${error.message}`;
                
                // 显示错误通知
                showDailyStatsNotification('danger', `网络错误: ${error.message}`);
            });
        }

        // 加载每日出入记录统计结果
        function loadDailyAccessStatsResults() {
            fetch('/task-management/api/daily-access-stats/results')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.statsData) {
                        // 更新仪表板显示
                        document.getElementById('todaySuccessCount').textContent = 
                            data.statsData.totalSuccessCount || 0;
                        document.getElementById('todayStatsTime').textContent = 
                            data.queryTime ? `更新于 ${data.queryTime}` : '暂无数据';
                        
                        // 更新详细区域显示
                        document.getElementById('lastStatsTime').textContent = 
                            data.queryTime || '-';
                        document.getElementById('dailyStatsStatus').innerHTML = 
                            data.statsData ? 
                            `<i class="fas fa-info-circle text-info"></i> 最近统计结果：当天校验成功 ${data.statsData.totalSuccessCount} 人` :
                            '点击"手动统计"按钮开始统计当天数据...';
                    } else {
                        // 无数据时的默认显示
                        document.getElementById('todaySuccessCount').textContent = '-';
                        document.getElementById('todayStatsTime').textContent = '暂无数据';
                        const lastStatsTimeEl = document.getElementById('lastStatsTime');
                        if (lastStatsTimeEl) {
                            lastStatsTimeEl.textContent = '-';
                        }
                    }
                })
                .catch(error => {
                    console.error('加载每日统计结果失败:', error);
                });
        }

        // 显示统计结果通知
        function showDailyStatsNotification(type, message, statsData) {
            const notification = document.getElementById('dailyStatsNotification');
            if (!notification) return;
            
            let alertClass = 'alert-info';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                alertClass = 'alert-success';
                icon = 'fa-check-circle';
            } else if (type === 'danger') {
                alertClass = 'alert-danger';
                icon = 'fa-exclamation-triangle';
            }
            
            let html = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${icon}"></i> ${message}
            `;
            
            if (statsData) {
                html += `
                    <div class="mt-2">
                        <strong>统计详情：</strong><br>
                        <small>
                            • 统计日期：${statsData.statsDate}<br>
                            • 查询时间范围：${statsData.startTime} 至 ${statsData.endTime}<br>
                            • 查询耗时：${statsData.queryDurationMs} 毫秒<br>
                            • 总记录数：${statsData.totalRecords} 条
                        </small>
                    </div>
                `;
            }
            
            html += `
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            notification.innerHTML = html;
            notification.style.display = 'block';
            
            // 5秒后自动隐藏成功通知
            if (type === 'success') {
                setTimeout(() => {
                    const alert = notification.querySelector('.alert');
                    if (alert) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000);
            }
        }

        // 显示统计详情（预留功能）
        function showDailyStatsDetail() {
            fetch('/task-management/api/daily-access-stats/results')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.statsData) {
                        showDailyStatsNotification('info', '统计详情', data.statsData);
                    } else {
                        showDailyStatsNotification('info', '暂无统计数据，请先执行统计任务');
                    }
                })
                .catch(error => {
                    showDailyStatsNotification('danger', `获取详情失败: ${error.message}`);
                });
        }

        // 显示任务错误信息
        function showTaskError(message) {
            const tbody = document.getElementById('taskTableBody');
            if (tbody) {
                tbody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">${message}</td></tr>`;
            }
        }

        // 切换同步任务状态
        function toggleSyncTask() {
            console.log('切换同步任务状态...');

            if (confirm('确定要切换人员宿舍同步任务的启用/禁用状态吗？')) {
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 切换中...';
                button.disabled = true;

                fetch('/task-management/api/tasks/PersonDormitorySyncTask/toggle', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('切换任务状态结果:', data);

                    if (data.success) {
                        alert('任务状态切换成功！\n请点击"检查任务状态"查看最新状态。');
                        // 刷新任务列表
                        loadTasks();
                    } else {
                        alert('切换任务状态失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('切换任务状态错误:', error);
                    alert('切换任务状态出现错误: ' + error.message);
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }

        // 检查任务状态
        function checkTaskStatus() {
            console.log('检查任务状态...');

            fetch('/task-management/api/tasks')
                .then(response => response.json())
                .then(data => {
                    console.log('任务状态数据:', data);

                    if (data.success && data.data) {
                        const syncTask = data.data.find(task => task.taskName === 'PersonDormitorySyncTask');
                        if (syncTask) {
                            const status = syncTask.enabled ? '启用' : '禁用';
                            const lastExecution = syncTask.lastExecutionTime
                                ? new Date(syncTask.lastExecutionTime).toLocaleString()
                                : '从未执行';

                            alert(`人员宿舍同步任务状态：\n` +
                                  `状态: ${status}\n` +
                                  `最后执行: ${lastExecution}\n` +
                                  `执行次数: ${syncTask.executionCount || 0}\n` +
                                  `成功次数: ${syncTask.successCount || 0}\n` +
                                  `失败次数: ${syncTask.failureCount || 0}`);
                        } else {
                            alert('未找到人员宿舍同步任务');
                        }
                    } else {
                        alert('获取任务状态失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('检查任务状态错误:', error);
                    alert('检查任务状态出现错误: ' + error.message);
                });
        }

        // 显示同步预览
        function showSyncPreview() {
            console.log('显示同步预览...');

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('syncPreviewModal'));
            modal.show();

            // 加载预览数据
            loadSyncPreviewData();
        }

        // 加载同步预览数据
        function loadSyncPreviewData() {
            const content = document.getElementById('syncPreviewContent');
            content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载预览数据中...</div>';

            fetch('/fastgate/dormitory/relations/sync/preview')
                .then(response => response.json())
                .then(data => {
                    console.log('同步预览数据:', data);

                    if (data.ErrCode === 200) {
                        displaySyncPreview(data.data);
                    } else {
                        content.innerHTML = '<div class="alert alert-danger">加载预览数据失败: ' + data.ErrMsg + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载预览数据错误:', error);
                    content.innerHTML = '<div class="alert alert-danger">网络错误: ' + error.message + '</div>';
                });
        }

        // 显示同步预览数据
        function displaySyncPreview(data) {
            const content = document.getElementById('syncPreviewContent');

            if (data.totalCount === 0) {
                content.innerHTML = `
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> 没有需要同步的人员宿舍关联关系
                    </div>
                `;
                document.getElementById('confirmSyncBtn').style.display = 'none';
                return;
            }

            let html = `
                <div class="alert alert-primary">
                    <h6><i class="fas fa-info-circle"></i> 同步概览</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary mb-0">${data.totalCount}</h4>
                                <small class="text-muted">待同步人员</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success mb-0">${data.buildingCount}</h4>
                                <small class="text-muted">涉及楼栋</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info mb-0">${data.buildings.reduce((sum, b) => sum + b.floorCount, 0)}</h4>
                                <small class="text-muted">涉及楼层</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning mb-0">${data.buildings.reduce((sum, b) => sum + b.floors.reduce((fsum, f) => fsum + f.roomCount, 0), 0)}</h4>
                                <small class="text-muted">涉及房间</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion" id="buildingAccordion">
            `;

            data.buildings.forEach((building, buildingIndex) => {
                html += `
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="building${buildingIndex}">
                            <button class="accordion-button ${buildingIndex === 0 ? '' : 'collapsed'}" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#collapse${buildingIndex}"
                                    aria-expanded="${buildingIndex === 0 ? 'true' : 'false'}" aria-controls="collapse${buildingIndex}">
                                <i class="fas fa-building me-2"></i>
                                <strong>${building.buildingName}</strong>
                                <span class="badge bg-primary ms-2">${building.personCount}人</span>
                                <span class="badge bg-secondary ms-1">${building.floorCount}层</span>
                            </button>
                        </h2>
                        <div id="collapse${buildingIndex}" class="accordion-collapse collapse ${buildingIndex === 0 ? 'show' : ''}"
                             aria-labelledby="building${buildingIndex}" data-bs-parent="#buildingAccordion">
                            <div class="accordion-body">
                `;

                building.floors.forEach(floor => {
                    html += `
                        <div class="mb-3">
                            <h6 class="text-primary">
                                <i class="fas fa-layer-group"></i> ${floor.floorName}
                                <span class="badge bg-info ms-2">${floor.personCount}人</span>
                                <span class="badge bg-secondary ms-1">${floor.roomCount}间</span>
                            </h6>
                            <div class="row">
                    `;

                    floor.rooms.forEach(room => {
                        html += `
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="card border-primary">
                                    <div class="card-header bg-light py-2">
                                        <h6 class="card-title mb-0 text-primary">
                                            <i class="fas fa-door-open"></i> ${room.roomName}
                                            <span class="badge bg-success ms-2">${room.personCount}人</span>
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="list-group list-group-flush">
                        `;

                        room.persons.forEach(person => {
                            html += `
                                <div class="list-group-item px-0 py-1">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>${person.personName}</strong>
                                            <small class="text-muted">(${person.personCode})</small>
                                            <br>
                                            <small class="text-info">${person.personType}</small>
                                            ${person.bedNo ? '<small class="text-muted"> | 床位: ' + person.bedNo + '</small>' : ''}
                                        </div>
                                    </div>
                                </div>
                            `;
                        });

                        html += `
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                });

                html += `
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            content.innerHTML = html;
            document.getElementById('confirmSyncBtn').style.display = 'inline-block';
        }

        // 确认并执行同步
        function confirmAndExecuteSync() {
            if (confirm('确定要执行同步吗？这将同步所有预览中显示的人员宿舍关联关系。')) {
                // 关闭预览模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('syncPreviewModal'));
                modal.hide();

                // 执行手动同步
                testManualSync();
            }
        }

        // 测试手动同步功能
        function testManualSync() {
            console.log('开始测试手动同步...');

            if (confirm('确定要执行手动同步吗？这将同步所有未同步的人员宿舍关联关系。')) {
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
                button.disabled = true;

                fetch('/fastgate/dormitory/relations/sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('手动同步结果:', data);

                    if (data.ErrCode === 200) {
                        alert('手动同步成功！\n' +
                              '总处理: ' + (data.data.totalProcessed || 0) + ' 条\n' +
                              '成功: ' + (data.data.successCount || 0) + ' 条\n' +
                              '失败: ' + (data.data.failedCount || 0) + ' 条');
                    } else {
                        alert('手动同步失败: ' + data.ErrMsg);
                    }
                })
                .catch(error => {
                    console.error('手动同步错误:', error);
                    alert('手动同步出现错误: ' + error.message);
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }

        // 测试定时任务功能
        function testTaskFunctionality() {
            console.log('=== 测试定时任务功能 ===');

            // 检查必要的DOM元素
            const elements = [
                'task-tab', 'task-content', 'totalTasks', 'enabledTasks',
                'runningTasks', 'disabledTasks', 'taskTableBody', 'recentLogs', 'alertTasks'
            ];

            elements.forEach(id => {
                const el = document.getElementById(id);
                console.log(`元素 ${id}:`, el ? '存在' : '不存在');
            });

            // 测试API连通性
            console.log('测试API连通性...');

            // 测试仪表板API
            fetch('/task-management/api/dashboard')
                .then(response => {
                    console.log('仪表板API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('仪表板API响应数据:', data);
                })
                .catch(error => {
                    console.error('仪表板API错误:', error);
                });

            // 测试任务列表API
            fetch('/task-management/api/tasks')
                .then(response => {
                    console.log('任务列表API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('任务列表API响应数据:', data);
                })
                .catch(error => {
                    console.error('任务列表API错误:', error);
                });

            // 手动触发加载
            console.log('手动触发定时任务数据加载...');
            loadTaskDashboard();
            loadTasks();
            loadRecentLogs();
        }

        // 在页面加载完成后添加测试按钮和强制事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加测试按钮到定时任务标签页
            setTimeout(() => {
                const taskContent = document.getElementById('task-content');
                if (taskContent) {
                    const testButton = document.createElement('button');
                    testButton.className = 'btn btn-sm btn-outline-info position-fixed';
                    testButton.style.cssText = 'top: 10px; right: 10px; z-index: 1000;';
                    testButton.innerHTML = '<i class="fas fa-bug"></i> 测试定时任务';
                    testButton.onclick = testTaskFunctionality;
                    document.body.appendChild(testButton);
                }

                // 强制添加点击事件监听器
                const taskTab = document.getElementById('task-tab');
                if (taskTab) {
                    // 移除所有现有的事件监听器，重新添加
                    taskTab.onclick = function(e) {
                        console.log('强制点击事件触发');
                        e.preventDefault();

                        // 手动切换标签页
                        const allTabs = document.querySelectorAll('.nav-link');
                        const allContents = document.querySelectorAll('.tab-pane');

                        // 移除所有active类
                        allTabs.forEach(tab => tab.classList.remove('active'));
                        allContents.forEach(content => {
                            content.classList.remove('active', 'show');
                        });

                        // 激活当前标签页
                        taskTab.classList.add('active');
                        const taskContent = document.getElementById('task-content');
                        if (taskContent) {
                            taskContent.classList.add('active', 'show');
                        }

                        // 加载数据
                        setTimeout(() => {
                            handleTaskTabClick();
                        }, 100);
                    };
                }
            }, 1000);
        });

        // ========== 人脸照片同步检查相关JavaScript函数 ==========

        // 人脸照片同步相关功能
        let currentFacePhotoPage = 0;
        const facePhotoPageSize = 20;

        // 手动检查人脸照片同步问题
        function manualCheckFacePhoto() {
            document.getElementById('facePhotoCheckStatus').innerHTML = 
                '<i class="fas fa-spinner fa-spin"></i> 正在检查...';
            
            fetch('/task-management/api/face-photo-issues/manual-check', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('facePhotoCheckStatus').innerHTML = 
                        `<i class="fas fa-check-circle text-success"></i> ${data.message}`;
                    document.getElementById('issueCountBadge').textContent = data.totalIssues + ' 个问题';
                    document.getElementById('lastCheckTime').textContent = new Date().toLocaleString();
                    
                    if (data.totalIssues > 0) {
                        document.getElementById('facePhotoCheckStatus').innerHTML += 
                            ` - 发现 ${data.totalIssues} 条问题记录`;
                    }
                } else {
                    document.getElementById('facePhotoCheckStatus').innerHTML = 
                        `<i class="fas fa-exclamation-triangle text-danger"></i> 检查失败: ${data.message}`;
                }
            })
            .catch(error => {
                document.getElementById('facePhotoCheckStatus').innerHTML = 
                    `<i class="fas fa-exclamation-triangle text-danger"></i> 网络错误: ${error.message}`;
            });
        }

        // 显示人脸照片同步问题数据
        function showFacePhotoIssues() {
            currentFacePhotoPage = 0;
            loadFacePhotoIssues();
            new bootstrap.Modal(document.getElementById('facePhotoIssuesModal')).show();
        }

        // 加载人脸照片同步问题数据
        function loadFacePhotoIssues(page = 0) {
            currentFacePhotoPage = page;
            
            fetch(`/task-management/api/face-photo-issues?page=${page}&size=${facePhotoPageSize}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayFacePhotoIssues(data.data);
                        updateFacePhotoPagination(data.currentPage, data.totalPages, data.totalElements);
                        
                        // 更新问题统计
                        document.getElementById('issueCountBadge').textContent = data.totalElements + ' 个问题';
                    } else {
                        showFacePhotoError('加载数据失败: ' + data.message);
                    }
                })
                .catch(error => {
                    showFacePhotoError('网络错误: ' + error.message);
                });
        }

        // 显示人脸照片同步问题列表
        function displayFacePhotoIssues(issues) {
            const tbody = document.getElementById('facePhotoIssuesTableBody');
            
            if (issues.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-success">恭喜！未发现同步问题</td></tr>';
                return;
            }

            let html = '';
            issues.forEach(issue => {
                const issueTypeClass = getIssueTypeClass(issue.issueType);
                
                // 判断是否可以同步（只有有人脸照片但标识不为1的才能同步）
                const canSync = issue.issueType === '人脸照片同步标识为空' || issue.issueType === '人脸照片未同步';
                const syncButton = canSync ? 
                    `<button class="btn btn-sm btn-outline-primary" onclick="syncSinglePersonFacePhoto('${issue.personCode}', '${issue.personName}', this)" title="同步此人员的人脸照片">
                        <i class="fas fa-sync"></i> 同步
                    </button>` : 
                    `<span class="text-muted" title="该记录无法自动同步">
                        <i class="fas fa-minus"></i> 无法同步
                    </span>`;
                
                html += `
                    <tr id="facePhotoRow_${issue.personCode}">
                        <td>${issue.personCode}</td>
                        <td>${issue.personName}</td>
                        <td>${issue.departmentCode || '-'}</td>
                        <td>${issue.telephone || '-'}</td>
                        <td><span class="badge ${issueTypeClass}">${issue.issueType}</span></td>
                        <td><small class="text-muted">${issue.issueDescription}</small></td>
                        <td>${syncButton}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // 获取问题类型样式类
        function getIssueTypeClass(issueType) {
            switch (issueType) {
                case '无人脸照片记录':
                    return 'bg-warning';
                case '人脸照片同步标识为空':
                    return 'bg-info';
                case '人脸照片未同步':
                    return 'bg-danger';
                default:
                    return 'bg-secondary';
            }
        }

        // 更新人脸照片问题分页
        function updateFacePhotoPagination(currentPage, totalPages, totalElements) {
            const pagination = document.getElementById('facePhotoPagination');
            let html = '';
            
            // 显示总数信息
            html += `<li class="page-item disabled"><span class="page-link">共 ${totalElements} 条记录</span></li>`;
            
            if (totalPages <= 1) {
                pagination.innerHTML = html;
                return;
            }
            
            // 上一页
            html += `<li class="page-item ${currentPage === 0 ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="loadFacePhotoIssues(${currentPage - 1})">上一页</a>
                     </li>`;
            
            // 页码
            const startPage = Math.max(0, currentPage - 2);
            const endPage = Math.min(totalPages - 1, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `<li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="loadFacePhotoIssues(${i})">${i + 1}</a>
                         </li>`;
            }
            
            // 下一页
            html += `<li class="page-item ${currentPage === totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="loadFacePhotoIssues(${currentPage + 1})">下一页</a>
                     </li>`;
            
            pagination.innerHTML = html;
        }

        // 刷新人脸照片问题数据
        function refreshFacePhotoIssues() {
            loadFacePhotoIssues(currentFacePhotoPage);
        }

        // 显示人脸照片错误信息
        function showFacePhotoError(message) {
            const tbody = document.getElementById('facePhotoIssuesTableBody');
            tbody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">
                                <i class="fas fa-exclamation-triangle"></i> ${message}
                               </td></tr>`;
        }

        // 单个人员人脸照片同步功能
        function syncSinglePersonFacePhoto(personCode, personName, buttonElement) {
            if (!confirm(`确定要同步人员 ${personName} (${personCode}) 的人脸照片吗？`)) {
                return;
            }
            
            // 更新按钮状态
            const originalHtml = buttonElement.innerHTML;
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
            
            fetch('/task-management/api/face-photo-issues/sync-single', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `personCode=${encodeURIComponent(personCode)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 同步成功，更新行状态
                    const row = document.getElementById(`facePhotoRow_${personCode}`);
                    if (row) {
                        // 更新问题类型为成功状态
                        const issueTypeCell = row.querySelector('.badge');
                        if (issueTypeCell) {
                            issueTypeCell.className = 'badge bg-success';
                            issueTypeCell.textContent = '已同步';
                        }
                        
                        // 更新操作按钮
                        buttonElement.innerHTML = '<i class="fas fa-check"></i> 已同步';
                        buttonElement.className = 'btn btn-sm btn-success';
                        buttonElement.disabled = true;
                    }
                    
                    // 显示成功消息
                    showFacePhotoSyncMessage(`${personName} (${personCode}) 人脸照片同步成功！`, 'success');
                    
                    // 更新统计信息
                    loadFacePhotoStats();
                } else {
                    // 同步失败，恢复按钮
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = originalHtml;
                    showFacePhotoSyncMessage(`${personName} (${personCode}) 人脸照片同步失败: ${data.message}`, 'danger');
                }
            })
            .catch(error => {
                // 网络错误，恢复按钮
                buttonElement.disabled = false;
                buttonElement.innerHTML = originalHtml;
                showFacePhotoSyncMessage(`${personName} (${personCode}) 人脸照片同步失败: ${error.message}`, 'danger');
            });
        }

        // 显示人脸照片同步消息
        function showFacePhotoSyncMessage(message, type) {
            // 创建消息提示元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒后自动删除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }
        
        // 显示人脸照片更新消息
        function showFacePhotoUpdateMessage(message, type) {
            // 创建消息提示元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒后自动删除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }

        // 页面初始化时加载人脸照片统计信息
        function loadFacePhotoStats() {
            fetch('/task-management/api/face-photo-sync-stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('issueCountBadge').textContent = (data.totalIssues || 0) + ' 个问题';
                    }
                })
                .catch(error => {
                    console.error('加载人脸照片统计信息失败:', error);
                });
        }

        // 定期刷新人脸照片统计信息（每30秒）
        setInterval(function() {
            // 只有在定时任务tab激活时才刷新
            const taskTab = document.getElementById('task-tab');
            const taskContent = document.getElementById('task-content');
            if (taskTab && taskTab.classList.contains('active') && 
                taskContent && taskContent.classList.contains('active')) {
                loadFacePhotoStats();
            }
        }, 30000);

        // ========== Excel人员导入相关JavaScript函数 ==========

        // 全局变量存储导入数据
        let importData = [];
        let currentStep = 1;

        // 选择Excel文件
        function selectExcelFile() {
            document.getElementById('excelFileInput').click();
        }

        // 文件选择事件处理
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('excelFileInput');
            const uploadArea = document.getElementById('uploadArea');

            // 文件选择事件
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    uploadExcelFile(file);
                }
            });

            // 拖拽上传支持
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
                uploadArea.style.backgroundColor = '#f8f9fa';
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#dee2e6';
                uploadArea.style.backgroundColor = '';
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#dee2e6';
                uploadArea.style.backgroundColor = '';
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) {
                        fileInput.files = files;
                        uploadExcelFile(file);
                    } else {
                        alert('请选择Excel文件（.xls或.xlsx格式）');
                    }
                }
            });

            uploadArea.addEventListener('click', function() {
                selectExcelFile();
            });
        });

        // 上传Excel文件
        function uploadExcelFile(file) {
            console.log('开始上传Excel文件:', file.name);
            
            // 显示上传进度
            document.getElementById('uploadContent').style.display = 'none';
            document.getElementById('uploadProgress').style.display = 'block';

            const formData = new FormData();
            formData.append('file', file);

            fetch('/api/person/import/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('文件上传响应:', data);
                
                if (data.success) {
                    // 隐藏上传进度
                    document.getElementById('uploadProgress').style.display = 'none';
                    
                    // 存储导入数据
                    importData = data.allData;
                    
                    // 显示文件信息
                    showFileInfo(data);
                    
                    // 显示数据预览
                    showDataPreview(data.previewData);
                    
                    // 显示下一步按钮
                    document.getElementById('nextStepBtn').style.display = 'inline-block';
                    
                } else {
                    alert('文件上传失败：' + data.message);
                    resetUploadArea();
                }
            })
            .catch(error => {
                console.error('文件上传错误:', error);
                alert('文件上传出现错误：' + error.message);
                resetUploadArea();
            });
        }

        // 重置上传区域
        function resetUploadArea() {
            document.getElementById('uploadContent').style.display = 'block';
            document.getElementById('uploadProgress').style.display = 'none';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('previewSection').style.display = 'none';
            document.getElementById('nextStepBtn').style.display = 'none';
            document.getElementById('excelFileInput').value = '';
        }

        // 显示文件信息
        function showFileInfo(data) {
            const fileInfo = document.getElementById('fileInfo');
            const fileInfoText = document.getElementById('fileInfoText');
            
            fileInfoText.textContent = `${data.fileName} (${data.totalCount}条记录)`;
            fileInfo.style.display = 'block';
        }

        // 显示数据预览
        function showDataPreview(previewData) {
            const previewSection = document.getElementById('previewSection');
            const tableBody = document.getElementById('previewTableBody');
            
            let html = '';
            previewData.forEach(item => {
                html += `
                    <tr>
                        <td>${item.rowNumber}</td>
                        <td>${item.name || ''}</td>
                        <td>${item.jobNumber || ''}</td>
                        <td>${item.departmentCode || ''}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
            previewSection.style.display = 'block';
        }

        // 下一步
        function nextStep() {
            if (currentStep === 1) {
                // 进入第二步：数据匹配
                goToStep2();
            }
        }

        // 进入第二步
        function goToStep2() {
            currentStep = 2;
            
            // 切换标签页
            document.getElementById('step1-tab').classList.remove('active');
            document.getElementById('step1-content').classList.remove('active', 'show');
            document.getElementById('step2-tab').classList.remove('disabled');
            document.getElementById('step2-tab').classList.add('active');
            document.getElementById('step2-content').classList.add('active', 'show');
            
            // 隐藏下一步按钮，显示开始匹配按钮
            document.getElementById('nextStepBtn').style.display = 'none';
            document.getElementById('startMatchBtn').style.display = 'inline-block';
        }

        // 开始匹配
        function startMatching() {
            console.log('开始人员匹配...');
            
            // 隐藏开始匹配按钮
            document.getElementById('startMatchBtn').style.display = 'none';
            
            // 显示匹配进度
            showMatchProgress();
            
            // 发送匹配请求
            const requestData = {
                importData: importData
            };
            
            fetch('/api/person/import/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('匹配响应:', data);
                
                if (data.success) {
                    // 更新导入数据
                    importData = data.data;
                    
                    // 显示匹配结果统计
                    showMatchStatistics(data.statistics);
                    
                    // 自动进入第三步
                    setTimeout(() => {
                        goToStep3();
                    }, 2000);
                    
                } else {
                    alert('人员匹配失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('匹配请求错误:', error);
                alert('匹配过程出现错误：' + error.message);
            });
        }

        // 显示匹配进度
        function showMatchProgress() {
            let progress = 0;
            const progressBar = document.getElementById('matchProgress');
            const progressText = document.getElementById('matchProgressText');
            
            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                
                if (progress <= 30) {
                    progressText.textContent = '正在验证Excel数据...';
                } else if (progress <= 60) {
                    progressText.textContent = '正在查询中间库...';
                } else if (progress <= 90) {
                    progressText.textContent = '正在检查同步状态...';
                } else {
                    progressText.textContent = '匹配完成！';
                    clearInterval(interval);
                }
            }, 200);
        }

        // 显示匹配统计
        function showMatchStatistics(statistics) {
            document.getElementById('totalCountStat').textContent = statistics.totalCount;
            document.getElementById('matchedCountStat').textContent = statistics.matchedCount;
            document.getElementById('syncedCountStat').textContent = statistics.syncedCount;
            document.getElementById('unmatchedCountStat').textContent = statistics.unmatchedCount;
            
            document.getElementById('matchResultSummary').style.display = 'block';
        }

        // 进入第三步
        function goToStep3() {
            currentStep = 3;
            
            // 切换标签页
            document.getElementById('step2-tab').classList.remove('active');
            document.getElementById('step2-content').classList.remove('active', 'show');
            document.getElementById('step3-tab').classList.remove('disabled');
            document.getElementById('step3-tab').classList.add('active');
            document.getElementById('step3-content').classList.add('active', 'show');
            
            // 显示结果表格
            showResultTable(importData);
        }

        // 显示结果表格
        function showResultTable(data, filter = 'all') {
            const tableBody = document.getElementById('resultTableBody');
            
            // 根据筛选条件过滤数据
            let filteredData = data;
            if (filter !== 'all') {
                filteredData = data.filter(item => {
                    switch (filter) {
                        case 'matched':
                            return item.isMatched;
                        case 'synced':
                            return item.isSynced;
                        case 'unmatched':
                            return !item.isMatched;
                        default:
                            return true;
                    }
                });
            }
            
            let html = '';
            filteredData.forEach(item => {
                const matchStatusClass = item.isMatched ? 'text-success' : 'text-danger';
                const syncStatusClass = item.isSynced ? 'text-info' : 'text-warning';
                
                // 获取匹配的姓名和数据来源
                let matchedName = '-';
                let dataSource = '未匹配';
                let sourceClass = 'text-muted';
                
                if (item.matchedPerson) {
                    matchedName = item.matchedPerson.accName;
                    dataSource = '中间库';
                    sourceClass = 'text-primary';
                } else if (item.localMatchedPerson) {
                    matchedName = item.localMatchedPerson.personName;
                    dataSource = '本地库';
                    sourceClass = 'text-success';
                }
                
                // 构建操作按钮
                let actionButtons = '';
                if (item.isMatched && !item.isSynced) {
                    actionButtons = `
                        <button type="button" class="btn btn-sm btn-success" 
                                onclick="syncSinglePerson(${item.rowNumber}, '${item.jobNumber}')"
                                title="同步到EGS平台">
                            <i class="fas fa-sync"></i> 同步
                        </button>
                    `;
                } else if (item.isSynced) {
                    actionButtons = '<span class="text-muted small">已同步</span>';
                } else {
                    actionButtons = '<span class="text-muted small">无法同步</span>';
                }
                
                html += `
                    <tr>
                        <td>${item.rowNumber}</td>
                        <td>${item.name}</td>
                        <td>${item.jobNumber}</td>
                        <td>${item.departmentCode || '-'}</td>
                        <td><span class="${matchStatusClass}">${item.matchStatus}</span></td>
                        <td>${matchedName}</td>
                        <td><span class="${sourceClass}">${dataSource}</span></td>
                        <td><span class="${syncStatusClass}">${item.isSynced ? '已同步' : '未同步'}</span></td>
                        <td>${actionButtons}</td>
                    </tr>
                `;
            });
            
            if (html === '') {
                html = '<tr><td colspan="9" class="text-center text-muted">没有符合条件的数据</td></tr>';
            }
            
            tableBody.innerHTML = html;
        }

        // 筛选结果
        function filterResults(status) {
            // 更新按钮状态
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 显示筛选后的结果
            showResultTable(importData, status);
        }

        // 同步未同步人员
        function syncUnsynced() {
            const unsyncedPersons = importData.filter(item => item.isMatched && !item.isSynced);
            
            if (unsyncedPersons.length === 0) {
                alert('没有需要同步的人员');
                return;
            }
            
            if (confirm(`确定要同步 ${unsyncedPersons.length} 名未同步人员吗？这将把人员信息同步到EGS平台。`)) {
                performPersonSync(unsyncedPersons);
            }
        }

        // 同步单个人员
        function syncSinglePerson(rowNumber, jobNumber) {
            // 从importData中查找对应的人员
            const person = importData.find(item => item.rowNumber === rowNumber && item.jobNumber === jobNumber);
            
            if (!person) {
                alert('未找到要同步的人员信息');
                return;
            }
            
            if (!person.isMatched) {
                alert('该人员未匹配，无法同步');
                return;
            }
            
            if (person.isSynced) {
                alert('该人员已同步，无需重复同步');
                return;
            }
            
            if (confirm(`确定要同步人员 "${person.name}"（工号：${person.jobNumber}）吗？`)) {
                performSinglePersonSync(person);
            }
        }

        // 执行人员同步
        function performPersonSync(unsyncedPersons) {
            // 显示同步进度
            const syncBtn = document.querySelector('button[onclick="syncUnsynced()"]');
            const originalText = syncBtn.innerHTML;
            syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
            syncBtn.disabled = true;
            
            const requestData = {
                importData: unsyncedPersons
            };
            
            fetch('/api/person/import/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('同步响应:', data);
                
                if (data.success) {
                    // 更新importData中的同步状态
                    if (data.data && Array.isArray(data.data)) {
                        data.data.forEach(syncedPerson => {
                            const originalPerson = importData.find(p => p.jobNumber === syncedPerson.jobNumber);
                            if (originalPerson) {
                                originalPerson.isSynced = true;
                                originalPerson.matchStatus = originalPerson.matchStatus.replace('未同步', '已同步');
                            }
                        });
                    }
                    
                    // 刷新显示的表格
                    showResultTable(importData);
                    
                    // 显示同步结果
                    const statistics = data.statistics;
                    let message = `同步完成！\n总计: ${statistics.totalCount} 人\n成功: ${statistics.successCount} 人\n失败: ${statistics.failCount} 人`;
                    
                    if (data.failedPersons && data.failedPersons.length > 0) {
                        message += '\n\n失败详情:\n' + data.failedPersons.join('\n');
                    }
                    
                    alert(message);
                    
                } else {
                    alert('同步失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('同步请求错误:', error);
                alert('同步过程出现错误：' + error.message);
            })
            .finally(() => {
                // 恢复按钮状态
                syncBtn.innerHTML = originalText;
                syncBtn.disabled = false;
            });
        }

        // 执行单个人员同步
        function performSinglePersonSync(person) {
            // 查找并禁用对应的同步按钮
            const syncButtons = document.querySelectorAll(`button[onclick="syncSinglePerson(${person.rowNumber}, '${person.jobNumber}')"]`);
            let originalButtonText = '';
            
            syncButtons.forEach(btn => {
                originalButtonText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中';
                btn.disabled = true;
            });
            
            const requestData = {
                personData: person
            };
            
            fetch('/api/person/import/sync-single', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('单个人员同步响应:', data);
                
                if (data.success) {
                    // 更新importData中的同步状态
                    person.isSynced = true;
                    person.matchStatus = person.matchStatus.replace('未同步', '已同步');
                    
                    // 刷新显示的表格
                    showResultTable(importData);
                    
                    // 显示同步结果
                    alert(`人员 "${person.name}"（工号：${person.jobNumber}）同步成功！`);
                    
                } else {
                    alert('同步失败：' + data.message);
                    
                    // 恢复按钮状态
                    syncButtons.forEach(btn => {
                        btn.innerHTML = originalButtonText;
                        btn.disabled = false;
                    });
                }
            })
            .catch(error => {
                console.error('单个人员同步请求错误:', error);
                alert('同步过程出现错误：' + error.message);
                
                // 恢复按钮状态
                syncButtons.forEach(btn => {
                    btn.innerHTML = originalButtonText;
                    btn.disabled = false;
                });
            });
        }

        // 重置导入流程
        function resetImportProcess() {
            currentStep = 1;
            importData = [];
            
            // 重置标签页状态
            document.getElementById('step1-tab').classList.add('active');
            document.getElementById('step2-tab').classList.remove('active');
            document.getElementById('step3-tab').classList.remove('active');
            document.getElementById('step2-tab').classList.add('disabled');
            document.getElementById('step3-tab').classList.add('disabled');
            
            document.getElementById('step1-content').classList.add('active', 'show');
            document.getElementById('step2-content').classList.remove('active', 'show');
            document.getElementById('step3-content').classList.remove('active', 'show');
            
            // 重置按钮状态
            document.getElementById('nextStepBtn').style.display = 'none';
            document.getElementById('startMatchBtn').style.display = 'none';
            
            // 重置上传区域
            resetUploadArea();
        }

        // 模态窗口关闭时重置
        const importModal = document.getElementById('importExcelModal');
        if (importModal) {
            importModal.addEventListener('hidden.bs.modal', function() {
                resetImportProcess();
            });
        }

        // ==================== 未归寝室人员查询功能 ====================

        let dormitoryCurrentPage = 0;
        let dormitoryPageSize = 20;
        let dormitoryTotalPages = 0;
        let dormitoryTotalElements = 0;
        let currentQueryDate = '';
        let currentQueryType = 'not-returned';

        // 处理未归寝室标签页点击
        function handleDormitoryStatusTabClick() {
            console.log('切换到未归寝室人员查询标签页');
            initializeDormitoryQuery();
        }

        // 初始化未归寝室查询
        function initializeDormitoryQuery() {
            // 设置默认查询日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('queryDate').value = today;
            currentQueryDate = today;

            // 重置页面状态
            resetDormitoryQuery();
        }

        // 重置查询条件
        function resetDormitoryQuery() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('queryDate').value = today;
            document.getElementById('queryType').value = 'not-returned';

            currentQueryDate = today;
            currentQueryType = 'not-returned';
            dormitoryCurrentPage = 0;

            // 隐藏结果区域
            document.getElementById('dormitoryStatsCards').style.display = 'none';
            document.getElementById('dormitoryResultTable').style.display = 'none';
            document.getElementById('dormitoryPaginationContainer').style.display = 'none';
            document.getElementById('dormitoryPageInfo').style.display = 'none';
            document.getElementById('dormitoryEmptyState').style.display = 'block';
            document.getElementById('dormitoryLoadingState').style.display = 'none';

            // 重置统计信息
            updateDormitoryStatistics({});
            updateResultTitle('查询结果', 0, today);
        }

        // 刷新当前查询
        function refreshDormitoryStatus() {
            if (currentQueryDate) {
                queryDormitoryStatus();
            } else {
                initializeDormitoryQuery();
            }
        }

        // 执行查询
        function queryDormitoryStatus() {
            const queryDate = document.getElementById('queryDate').value;
            const queryType = document.getElementById('queryType').value;

            if (!queryDate) {
                alert('请选择查询日期');
                return;
            }

            currentQueryDate = queryDate;
            currentQueryType = queryType;
            dormitoryCurrentPage = 0;

            // 显示加载状态
            showDormitoryLoadingState();

            // 首先获取统计信息
            fetchDormitoryStatistics(queryDate).then(() => {
                // 然后根据查询类型获取具体数据
                if (queryType === 'statistics') {
                    showStatisticsOnly();
                } else {
                    fetchDormitoryData(queryDate, queryType, dormitoryCurrentPage, dormitoryPageSize);
                }
            });
        }

        // 显示加载状态
        function showDormitoryLoadingState() {
            document.getElementById('dormitoryEmptyState').style.display = 'none';
            document.getElementById('dormitoryResultTable').style.display = 'none';
            document.getElementById('dormitoryPaginationContainer').style.display = 'none';
            document.getElementById('dormitoryPageInfo').style.display = 'none';
            document.getElementById('dormitoryLoadingState').style.display = 'block';
        }

        // 获取统计信息
        async function fetchDormitoryStatistics(date) {
            try {
                const response = await fetch(`/api/dormitory-status/statistics?date=${date}`);
                const data = await response.json();

                if (data.success) {
                    updateDormitoryStatistics(data);
                    document.getElementById('dormitoryStatsCards').style.display = 'flex';
                } else {
                    console.error('获取统计信息失败:', data.message);
                    showErrorMessage('获取统计信息失败: ' + data.message);
                }
            } catch (error) {
                console.error('获取统计信息异常:', error);
                showErrorMessage('获取统计信息异常: ' + error.message);
            }
        }

        // 获取寝室归宿数据
        async function fetchDormitoryData(date, type, page, size) {
            try {
                let url = '';
                if (type === 'not-returned') {
                    url = `/api/dormitory-status/not-returned/page?date=${date}&page=${page}&size=${size}`;
                } else if (type === 'returned') {
                    url = `/api/dormitory-status/returned?date=${date}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                document.getElementById('dormitoryLoadingState').style.display = 'none';

                if (data.success) {
                    if (type === 'not-returned') {
                        displayDormitoryPagedResults(data, '未归寝室人员');
                    } else {
                        displayDormitoryResults(data.data, '已归寝室人员');
                    }
                    updateResultTitle(type === 'not-returned' ? '未归寝室人员' : '已归寝室人员',
                                    data.data ? data.data.length : (data.totalElements || 0), date);
                } else {
                    showErrorMessage('查询失败: ' + data.message);
                }
            } catch (error) {
                document.getElementById('dormitoryLoadingState').style.display = 'none';
                console.error('查询异常:', error);
                showErrorMessage('查询异常: ' + error.message);
            }
        }

        // 仅显示统计信息
        function showStatisticsOnly() {
            document.getElementById('dormitoryLoadingState').style.display = 'none';
            document.getElementById('dormitoryResultTable').style.display = 'none';
            document.getElementById('dormitoryPaginationContainer').style.display = 'none';
            document.getElementById('dormitoryPageInfo').style.display = 'none';
            document.getElementById('dormitoryEmptyState').style.display = 'none';

            updateResultTitle('统计信息', 0, currentQueryDate);
        }

        // 更新统计信息显示
        function updateDormitoryStatistics(data) {
            document.getElementById('totalPersonsCount').textContent = data.totalPersons || 0;
            document.getElementById('notReturnedCount').textContent = data.notReturnedCount || 0;
            document.getElementById('returnedCount').textContent = data.returnedCount || 0;
            document.getElementById('returnRate').textContent = (data.returnRate || 0) + '%';
        }

        // 更新结果标题
        function updateResultTitle(title, count, date) {
            document.getElementById('resultTitle').textContent = title;
            document.getElementById('resultCount').textContent = count + ' 人';
            document.getElementById('queryDateBadge').textContent = date;
        }

        // 显示分页结果
        function displayDormitoryPagedResults(data, title) {
            dormitoryTotalElements = data.totalElements;
            dormitoryTotalPages = data.totalPages;
            dormitoryCurrentPage = data.currentPage;

            displayDormitoryResults(data.data, title);

            // 显示分页控件
            if (dormitoryTotalPages > 1) {
                document.getElementById('dormitoryPaginationContainer').style.display = 'flex';
                generateDormitoryPagination();
            }

            // 显示分页信息
            updateDormitoryPageInfo();
        }

        // 显示查询结果
        function displayDormitoryResults(data, title) {
            const tableHeader = document.getElementById('dormitoryTableHeader');
            const tableBody = document.getElementById('dormitoryTableBody');

            // 生成表头
            tableHeader.innerHTML = `
                <th>序号</th>
                <th>人员姓名</th>
                <th>人员编码</th>
                <th>部门</th>
                <th>人员类型</th>
                <th>性别</th>
                <th>最后通行时间</th>
                <th>通行类型</th>
                <th>归宿状态</th>
                <th>通行设备</th>
                <th>通行区域</th>
            `;

            // 生成表格内容
            let html = '';
            if (data && data.length > 0) {
                data.forEach((person, index) => {
                    const rowNumber = dormitoryCurrentPage * dormitoryPageSize + index + 1;
                    const statusClass = person.isInDormitory ? 'text-success' : 'text-danger';
                    const statusIcon = person.isInDormitory ? 'fas fa-check-circle' : 'fas fa-times-circle';

                    html += `
                        <tr>
                            <td>${rowNumber}</td>
                            <td>${person.personName || '-'}</td>
                            <td>${person.personCode || '-'}</td>
                            <td>${person.departmentName || person.departmentCode || '-'}</td>
                            <td>${person.personTypeDesc || '-'}</td>
                            <td>${person.genderDesc || '-'}</td>
                            <td>${person.lastPassTimeStr || '-'}</td>
                            <td>
                                <span class="badge ${person.lastInOrOut === 1 ? 'bg-success' : 'bg-warning'}">
                                    ${person.lastInOrOutDesc || '-'}
                                </span>
                            </td>
                            <td>
                                <span class="${statusClass}">
                                    <i class="${statusIcon}"></i> ${person.dormitoryStatusDesc || '-'}
                                </span>
                            </td>
                            <td>${person.lastDeviceName || '-'}</td>
                            <td>${person.lastAreaName || '-'}</td>
                        </tr>
                    `;
                });
            } else {
                html = `
                    <tr>
                        <td colspan="11" class="text-center text-muted py-4">
                            <i class="fas fa-info-circle"></i> 暂无${title}数据
                        </td>
                    </tr>
                `;
            }

            tableBody.innerHTML = html;
            document.getElementById('dormitoryResultTable').style.display = 'block';
            document.getElementById('dormitoryEmptyState').style.display = 'none';
        }

        // 生成分页控件
        function generateDormitoryPagination() {
            const pagination = document.getElementById('dormitoryPagination');
            let html = '';

            // 上一页
            const prevDisabled = dormitoryCurrentPage === 0 ? 'disabled' : '';
            html += `
                <li class="page-item ${prevDisabled}">
                    <a class="page-link" href="#" onclick="changeDormitoryPage(${dormitoryCurrentPage - 1})">上一页</a>
                </li>
            `;

            // 页码
            const startPage = Math.max(0, dormitoryCurrentPage - 2);
            const endPage = Math.min(dormitoryTotalPages - 1, dormitoryCurrentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === dormitoryCurrentPage ? 'active' : '';
                html += `
                    <li class="page-item ${activeClass}">
                        <a class="page-link" href="#" onclick="changeDormitoryPage(${i})">${i + 1}</a>
                    </li>
                `;
            }

            // 下一页
            const nextDisabled = dormitoryCurrentPage >= dormitoryTotalPages - 1 ? 'disabled' : '';
            html += `
                <li class="page-item ${nextDisabled}">
                    <a class="page-link" href="#" onclick="changeDormitoryPage(${dormitoryCurrentPage + 1})">下一页</a>
                </li>
            `;

            pagination.innerHTML = html;
        }

        // 更新分页信息
        function updateDormitoryPageInfo() {
            const pageInfo = document.getElementById('dormitoryPageInfo');
            const pageInfoText = document.getElementById('dormitoryPageInfoText');

            if (dormitoryTotalElements > 0) {
                const startRecord = dormitoryCurrentPage * dormitoryPageSize + 1;
                const endRecord = Math.min((dormitoryCurrentPage + 1) * dormitoryPageSize, dormitoryTotalElements);

                pageInfoText.textContent = `第 ${dormitoryCurrentPage + 1} 页，共 ${dormitoryTotalPages} 页，总计 ${dormitoryTotalElements} 条记录，当前显示第 ${startRecord} - ${endRecord} 条`;
                pageInfo.style.display = 'block';
            } else {
                pageInfo.style.display = 'none';
            }
        }

        // 切换页码
        function changeDormitoryPage(page) {
            if (page < 0 || page >= dormitoryTotalPages || page === dormitoryCurrentPage) {
                return;
            }

            dormitoryCurrentPage = page;
            fetchDormitoryData(currentQueryDate, currentQueryType, dormitoryCurrentPage, dormitoryPageSize);
        }

        // 改变每页显示数量
        function changeDormitoryPageSize() {
            const newPageSize = parseInt(document.getElementById('dormitoryPageSize').value);
            if (newPageSize !== dormitoryPageSize) {
                dormitoryPageSize = newPageSize;
                dormitoryCurrentPage = 0;
                fetchDormitoryData(currentQueryDate, currentQueryType, dormitoryCurrentPage, dormitoryPageSize);
            }
        }

        // 显示错误消息
        function showErrorMessage(message) {
            document.getElementById('dormitoryLoadingState').style.display = 'none';
            document.getElementById('dormitoryResultTable').style.display = 'none';
            document.getElementById('dormitoryPaginationContainer').style.display = 'none';
            document.getElementById('dormitoryPageInfo').style.display = 'none';

            const emptyState = document.getElementById('dormitoryEmptyState');
            emptyState.innerHTML = `
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h6 class="text-warning">查询出错</h6>
                <p class="text-muted small">${message}</p>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshDormitoryStatus()">
                    <i class="fas fa-refresh"></i> 重试
                </button>
            `;
            emptyState.style.display = 'block';
        }
    </script>
</body>

<!-- 同步预览模态框 -->
<div class="modal fade" id="syncPreviewModal" tabindex="-1" aria-labelledby="syncPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="syncPreviewModalLabel">
                    <i class="fas fa-eye"></i> 人员宿舍同步预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="syncPreviewContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmSyncBtn" onclick="confirmAndExecuteSync()">
                    <i class="fas fa-check"></i> 确认同步
                </button>
            </div>
        </div>
    </div>
</div>

</html>