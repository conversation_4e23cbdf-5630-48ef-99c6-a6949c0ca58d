package fastgatedemo.demo.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import fastgatedemo.demo.dto.DormitoryStatusDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * @description 大屏WebSocket处理器
 * 处理大屏实时数据推送的WebSocket连接
 * <AUTHOR>
 * @date 2025-01-31
 */
@Component
public class DashboardWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(DashboardWebSocketHandler.class);
    
    // 存储所有活跃的WebSocket会话
    private final CopyOnWriteArraySet<WebSocketSession> sessions = new CopyOnWriteArraySet<>();
    
    // 存储会话元数据
    private final Map<String, Object> sessionMetadata = new ConcurrentHashMap<>();
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
        sessionMetadata.put(session.getId(), System.currentTimeMillis());
        logger.info("WebSocket连接建立: sessionId={}, 当前连接数: {}", session.getId(), sessions.size());
        
        // 向新连接发送欢迎消息
        sendWelcomeMessage(session);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        logger.debug("收到WebSocket消息: sessionId={}, message={}", session.getId(), message.getPayload());
        
        // 处理客户端发送的消息，比如心跳检测
        if ("ping".equals(message.getPayload().toString())) {
            session.sendMessage(new TextMessage("pong"));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket传输错误: sessionId={}, error={}", session.getId(), exception.getMessage(), exception);
        removeSession(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        removeSession(session);
        logger.info("WebSocket连接关闭: sessionId={}, status={}, 当前连接数: {}", 
                session.getId(), closeStatus, sessions.size());
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 移除会话
     */
    private void removeSession(WebSocketSession session) {
        sessions.remove(session);
        sessionMetadata.remove(session.getId());
    }

    /**
     * 发送欢迎消息
     */
    private void sendWelcomeMessage(WebSocketSession session) {
        try {
            Map<String, Object> welcomeMsg = new HashMap<>();
            welcomeMsg.put("type", "welcome");
            welcomeMsg.put("message", "连接成功");
            welcomeMsg.put("timestamp", System.currentTimeMillis());
            
            String jsonMessage = objectMapper.writeValueAsString(welcomeMsg);
            session.sendMessage(new TextMessage(jsonMessage));
        } catch (Exception e) {
            logger.error("发送欢迎消息失败: sessionId={}, error={}", session.getId(), e.getMessage(), e);
        }
    }

    /**
     * 推送新的进出记录
     */
    public void pushNewRecord(DormitoryStatusDTO record) {
        if (sessions.isEmpty()) {
            logger.debug("没有WebSocket连接，跳过推送记录");
            return;
        }

        try {
            Map<String, Object> message = new HashMap<>();
            message.put("type", "newRecord");
            message.put("data", record);
            message.put("timestamp", System.currentTimeMillis());
            
            String jsonMessage = objectMapper.writeValueAsString(message);
            broadcastMessage(jsonMessage);
            
            logger.debug("推送新记录: personCode={}, personName={}, inOrOut={}", 
                    record.getPersonCode(), record.getPersonName(), record.getLastInOrOutDesc());
        } catch (Exception e) {
            logger.error("推送新记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 推送批量记录
     */
    public void pushBatchRecords(java.util.List<DormitoryStatusDTO> records) {
        if (sessions.isEmpty()) {
            logger.debug("没有WebSocket连接，跳过推送批量记录");
            return;
        }

        try {
            Map<String, Object> message = new HashMap<>();
            message.put("type", "batchRecords");
            message.put("data", records);
            message.put("count", records.size());
            message.put("timestamp", System.currentTimeMillis());
            
            String jsonMessage = objectMapper.writeValueAsString(message);
            broadcastMessage(jsonMessage);
            
            logger.info("推送批量记录: {}条", records.size());
        } catch (Exception e) {
            logger.error("推送批量记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送心跳消息
     */
    public void sendHeartbeat() {
        if (sessions.isEmpty()) {
            return;
        }

        try {
            Map<String, Object> heartbeat = new HashMap<>();
            heartbeat.put("type", "heartbeat");
            heartbeat.put("timestamp", System.currentTimeMillis());
            heartbeat.put("connections", sessions.size());
            
            String jsonMessage = objectMapper.writeValueAsString(heartbeat);
            broadcastMessage(jsonMessage);
            
            logger.debug("发送心跳消息，连接数: {}", sessions.size());
        } catch (Exception e) {
            logger.error("发送心跳消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 广播消息到所有连接
     */
    public void broadcastMessage(Object message) {
        if (sessions.isEmpty()) {
            return;
        }

        String jsonMessage;
        try {
            if (message instanceof String) {
                jsonMessage = (String) message;
            } else {
                jsonMessage = objectMapper.writeValueAsString(message);
            }
        } catch (Exception e) {
            logger.error("序列化广播消息失败: {}", e.getMessage(), e);
            return;
        }

        // 使用迭代器安全地遍历并发集合
        sessions.removeIf(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(jsonMessage));
                    return false; // 保留会话
                } else {
                    logger.debug("移除已关闭的会话: {}", session.getId());
                    sessionMetadata.remove(session.getId());
                    return true; // 移除会话
                }
            } catch (IOException e) {
                logger.error("发送消息失败，移除会话: sessionId={}, error={}", session.getId(), e.getMessage());
                sessionMetadata.remove(session.getId());
                return true; // 移除有问题的会话
            }
        });

        logger.debug("广播消息完成，当前连接数: {}", sessions.size());
    }

    /**
     * 获取当前连接数
     */
    public int getConnectionCount() {
        return sessions.size();
    }

    /**
     * 获取所有会话ID
     */
    public java.util.Set<String> getSessionIds() {
        return sessions.stream()
                .map(WebSocketSession::getId)
                .collect(java.util.stream.Collectors.toSet());
    }
}