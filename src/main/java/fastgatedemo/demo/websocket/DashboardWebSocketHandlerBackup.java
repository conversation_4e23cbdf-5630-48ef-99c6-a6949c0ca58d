package fastgatedemo.demo.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import fastgatedemo.demo.dto.DormitoryStatusDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * @description 大屏WebSocket处理器备用版本
 * 当WebSocket依赖有问题时使用的简化版本
 * <AUTHOR>
 * @date 2025-01-31
 */
@Component
public class DashboardWebSocketHandlerBackup {

    private static final Logger logger = LoggerFactory.getLogger(DashboardWebSocketHandlerBackup.class);
    
    // 模拟存储连接数
    private volatile int connectionCount = 0;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 模拟连接建立
     */
    public void simulateConnectionEstablished() {
        connectionCount++;
        logger.info("模拟WebSocket连接建立，当前连接数: {}", connectionCount);
    }

    /**
     * 模拟连接关闭
     */
    public void simulateConnectionClosed() {
        if (connectionCount > 0) {
            connectionCount--;
        }
        logger.info("模拟WebSocket连接关闭，当前连接数: {}", connectionCount);
    }

    /**
     * 获取连接数
     */
    public int getConnectionCount() {
        return connectionCount;
    }

    /**
     * 模拟推送新的进出记录
     */
    public void pushNewRecord(DormitoryStatusDTO record) {
        logger.info("模拟推送新的进出记录: personCode={}, personName={}, inOrOut={}, buildingName={}", 
                record.getPersonCode(), record.getPersonName(), record.getLastInOrOutDesc(), record.getBuildingName());
        
        // 在实际WebSocket不可用时，这里可以：
        // 1. 记录到日志
        // 2. 存储到缓存供前端轮询
        // 3. 发送到消息队列
        
        try {
            String jsonMessage = objectMapper.writeValueAsString(record);
            logger.debug("模拟WebSocket消息: {}", jsonMessage);
        } catch (Exception e) {
            logger.error("序列化记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 模拟推送批量记录
     */
    public void pushBatchRecords(java.util.List<DormitoryStatusDTO> records) {
        logger.info("模拟推送批量记录: {}条", records.size());
        
        for (DormitoryStatusDTO record : records) {
            pushNewRecord(record);
        }
    }

    /**
     * 模拟发送心跳消息
     */
    public void sendHeartbeat() {
        if (connectionCount > 0) {
            logger.debug("模拟发送WebSocket心跳消息，连接数: {}", connectionCount);
        }
    }

    /**
     * 模拟广播消息
     */
    public void broadcastMessage(Object message) {
        if (connectionCount == 0) {
            logger.debug("没有WebSocket连接，跳过广播");
            return;
        }
        
        logger.info("模拟广播WebSocket消息到{}个客户端", connectionCount);
        
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            logger.debug("模拟广播消息: {}", jsonMessage);
        } catch (Exception e) {
            logger.error("序列化广播消息失败: {}", e.getMessage(), e);
        }
    }
}
