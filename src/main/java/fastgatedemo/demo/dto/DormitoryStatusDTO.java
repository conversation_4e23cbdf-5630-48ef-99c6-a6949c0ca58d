package fastgatedemo.demo.dto;

import java.time.LocalDateTime;

/**
 * @description 寝室归宿状态DTO
 * 用于返回人员寝室归宿状态信息
 * <AUTHOR>
 * @date 2025-01-25
 */
public class DormitoryStatusDTO {
    
    /**
     * 人员编码
     */
    private String personCode;
    
    /**
     * 人员姓名
     */
    private String personName;
    
    /**
     * 部门编码
     */
    private String departmentCode;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 人员类型 (1-学生, 2-教师, 3-其他)
     */
    private Integer personType;
    
    /**
     * 性别 (1-男, 2-女)
     */
    private Integer gender;
    
    /**
     * 电话号码
     */
    private String telephone;
    
    /**
     * 查询日期
     */
    private String queryDate;
    
    /**
     * 最后通行时间
     */
    private LocalDateTime lastPassTime;
    
    /**
     * 最后通行记录时间 (格式化字符串)
     */
    private String lastPassTimeStr;
    
    /**
     * 最后通行类型
     * 1 = 进入寝室
     * 2 = 离开寝室
     */
    private Integer lastInOrOut;
    
    /**
     * 最后通行类型描述
     */
    private String lastInOrOutDesc;
    
    /**
     * 归宿状态
     * true = 已归寝室 (最后记录为进入)
     * false = 未归寝室 (最后记录为离开)
     */
    private Boolean isInDormitory;
    
    /**
     * 归宿状态描述
     */
    private String dormitoryStatusDesc;
    
    /**
     * 最后通行设备名称
     */
    private String lastDeviceName;
    
    /**
     * 最后通行区域名称
     */
    private String lastAreaName;
    
    /**
     * 宿舍编码 (如果有宿舍关联信息)
     */
    private String dormitoryCode;
    
    /**
     * 宿舍名称 (如果有宿舍关联信息)
     */
    private String dormitoryName;
    
    /**
     * 楼栋名称
     */
    private String buildingName;
    
    /**
     * 楼层
     */
    private String floor;
    
    /**
     * 房间号
     */
    private String roomNumber;

    // ==================== 构造函数 ====================
    
    public DormitoryStatusDTO() {
    }

    public DormitoryStatusDTO(String personCode, String personName, String queryDate, 
                             LocalDateTime lastPassTime, Integer lastInOrOut, Boolean isInDormitory) {
        this.personCode = personCode;
        this.personName = personName;
        this.queryDate = queryDate;
        this.lastPassTime = lastPassTime;
        this.lastInOrOut = lastInOrOut;
        this.isInDormitory = isInDormitory;
        this.lastInOrOutDesc = getInOrOutDescription(lastInOrOut);
        this.dormitoryStatusDesc = isInDormitory ? "已归寝室" : "未归寝室";
    }

    // ==================== Getter和Setter方法 ====================
    
    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getPersonType() {
        return personType;
    }

    public void setPersonType(Integer personType) {
        this.personType = personType;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getQueryDate() {
        return queryDate;
    }

    public void setQueryDate(String queryDate) {
        this.queryDate = queryDate;
    }

    public LocalDateTime getLastPassTime() {
        return lastPassTime;
    }

    public void setLastPassTime(LocalDateTime lastPassTime) {
        this.lastPassTime = lastPassTime;
    }

    public String getLastPassTimeStr() {
        return lastPassTimeStr;
    }

    public void setLastPassTimeStr(String lastPassTimeStr) {
        this.lastPassTimeStr = lastPassTimeStr;
    }

    public Integer getLastInOrOut() {
        return lastInOrOut;
    }

    public void setLastInOrOut(Integer lastInOrOut) {
        this.lastInOrOut = lastInOrOut;
        this.lastInOrOutDesc = getInOrOutDescription(lastInOrOut);
    }

    public String getLastInOrOutDesc() {
        return lastInOrOutDesc;
    }

    public void setLastInOrOutDesc(String lastInOrOutDesc) {
        this.lastInOrOutDesc = lastInOrOutDesc;
    }

    public Boolean getIsInDormitory() {
        return isInDormitory;
    }

    public void setIsInDormitory(Boolean isInDormitory) {
        this.isInDormitory = isInDormitory;
        this.dormitoryStatusDesc = isInDormitory != null && isInDormitory ? "已归寝室" : "未归寝室";
    }

    public String getDormitoryStatusDesc() {
        return dormitoryStatusDesc;
    }

    public void setDormitoryStatusDesc(String dormitoryStatusDesc) {
        this.dormitoryStatusDesc = dormitoryStatusDesc;
    }

    public String getLastDeviceName() {
        return lastDeviceName;
    }

    public void setLastDeviceName(String lastDeviceName) {
        this.lastDeviceName = lastDeviceName;
    }

    public String getLastAreaName() {
        return lastAreaName;
    }

    public void setLastAreaName(String lastAreaName) {
        this.lastAreaName = lastAreaName;
    }

    public String getDormitoryCode() {
        return dormitoryCode;
    }

    public void setDormitoryCode(String dormitoryCode) {
        this.dormitoryCode = dormitoryCode;
    }

    public String getDormitoryName() {
        return dormitoryName;
    }

    public void setDormitoryName(String dormitoryName) {
        this.dormitoryName = dormitoryName;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getRoomNumber() {
        return roomNumber;
    }

    public void setRoomNumber(String roomNumber) {
        this.roomNumber = roomNumber;
    }

    // ==================== 业务方法 ====================
    
    /**
     * 获取人员类型描述
     * @return 人员类型描述
     */
    public String getPersonTypeDesc() {
        if (personType == null) return "未知";
        switch (personType) {
            case 1: return "学生";
            case 2: return "教师";
            case 3: return "其他";
            default: return "未知";
        }
    }

    /**
     * 获取性别描述
     * @return 性别描述
     */
    public String getGenderDesc() {
        if (gender == null) return "未知";
        return gender == 1 ? "男" : "女";
    }

    /**
     * 获取进出状态描述
     * @param inOrOut 进出状态
     * @return 状态描述
     */
    private String getInOrOutDescription(Integer inOrOut) {
        if (inOrOut == null) return "未知";
        return inOrOut == 1 ? "进入" : "离开";
    }

    /**
     * 获取完整的宿舍信息描述
     * @return 宿舍信息描述
     */
    public String getFullDormitoryInfo() {
        StringBuilder sb = new StringBuilder();
        if (buildingName != null) {
            sb.append(buildingName);
        }
        if (floor != null) {
            sb.append(" ").append(floor).append("层");
        }
        if (roomNumber != null) {
            sb.append(" ").append(roomNumber).append("室");
        }
        if (dormitoryName != null && !dormitoryName.equals(sb.toString())) {
            sb.append(" (").append(dormitoryName).append(")");
        }
        return sb.toString().trim();
    }

    // ==================== toString方法 ====================
    
    @Override
    public String toString() {
        return "DormitoryStatusDTO{" +
                "personCode='" + personCode + '\'' +
                ", personName='" + personName + '\'' +
                ", queryDate='" + queryDate + '\'' +
                ", lastPassTime=" + lastPassTime +
                ", lastInOrOut=" + lastInOrOut +
                ", isInDormitory=" + isInDormitory +
                ", dormitoryStatusDesc='" + dormitoryStatusDesc + '\'' +
                ", dormitoryName='" + dormitoryName + '\'' +
                '}';
    }
}
