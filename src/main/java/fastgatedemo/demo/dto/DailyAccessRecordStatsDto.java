package fastgatedemo.demo.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @description 每日出入记录统计数据传输对象
 */
public class DailyAccessRecordStatsDto {

    /**
     * 统计日期
     */
    private LocalDate statsDate;

    /**
     * 当天校验成功的总人数
     */
    private Integer totalSuccessCount;

    /**
     * 查询开始时间
     */
    private String startTime;

    /**
     * 查询结束时间
     */
    private String endTime;

    /**
     * 实际查询的记录总数（可能包含多页数据）
     */
    private Integer totalRecords;

    /**
     * 查询时间
     */
    private LocalDateTime queryTime;

    /**
     * 查询耗时（毫秒）
     */
    private Long queryDurationMs;

    /**
     * 备注信息
     */
    private String remarks;

    // 构造函数
    public DailyAccessRecordStatsDto() {
        this.queryTime = LocalDateTime.now();
    }

    public DailyAccessRecordStatsDto(LocalDate statsDate, Integer totalSuccessCount) {
        this();
        this.statsDate = statsDate;
        this.totalSuccessCount = totalSuccessCount;
    }

    // Getter和Setter方法
    public LocalDate getStatsDate() {
        return statsDate;
    }

    public void setStatsDate(LocalDate statsDate) {
        this.statsDate = statsDate;
    }

    public Integer getTotalSuccessCount() {
        return totalSuccessCount;
    }

    public void setTotalSuccessCount(Integer totalSuccessCount) {
        this.totalSuccessCount = totalSuccessCount;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Integer totalRecords) {
        this.totalRecords = totalRecords;
    }

    public LocalDateTime getQueryTime() {
        return queryTime;
    }

    public void setQueryTime(LocalDateTime queryTime) {
        this.queryTime = queryTime;
    }

    public Long getQueryDurationMs() {
        return queryDurationMs;
    }

    public void setQueryDurationMs(Long queryDurationMs) {
        this.queryDurationMs = queryDurationMs;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "DailyAccessRecordStatsDto{" +
                "statsDate=" + statsDate +
                ", totalSuccessCount=" + totalSuccessCount +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", totalRecords=" + totalRecords +
                ", queryTime=" + queryTime +
                ", queryDurationMs=" + queryDurationMs +
                ", remarks='" + remarks + '\'' +
                '}';
    }
} 