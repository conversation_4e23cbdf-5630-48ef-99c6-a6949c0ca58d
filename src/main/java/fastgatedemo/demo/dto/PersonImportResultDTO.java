package fastgatedemo.demo.dto;

import fastgatedemo.demo.model.MiddlewarePersonInfo;
import fastgatedemo.demo.model.PersonInfo;

/**
 * @description 人员导入结果数据传输对象
 * 用于封装Excel导入后的匹配结果和同步状态信息
 */
public class PersonImportResultDTO {

    /**
     * 匹配数据来源枚举
     */
    public enum MatchSource {
        MIDDLEWARE("中间库"),
        LOCAL("本地库"), 
        NONE("未匹配");

        private final String description;

        MatchSource(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * Excel中的姓名
     */
    private String name;

    /**
     * Excel中的工号
     */
    private String jobNumber;

    /**
     * Excel中的部门编码
     */
    private String departmentCode;

    /**
     * 匹配到的中间库人员信息
     */
    private MiddlewarePersonInfo matchedPerson;

    /**
     * 匹配到的本地人员信息
     */
    private PersonInfo localMatchedPerson;

    /**
     * 匹配数据来源
     */
    private MatchSource matchSource;

    /**
     * 本地人员编码（用于关联本地表）
     */
    private String localPersonCode;

    /**
     * 是否匹配成功
     */
    private Boolean isMatched;

    /**
     * 是否已同步到本地
     */
    private Boolean isSynced;

    /**
     * 匹配状态描述
     */
    private String matchStatus;

    /**
     * Excel中的行号（用于错误定位）
     */
    private Integer rowNumber;

    // 构造函数
    public PersonImportResultDTO() {
    }

    public PersonImportResultDTO(String name, String jobNumber, String departmentCode, Integer rowNumber) {
        this.name = name;
        this.jobNumber = jobNumber;
        this.departmentCode = departmentCode;
        this.rowNumber = rowNumber;
        this.isMatched = false;
        this.isSynced = false;
        this.matchStatus = "未处理";
        this.matchSource = MatchSource.NONE;
    }

    // Getter and Setter methods
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public MiddlewarePersonInfo getMatchedPerson() {
        return matchedPerson;
    }

    public void setMatchedPerson(MiddlewarePersonInfo matchedPerson) {
        this.matchedPerson = matchedPerson;
    }

    public PersonInfo getLocalMatchedPerson() {
        return localMatchedPerson;
    }

    public void setLocalMatchedPerson(PersonInfo localMatchedPerson) {
        this.localMatchedPerson = localMatchedPerson;
    }

    public MatchSource getMatchSource() {
        return matchSource;
    }

    public void setMatchSource(MatchSource matchSource) {
        this.matchSource = matchSource;
    }

    public String getLocalPersonCode() {
        return localPersonCode;
    }

    public void setLocalPersonCode(String localPersonCode) {
        this.localPersonCode = localPersonCode;
    }

    public Boolean getIsMatched() {
        return isMatched;
    }

    public void setIsMatched(Boolean isMatched) {
        this.isMatched = isMatched;
    }

    public Boolean getIsSynced() {
        return isSynced;
    }

    public void setIsSynced(Boolean isSynced) {
        this.isSynced = isSynced;
    }

    public String getMatchStatus() {
        return matchStatus;
    }

    public void setMatchStatus(String matchStatus) {
        this.matchStatus = matchStatus;
    }

    public Integer getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(Integer rowNumber) {
        this.rowNumber = rowNumber;
    }

    /**
     * 设置中间库匹配成功状态
     */
    public void setMatchSuccess(MiddlewarePersonInfo person) {
        this.matchedPerson = person;
        this.isMatched = true;
        this.matchSource = MatchSource.MIDDLEWARE;
        this.matchStatus = "匹配成功（中间库）";
    }

    /**
     * 设置本地库匹配成功状态
     */
    public void setLocalMatchSuccess(PersonInfo person) {
        this.localMatchedPerson = person;
        this.localPersonCode = person.getPersonCode();
        this.isMatched = true;
        this.matchSource = MatchSource.LOCAL;
        this.matchStatus = "匹配成功（本地库）";
        // 同步状态根据实际的sync_flag字段设置，不默认为已同步
    }

    /**
     * 设置匹配失败状态
     */
    public void setMatchFailed(String reason) {
        this.matchedPerson = null;
        this.localMatchedPerson = null;
        this.isMatched = false;
        this.matchSource = MatchSource.NONE;
        this.matchStatus = "未匹配: " + reason;
    }

    /**
     * 设置同步状态
     */
    public void setSyncStatus(boolean synced) {
        this.isSynced = synced;
        if (this.isMatched && this.matchSource == MatchSource.MIDDLEWARE) {
            this.matchStatus = synced ? "已同步" : "未同步";
        }
    }

    @Override
    public String toString() {
        return "PersonImportResultDTO{" +
                "name='" + name + '\'' +
                ", jobNumber='" + jobNumber + '\'' +
                ", departmentCode='" + departmentCode + '\'' +
                ", matchSource=" + matchSource +
                ", localPersonCode='" + localPersonCode + '\'' +
                ", isMatched=" + isMatched +
                ", isSynced=" + isSynced +
                ", matchStatus='" + matchStatus + '\'' +
                ", rowNumber=" + rowNumber +
                '}';
    }
} 