package fastgatedemo.demo.dto;

import java.time.LocalDateTime;

/**
 * @description EGS平台提取失败人脸记录数据传输对象
 */
public class EGSFailedFaceExtractionDto {

    /**
     * 人员编码
     */
    private String personCode;

    /**
     * 人员姓名
     */
    private String personName;

    /**
     * 部门编码
     */
    private String departmentCode;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 特征提取状态 (0-未提取, 1-提取成功, 2-提取失败, 3-部分成功, 4-无底库照片, 5-全部)
     */
    private Integer featureStatus;

    /**
     * 人员性别 (1-男, 2-女, 0-未知)
     */
    private Integer sex;

    /**
     * 电话号码
     */
    private String telephone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * IC卡号
     */
    private String icCard;

    /**
     * 照片数量
     */
    private Integer picNum;

    /**
     * 人员状态
     */
    private Integer status;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 查询时间
     */
    private LocalDateTime queryTime;

    // 构造函数
    public EGSFailedFaceExtractionDto() {
        this.queryTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getFeatureStatus() {
        return featureStatus;
    }

    public void setFeatureStatus(Integer featureStatus) {
        this.featureStatus = featureStatus;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIcCard() {
        return icCard;
    }

    public void setIcCard(String icCard) {
        this.icCard = icCard;
    }

    public Integer getPicNum() {
        return picNum;
    }

    public void setPicNum(Integer picNum) {
        this.picNum = picNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getQueryTime() {
        return queryTime;
    }

    public void setQueryTime(LocalDateTime queryTime) {
        this.queryTime = queryTime;
    }

    /**
     * 获取性别描述
     */
    public String getSexDescription() {
        if (sex == null) return "未知";
        switch (sex) {
            case 1: return "男";
            case 2: return "女";
            default: return "未知";
        }
    }

    /**
     * 获取特征状态描述
     */
    public String getFeatureStatusDescription() {
        if (featureStatus == null) return "未知";
        switch (featureStatus) {
            case 0: return "未提取";
            case 1: return "提取成功";
            case 2: return "提取失败";
            case 3: return "部分成功";
            case 4: return "无底库照片";
            case 5: return "全部";
            default: return "未知状态";
        }
    }

    @Override
    public String toString() {
        return "EGSFailedFaceExtractionDto{" +
                "personCode='" + personCode + '\'' +
                ", personName='" + personName + '\'' +
                ", departmentCode='" + departmentCode + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", featureStatus=" + featureStatus +
                ", sex=" + sex +
                ", telephone='" + telephone + '\'' +
                ", idCard='" + idCard + '\'' +
                ", icCard='" + icCard + '\'' +
                ", picNum=" + picNum +
                ", status=" + status +
                ", updateTime=" + updateTime +
                ", queryTime=" + queryTime +
                '}';
    }
} 