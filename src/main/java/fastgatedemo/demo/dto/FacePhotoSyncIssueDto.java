package fastgatedemo.demo.dto;

/**
 * @description 人脸照片同步问题数据传输对象
 */
public class FacePhotoSyncIssueDto {
    
    private String personCode;
    private String personName;
    private String departmentCode;
    private String telephone;
    private String idcard;
    private Integer personSyncFlag;
    private String facePhotoId;
    private Integer faceSyncFlag;
    private String issueType;
    private String issueDescription;
    
    // 构造函数
    public FacePhotoSyncIssueDto() {}
    
    public FacePhotoSyncIssueDto(String personCode, String personName, String departmentCode, 
                                String telephone, String idcard, Integer personSyncFlag, 
                                String facePhotoId, Integer faceSyncFlag, String issueType, 
                                String issueDescription) {
        this.personCode = personCode;
        this.personName = personName;
        this.departmentCode = departmentCode;
        this.telephone = telephone;
        this.idcard = idcard;
        this.personSyncFlag = personSyncFlag;
        this.facePhotoId = facePhotoId;
        this.faceSyncFlag = faceSyncFlag;
        this.issueType = issueType;
        this.issueDescription = issueDescription;
    }
    
    // Getter和Setter方法
    public String getPersonCode() {
        return personCode;
    }
    
    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }
    
    public String getPersonName() {
        return personName;
    }
    
    public void setPersonName(String personName) {
        this.personName = personName;
    }
    
    public String getDepartmentCode() {
        return departmentCode;
    }
    
    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }
    
    public String getTelephone() {
        return telephone;
    }
    
    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
    
    public String getIdcard() {
        return idcard;
    }
    
    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }
    
    public Integer getPersonSyncFlag() {
        return personSyncFlag;
    }
    
    public void setPersonSyncFlag(Integer personSyncFlag) {
        this.personSyncFlag = personSyncFlag;
    }
    
    public String getFacePhotoId() {
        return facePhotoId;
    }
    
    public void setFacePhotoId(String facePhotoId) {
        this.facePhotoId = facePhotoId;
    }
    
    public Integer getFaceSyncFlag() {
        return faceSyncFlag;
    }
    
    public void setFaceSyncFlag(Integer faceSyncFlag) {
        this.faceSyncFlag = faceSyncFlag;
    }
    
    public String getIssueType() {
        return issueType;
    }
    
    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }
    
    public String getIssueDescription() {
        return issueDescription;
    }
    
    public void setIssueDescription(String issueDescription) {
        this.issueDescription = issueDescription;
    }
    
    @Override
    public String toString() {
        return "FacePhotoSyncIssueDto{" +
                "personCode='" + personCode + '\'' +
                ", personName='" + personName + '\'' +
                ", departmentCode='" + departmentCode + '\'' +
                ", issueType='" + issueType + '\'' +
                ", issueDescription='" + issueDescription + '\'' +
                '}';
    }
} 