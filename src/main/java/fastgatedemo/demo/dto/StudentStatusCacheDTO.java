package fastgatedemo.demo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * @description 学生状态缓存DTO
 * 用于Redis缓存中存储学生的最新在寝状态
 * <AUTHOR>
 * @date 2025-01-29
 */
public class StudentStatusCacheDTO {

    /**
     * 人员编码
     */
    private String personCode;

    /**
     * 人员姓名
     */
    private String personName;

    /**
     * 最后一次进出状态
     * 1 = 进入 (在寝室)
     * 2 = 离开 (不在寝室)
     */
    private Integer lastInOrOut;

    /**
     * 最后一次通行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastPassTime;

    /**
     * 最后一次通行设备名称
     */
    private String lastDeviceName;

    /**
     * 最后一次通行区域名称
     */
    private String lastAreaName;

    /**
     * 缓存更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 部门编码
     */
    private String departmentCode;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 电话号码
     */
    private String telephone;

    public StudentStatusCacheDTO() {
        this.updateTime = LocalDateTime.now();
    }

    public StudentStatusCacheDTO(String personCode, String personName, Integer lastInOrOut, LocalDateTime lastPassTime) {
        this();
        this.personCode = personCode;
        this.personName = personName;
        this.lastInOrOut = lastInOrOut;
        this.lastPassTime = lastPassTime;
    }

    // ==================== Getter和Setter方法 ====================

    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public Integer getLastInOrOut() {
        return lastInOrOut;
    }

    public void setLastInOrOut(Integer lastInOrOut) {
        this.lastInOrOut = lastInOrOut;
    }

    public LocalDateTime getLastPassTime() {
        return lastPassTime;
    }

    public void setLastPassTime(LocalDateTime lastPassTime) {
        this.lastPassTime = lastPassTime;
    }

    public String getLastDeviceName() {
        return lastDeviceName;
    }

    public void setLastDeviceName(String lastDeviceName) {
        this.lastDeviceName = lastDeviceName;
    }

    public String getLastAreaName() {
        return lastAreaName;
    }

    public void setLastAreaName(String lastAreaName) {
        this.lastAreaName = lastAreaName;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    // ==================== 业务方法 ====================

    /**
     * 判断是否在寝室
     * @return true-在寝室, false-不在寝室
     */
    public boolean isInDormitory() {
        return lastInOrOut != null && lastInOrOut == 1;
    }

    /**
     * 获取在寝状态描述
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (lastInOrOut == null) return "无记录";
        return lastInOrOut == 1 ? "已归寝室" : "未归寝室";
    }

    /**
     * 获取进出状态描述
     * @return 进出状态文字描述
     */
    public String getInOrOutDescription() {
        if (lastInOrOut == null) return "无通行记录";
        return lastInOrOut == 1 ? "进入寝室" : "离开寝室";
    }

    /**
     * 获取Redis缓存Key
     * @return 缓存Key
     */
    public String getCacheKey() {
        return "student:status:" + personCode;
    }

    /**
     * 获取统计分组Key
     * @return 统计分组Key
     */
    public String getStatsGroupKey() {
        if (lastInOrOut == null) return "stats:group:no_record";
        return lastInOrOut == 1 ? "stats:group:in_dormitory" : "stats:group:out_dormitory";
    }

    /**
     * 更新状态信息
     * @param accessRecord 通行记录
     */
    public void updateFromAccessRecord(AccessRecordDTO accessRecord) {
        this.lastInOrOut = accessRecord.getInOrOut();
        this.lastPassTime = accessRecord.getPassTime();
        this.lastDeviceName = accessRecord.getDeviceName();
        this.lastAreaName = accessRecord.getAreaName();
        this.updateTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "StudentStatusCacheDTO{" +
                "personCode='" + personCode + '\'' +
                ", personName='" + personName + '\'' +
                ", lastInOrOut=" + lastInOrOut +
                ", lastPassTime=" + lastPassTime +
                ", lastDeviceName='" + lastDeviceName + '\'' +
                ", lastAreaName='" + lastAreaName + '\'' +
                ", updateTime=" + updateTime +
                '}';
    }
}