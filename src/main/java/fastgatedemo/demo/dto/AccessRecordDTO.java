package fastgatedemo.demo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * @description 通行记录DTO
 * 用于在Redis缓存和各服务间传递通行记录数据
 * <AUTHOR>
 * @date 2025-01-29
 */
public class AccessRecordDTO {

    /**
     * 人员编码
     */
    private String personCode;

    /**
     * 人员姓名
     */
    private String personName;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 地点编码
     */
    private String placeCode;

    /**
     * 地点名称
     */
    private String placeName;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 进出标志
     * 1 = 进入 (进入寝室)
     * 2 = 离开 (离开寝室)
     */
    private Integer inOrOut;

    /**
     * 通行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime passTime;

    /**
     * 记录日期 (格式: YYYY-MM-DD)
     */
    private String recordDate;

    /**
     * 记录时间 (格式: HH:mm:ss)
     */
    private String recordTime;

    /**
     * 匹配置信度
     */
    private Integer matchConfidence;

    /**
     * 设备IP
     */
    private String deviceIp;

    /**
     * 体温
     */
    private Double temperature;

    /**
     * 创建时间（用于缓存管理）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    public AccessRecordDTO() {
        this.createTime = LocalDateTime.now();
    }

    public AccessRecordDTO(String personCode, String personName, Integer inOrOut, LocalDateTime passTime) {
        this();
        this.personCode = personCode;
        this.personName = personName;
        this.inOrOut = inOrOut;
        this.passTime = passTime;
    }

    // ==================== Getter和Setter方法 ====================

    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getPlaceCode() {
        return placeCode;
    }

    public void setPlaceCode(String placeCode) {
        this.placeCode = placeCode;
    }

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getInOrOut() {
        return inOrOut;
    }

    public void setInOrOut(Integer inOrOut) {
        this.inOrOut = inOrOut;
    }

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public String getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(String recordDate) {
        this.recordDate = recordDate;
    }

    public String getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(String recordTime) {
        this.recordTime = recordTime;
    }

    public Integer getMatchConfidence() {
        return matchConfidence;
    }

    public void setMatchConfidence(Integer matchConfidence) {
        this.matchConfidence = matchConfidence;
    }

    public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    // ==================== 业务方法 ====================

    /**
     * 判断是否为进入记录
     * @return true-进入, false-离开
     */
    public boolean isEntering() {
        return inOrOut != null && inOrOut == 1;
    }

    /**
     * 判断是否为离开记录
     * @return true-离开, false-进入
     */
    public boolean isLeaving() {
        return inOrOut != null && inOrOut == 2;
    }

    /**
     * 获取进出状态描述
     * @return 进出状态文字描述
     */
    public String getInOrOutDescription() {
        if (inOrOut == null) return "未知";
        return inOrOut == 1 ? "进入" : "离开";
    }

    /**
     * 获取缓存Key
     * @return Redis缓存Key
     */
    public String getCacheKey() {
        return "student:status:" + personCode;
    }

    @Override
    public String toString() {
        return "AccessRecordDTO{" +
                "personCode='" + personCode + '\'' +
                ", personName='" + personName + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", inOrOut=" + inOrOut +
                ", passTime=" + passTime +
                ", areaName='" + areaName + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}