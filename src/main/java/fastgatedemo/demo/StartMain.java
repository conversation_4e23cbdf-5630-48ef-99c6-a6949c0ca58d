package fastgatedemo.demo;

import fastgatedemo.demo.config.FastGateConf;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * 程序启动主函数
 * 使用dynamic-datasource-spring-boot-starter自动配置数据源
 */
@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties({FastGateConf.class})
public class StartMain {
	public static void main(String[] args) {
		SpringApplication.run(StartMain.class, args);
	}
}
