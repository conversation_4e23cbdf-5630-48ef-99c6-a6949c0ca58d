package fastgatedemo.demo.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fastgatedemo.demo.constant.BaseConstant;
import fastgatedemo.demo.model.DepartPerson;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.model.PersonPictureRecord;
import fastgatedemo.demo.model.UniResult;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.client.CookieStore;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.List;

/**
 * @description: 速通门接口调用http工具类
 */
public class UniHttpUtil {
    /**
     * 日志信息
     */
    private static final Logger logger = LoggerFactory.getLogger(UniHttpUtil.class);

    // 解决中文乱码
    static ContentType contentType = ContentType.create("text/plain", Charset.forName("UTF-8"));

    private static CookieStore cookieStore;

    public static CookieStore getCookieStore() {
        return cookieStore;
    }

    public static void setCookieStore(CookieStore cookieStore) {
        UniHttpUtil.cookieStore = cookieStore;
    }

    /**
     * 获取登录的cookie信息
     *
     * @return
     */
    public static CookieStore getCookieStore(String loginUrl, String user, String passwd) throws IOException {
        JSONObject loginParm = new JSONObject();
        if (null != user && null != passwd) {
            loginParm.put("Id", user);
            loginParm.put("Pwd", passwd);
        } else {
            logger.error("user or password can't be null");
            return null;
        }

        CookieStore cookieStore = new BasicCookieStore();
        HttpContext localContext = new BasicHttpContext();
        localContext.setAttribute(HttpClientContext.COOKIE_STORE, cookieStore);
        //创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();

        //创建httpPost请求对象
        HttpPost httpPost = new HttpPost(loginUrl);
        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, BaseConstant.CONTENT_TYPE_JSON);
        httpPost.setEntity(new StringEntity(loginParm.toString(), ContentType.APPLICATION_JSON));

        //执行请求
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost, localContext);
        HttpEntity entity = httpResponse.getEntity();
        //解析请求结果
        String response = EntityUtils.toString(entity, "UTF-8");
        UniResult uniResult = JSONObject.parseObject(response, UniResult.class);
        //资源关闭
        close(httpClient, httpResponse);
        if (uniResult == null || HttpStatus.SC_OK != uniResult.getErrCode()) {
            logger.error(" get cookieStore error,UniResult: {}", uniResult.toString());
            return null;
        } else {
            setCookieStore(cookieStore);
            logger.debug(" get cookieStore success.");
        }
        return cookieStore;
    }

    /**
     * 添加人员信息
     *
     * @param httpUrl
     * @param personInfo
     * @return
     */
    public static UniResult insertPerson(String httpUrl, PersonInfo personInfo, String picPath) throws IOException {
        UniResult uniResult;
        HttpEntity reqEntity;
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        //设置人员信息
        setPersonInfo(personInfo, builder);
        //设置人员图片信息(如果存在图片信息)
        deleteDirectory(picPath);
        if (!StringUtils.isEmpty(personInfo.getPersonPicture())) {
            File file = new File(picPath + personInfo.getPersonCode() + BaseConstant.SUFFIX_JPG);
            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(file));
            bufferedOutputStream.write(personInfo.getPersonPicture());
            if (bufferedOutputStream != null) {
                bufferedOutputStream.close();
            }
            FileBody fileBody = new FileBody(file, BaseConstant.MIME_TYPE_IMAGE_JPEG, BaseConstant.CHARSET_UTF8);
            builder.addPart("file", fileBody);
        }

        reqEntity = builder.build();
        uniResult = sendHttpPostFormData(httpUrl, reqEntity, BaseConstant.CHARSET_UTF8);
        return uniResult;
    }


    /**
     * 删除人员信息
     *
     * @param httpUrl
     * @param personCodeList
     * @return
     * @throws IOException
     */
    public static UniResult sendHttpDeleteJson(String httpUrl, String personCodeList) throws IOException {
        String msg = null;
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        HttpDeleteWithBody httpDelete = new HttpDeleteWithBody(httpUrl);
        httpDelete.setHeader(BaseConstant.CONTENT_TYPE, BaseConstant.CONTENT_TYPE_JSON);
        if (!StringUtils.isEmpty(personCodeList)) {
            httpDelete.setEntity(new StringEntity(personCodeList, ContentType.APPLICATION_JSON));
        }
        CloseableHttpResponse httpResponse = httpClient.execute(httpDelete);
        HttpEntity httpEntity = httpResponse.getEntity();
        if (httpEntity != null) {
            msg = EntityUtils.toString(httpEntity, BaseConstant.CHARSET_UTF8);
        }
        return JSONObject.parseObject(msg, UniResult.class);
    }

    /**
     * 更新人员信息
     *
     * @param url
     * @param departPerson
     * @return
     */
    public static UniResult updatePerson(String url, DepartPerson departPerson, PersonInfo personInfo) throws IOException {

        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        HttpEntity reqEntity = null;
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        //唯一主键
        builder.addPart("Seqid", new StringBody(departPerson.getSeqid().toString(), contentType));
        //更新人员信息 - 使用DepartPerson中的数据，只从PersonInfo中获取照片数据
        setPersonInfoForUpdate(departPerson, personInfo, builder);

        //人员图片信息更新 - 参考insertPerson的实现
        // 如果有图片数据，则上传图片文件
        String picPath = "temp/person_pics/";
        deleteDirectory(picPath);
        if (personInfo.getPersonPicture() != null && personInfo.getPersonPicture().length > 0) {
            File file = new File(picPath + personInfo.getPersonCode() + BaseConstant.SUFFIX_JPG);
            try (BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(file))) {
                bufferedOutputStream.write(personInfo.getPersonPicture());
            }
            FileBody fileBody = new FileBody(file, BaseConstant.MIME_TYPE_IMAGE_JPEG, BaseConstant.CHARSET_UTF8);
            builder.addPart("file", fileBody);
            logger.info("为人员 {} 更新图片文件: {} ({}字节)", 
                       personInfo.getPersonCode(), file.getAbsolutePath(), personInfo.getPersonPicture().length);
        }

        reqEntity = builder.build();
        httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        HttpPut httpPut = new HttpPut(url);
        httpPut.setEntity(reqEntity);

        httpResponse = httpClient.execute(httpPut);
        HttpEntity httpEntity = httpResponse.getEntity();
        String msg = null;
        if (httpEntity != null) {
            msg = EntityUtils.toString(httpEntity, BaseConstant.CHARSET_UTF8);
        }
        
        //清理临时图片文件
        deleteDirectory(picPath);
        
        return JSONObject.parseObject(msg, UniResult.class);
    }

    /**
     * 发送get请求
     *
     * @param httpUrl
     * @param json
     * @return
     * @throws IOException
     */
    public static UniResult sendHttpGetJson(String httpUrl, String json) throws IOException {
        String response = null;
        String params = null;
        HttpGet httpGet = null;
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        
        try {
            if (json != null) {
                params = URLEncoder.encode(json, "UTF-8");
                httpGet = new HttpGet(httpUrl + params);
            } else {
                httpGet = new HttpGet(httpUrl);
            }
            
            httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
            httpResponse = httpClient.execute(httpGet);
            
            if (httpResponse.getEntity() != null) {
                response = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            }
            
            if (null == response) {
                return null;
            }
            
            return JSONObject.parseObject(response, UniResult.class);
        } catch (Exception e) {
            throw new IOException("HTTP请求失败: " + e.getMessage(), e);
        } finally {
            // 确保资源在finally块中关闭
            close(httpClient, httpResponse);
        }
    }


    /**
     * @param httpUrl
     * @param json
     * @return
     * @throws IOException
     */
    public static UniResult sendHttpPostJson(String httpUrl, String json) throws IOException {
        String response = null;
        HttpContext localContext = new BasicHttpContext();
        localContext.setAttribute(HttpClientContext.COOKIE_STORE, cookieStore);

        // 1. 创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        // 2. 创建HttpPost对象
        HttpPost httpPost = new HttpPost(httpUrl);
        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, BaseConstant.CONTENT_TYPE_JSON);
        // 3. 设置POST请求传递参数
        if (json != null) {
            httpPost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
        }
        // 4. 执行请求并处理响应
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost, localContext);
        HttpEntity entity = httpResponse.getEntity();
        response = EntityUtils.toString(entity);
        //资源关闭
        close(httpClient, httpResponse);
        return JSONObject.parseObject(response, UniResult.class);
    }

    /**
     * @param httpUrl
     * @param reqEntity
     * @param encoding
     * @return
     */
    public static UniResult sendHttpPostFormData(String httpUrl, HttpEntity reqEntity, String encoding) {
        String msg = null;
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        // 设置cookie信息
        httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
        HttpPost httpPost = new HttpPost(httpUrl);
        if (reqEntity != null) {
            httpPost.setEntity(reqEntity);
        }
        try {
            httpResponse = httpClient.execute(httpPost);
            HttpEntity httpEntity = httpResponse.getEntity();
            if (httpEntity != null) {
                msg = EntityUtils.toString(httpEntity, encoding);
            }
        } catch (Exception e) {
            logger.error("{}", e);
            return null;
        } finally {
            close(httpClient, httpResponse);
        }
        return JSONObject.parseObject(msg, UniResult.class);
    }

    /**
     * 资源释放
     *
     * @param httpClient
     * @param httpResponse
     */
    public static void close(CloseableHttpClient httpClient, CloseableHttpResponse httpResponse) {
        // 释放资源
        if (httpResponse != null) {
            try {
                httpResponse.close();
            } catch (IOException e) {
                logger.error("{}", e);
            }
        }
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                logger.error("{}", e);
            }
        }
    }

    /**
     * 设置人员信息,不包括图片信息
     *
     * @param personInfo
     * @param builder
     */
    public static void setPersonInfo(PersonInfo personInfo, MultipartEntityBuilder builder) {

        // 人员姓名
        if (!StringUtils.isEmpty(personInfo.getPersonName())) {
            builder.addPart("Name", new StringBody(personInfo.getPersonName().trim(), contentType));
        }
        // 人员编号/学号
        if (!StringUtils.isEmpty(personInfo.getPersonCode())) {
            builder.addPart("Code", new StringBody(personInfo.getPersonCode().trim(), contentType));
        }
        // 部门编码
        if (StringUtils.isEmpty(personInfo.getDepartmentCode())) {
            builder.addPart("Depart", new StringBody("iccsid", contentType));
        } else {
            builder.addPart("Depart", new StringBody(personInfo.getDepartmentCode().trim(), contentType));
        }
        //联系电话
        if (!StringUtils.isEmpty(personInfo.getTelephone())) {
            builder.addPart("Telephone", new StringBody(personInfo.getTelephone().trim(), contentType));
        }
        //人员类型
        if (StringUtils.isEmpty(personInfo.getPersonType())) {
            builder.addPart("PerType", new StringBody("-1", contentType));
        } else {
            builder.addPart("PerType", new StringBody(personInfo.getPersonType().toString(), contentType));
        }
        //人员性别
        if (StringUtils.isEmpty(personInfo.getGender())) {
            builder.addPart("Sex", new StringBody("1",contentType));
        } else {
            builder.addPart("Sex", new StringBody(personInfo.getGender().toString(),contentType));
        }

        //证件类型 卡号为空，证件类型为身份证
        if (StringUtils.isEmpty(personInfo.getCardnum())) {
            builder.addPart("Cardtype", new StringBody("0", contentType));
        } else {
            builder.addPart("Cardtype", new StringBody("1", contentType));
        }
        //卡号 卡号为空，则使用身份证号
        if (!StringUtils.isEmpty(personInfo.getCardnum())) {
            builder.addPart("Idcard", new StringBody(personInfo.getCardnum(), contentType));
        } else {
            if (!StringUtils.isEmpty(personInfo.getIdcard())) {
                builder.addPart("Idcard", new StringBody(personInfo.getIdcard(), contentType));
            }
        }
        
        //区域编码/宿舍编码
        if (!StringUtils.isEmpty(personInfo.getAreaCode())) {
            builder.addPart("AreaCode", new StringBody(personInfo.getAreaCode(), contentType));
        }
    }

    /**
     * 设置人员信息用于更新场景，除照片外的数据都使用DepartPerson中的信息
     *
     * @param departPerson EGS平台查询到的人员信息
     * @param personInfo 仅用于获取照片数据
     * @param builder HTTP请求构建器
     */
    public static void setPersonInfoForUpdate(DepartPerson departPerson, PersonInfo personInfo, MultipartEntityBuilder builder) {

        // 人员姓名 - 使用DepartPerson中的数据
        if (!StringUtils.isEmpty(departPerson.getName())) {
            builder.addPart("Name", new StringBody(departPerson.getName().trim(), contentType));
        }
        
        // 人员编号/学号 - 使用DepartPerson中的数据
        if (!StringUtils.isEmpty(departPerson.getCode())) {
            builder.addPart("Code", new StringBody(departPerson.getCode().trim(), contentType));
        }
        
        // 部门编码 - 使用DepartPerson中的数据
        if (StringUtils.isEmpty(departPerson.getDepartCode())) {
            builder.addPart("Depart", new StringBody("iccsid", contentType));
        } else {
            builder.addPart("Depart", new StringBody(departPerson.getDepartCode().trim(), contentType));
        }
        
        //联系电话 - 使用DepartPerson中的数据
        if (!StringUtils.isEmpty(departPerson.getTelephone())) {
            builder.addPart("Telephone", new StringBody(departPerson.getTelephone().trim(), contentType));
        }
        
        //人员类型 - 使用DepartPerson中的数据
        if (departPerson.getPerType() == null) {
            builder.addPart("PerType", new StringBody("-1", contentType));
        } else {
            builder.addPart("PerType", new StringBody(departPerson.getPerType().toString(), contentType));
        }
        
        //人员性别 - 使用DepartPerson中的数据
        if (departPerson.getSex() == null) {
            builder.addPart("Sex", new StringBody("1", contentType));
        } else {
            builder.addPart("Sex", new StringBody(departPerson.getSex().toString(), contentType));
        }

        //证件类型 - 使用DepartPerson中的数据
        if (StringUtils.isEmpty(departPerson.getCardtype())) {
            builder.addPart("Cardtype", new StringBody("0", contentType));
        } else {
            builder.addPart("Cardtype", new StringBody(departPerson.getCardtype(), contentType));
        }
        
        //身份证号 - 使用DepartPerson中的数据
        if (!StringUtils.isEmpty(departPerson.getIdcard())) {
            builder.addPart("Idcard", new StringBody(departPerson.getIdcard(), contentType));
        }
        
        //区域编码/宿舍编码 - 使用DepartPerson中的数据
        if (!StringUtils.isEmpty(departPerson.getAreacode())) {
            builder.addPart("AreaCode", new StringBody(departPerson.getAreacode(), contentType));
        }
        
        //图片列表 - 设置为空字符串，避免传递历史图片数据
        builder.addPart("ImageList", new StringBody("", contentType));
        logger.debug("setPersonInfoForUpdate: 已将ImageList设置为空字符串");
    }

    /**
     * 为人员绑定默认权限组
     *
     * @param groupUrl
     * @param personInfo
     */
    public static UniResult bindDefaultGroup(String groupUrl, PersonInfo personInfo) throws IOException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Code", personInfo.getPersonCode());
        jsonObject.put("Name", personInfo.getPersonName());
        jsonObject.put("Type", "6");

        JSONObject group = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        // 绑定默认权限组 0
        jsonArray.add(BaseConstant.DEFAULT_GROUP);

        group.put("PersonBean", jsonObject);
        group.put("DeleteGroupIds", new JSONArray());
        group.put("AddGroupIds", jsonArray);

        UniResult uniResult = UniHttpUtil.sendHttpPostJson(groupUrl, group.toJSONString());
        return uniResult;
    }

    /**
     * 删除本地缓存的图片
     * @param dirPath
     */
    private static void deleteDirectory(String dirPath) {
        File directory = new File(dirPath);
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (StringUtils.isEmpty(files)) {
                return;
            }
            for (File file : files) {
                file.delete();
            }
        } else {
            directory.mkdirs();
        }
    }


    /**
     * 更新人员图片信息
     * @param personInfo
     * @param departPerson
     * @param builder
     * @param data
     */
    /**
     * 检查API响应是否为登录状态失效
     *
     * @param result API响应结果
     * @return 是否为登录状态失效
     */
    public static boolean isLoginExpired(UniResult result) {
        if (result == null) {
            return false;
        }
        // ErrCode=1010表示"登录状态已失效"
        return result.getErrCode() == 1010;
    }

    /**
     * 检查API响应是否为认证相关错误
     *
     * @param result API响应结果
     * @return 是否为认证错误
     */
    public static boolean isAuthenticationError(UniResult result) {
        if (result == null) {
            return false;
        }
        // 1010: 登录状态已失效, 401: 未授权, 403: 禁止访问
        return result.getErrCode() == 1010 || result.getErrCode() == 401 || result.getErrCode() == 403;
    }

    public static void updatePersonPicture(PersonInfo personInfo, DepartPerson departPerson, MultipartEntityBuilder builder, String data) {

        JSONObject jsonData = JSON.parseObject(data);
        JSONArray jsonArray = jsonData.getJSONArray("Pictures");
        List<PersonPictureRecord> pictureRecordList = JSON.parseArray(jsonArray.toString(), PersonPictureRecord.class);

    }
}
