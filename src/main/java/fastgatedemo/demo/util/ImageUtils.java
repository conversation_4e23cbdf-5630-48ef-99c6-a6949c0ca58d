package fastgatedemo.demo.util;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Base64;

/**
 *@description 获取图片信息
 */
public class ImageUtils  {

    /**
     * 根据文件路径获得数据的字节流
     * @param imgFilePath 本地路径
     * @return
     */
    public static byte[] getImageBytesByFilePath(String imgFilePath) throws Exception
    {
        byte[] data = null;
        // 读取图片字节数组
        InputStream in = null;
        try {
            in = new FileInputStream(imgFilePath);
            data = new byte[in.available()];
            in.read(data);
        } finally {
            if(null != in){
                in.close();
            }
        }
        return data;
    }

    /**
     * 根据Base64字符串获得数据的字节流
     * @param base64Data Base64格式的图片数据
     * @return byte[]数组
     */
    public static byte[] getImageBytesByBase64String(String base64Data) throws Exception {
        if (base64Data == null || base64Data.trim().isEmpty()) {
            throw new IllegalArgumentException("Base64数据不能为空");
        }
        
        try {
            // 移除可能的Base64前缀 (如: data:image/jpeg;base64,)
            String cleanBase64 = base64Data;
            if (base64Data.contains(",")) {
                cleanBase64 = base64Data.substring(base64Data.indexOf(",") + 1);
            }
            
            // 解码Base64字符串为字节数组
            return Base64.getDecoder().decode(cleanBase64);
        } catch (IllegalArgumentException e) {
            throw new Exception("Base64数据格式错误: " + e.getMessage(), e);
        }
    }
}
