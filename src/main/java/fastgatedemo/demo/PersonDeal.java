package fastgatedemo.demo;

import fastgatedemo.demo.config.FastGateConf;
import fastgatedemo.demo.constant.BaseConstant;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.service.PersonService;
import fastgatedemo.demo.util.ImageUtils;
import fastgatedemo.demo.util.UniHttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
// import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @description 人员信息处理 (已禁用自动启动，改为Web界面手动操作)
 */
// @Component
// @Order(1)
public class PersonDeal /* implements ApplicationRunner */ {

    @Autowired
    FastGateConf fastGateConf;

    @Autowired
    PersonService personService;

    // @Override
    public void run(ApplicationArguments args) throws Exception {

        String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
        String user = fastGateConf.getUser();
        String passwd = fastGateConf.getPasswd();
        //程序启动后立即加载cookieStore信息
        UniHttpUtil.getCookieStore(loginUrl, user, passwd);

        // 初始化人员信息
        PersonInfo personInfo = new PersonInfo();
        personInfo.setCardnum("A83909A80");
        personInfo.setGender(1);
        personInfo.setPersonCode("YKT0000031");
        personInfo.setPersonName("赵七1");
        personInfo.setCardtype("1");
        personInfo.setDepartmentCode("iccsid");
        personInfo.setPersonType("6");
        //人员图片信息
        // TODO: 注释掉硬编码的图片路径，避免FileNotFoundException
        // 如需使用图片功能，请确保E:\boot\timg.jpg文件存在，或使用classpath资源
        personInfo.setPersonPicture(ImageUtils.getImageBytesByFilePath("E:\\jianyou\\DataTrans\\doc\\lisi.png"));

        //新增人员
        personService.addPerson(personInfo);




        //更新人员  根据personcode更新
        // 更新人员中有根据personcode查询对应的人员信息
        //personService.updatePerson(personInfo);

        //删除人员 根据personcode删除
        //personService.deletePerson(personInfo);
    }
}
