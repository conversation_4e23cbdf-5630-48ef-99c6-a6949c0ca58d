package fastgatedemo.demo.model;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Date: 2018/8/2
 */
@Entity
@Table(name = "person_info")
public class PersonInfo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.seqid
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Id
    @Column(name = "seqid")
    private String seqid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.person_name
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @NotBlank(message = "人员姓名不能为空")
    @Column(name = "person_name")
    private String personName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.person_code
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @NotBlank(message = "人员编码不能为空")
    @Column(name = "person_code", unique = true)
    private String personCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.department_code
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "department_code")
    private String departmentCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.gender
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "gender")
    private Integer gender;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.telephone
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "telephone")
    private String telephone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.idcard
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "idcard")
    private String idcard;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.status
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "status")
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.update_time
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "update_time")
    private String updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.sync_flag
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "sync_flag")
    private Integer syncFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.person_type
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "person_type")
    private String personType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.cardnum
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "cardnum")
    private String cardnum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.cardtype
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "cardtype")
    private String cardtype;

    /**
     * 区域编码/宿舍编码 - 用于EGS平台AreaCode字段
     * 从person_dormitory_relation表查询获得
     */
    @Transient
    private String areaCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_info.person_picture
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    @Column(name = "person_picture", columnDefinition = "BYTEA")
    private byte[] personPicture;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.seqid
     *
     * @return the value of tbl_person_info.seqid
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getSeqid() {
        return seqid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.seqid
     *
     * @param seqid the value for tbl_person_info.seqid
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setSeqid(String seqid) {
        this.seqid = seqid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.person_name
     *
     * @return the value of tbl_person_info.person_name
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getPersonName() {
        return personName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.person_name
     *
     * @param personName the value for tbl_person_info.person_name
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setPersonName(String personName) {
        this.personName = personName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.person_code
     *
     * @return the value of tbl_person_info.person_code
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getPersonCode() {
        return personCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.person_code
     *
     * @param personCode the value for tbl_person_info.person_code
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.department_code
     *
     * @return the value of tbl_person_info.department_code
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getDepartmentCode() {
        return departmentCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.department_code
     *
     * @param departmentCode the value for tbl_person_info.department_code
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.gender
     *
     * @return the value of tbl_person_info.gender
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public Integer getGender() {
        return gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.gender
     *
     * @param gender the value for tbl_person_info.gender
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setGender(Integer gender) {
        this.gender = gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.telephone
     *
     * @return the value of tbl_person_info.telephone
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getTelephone() {
        return telephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.telephone
     *
     * @param telephone the value for tbl_person_info.telephone
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.idcard
     *
     * @return the value of tbl_person_info.idcard
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getIdcard() {
        return idcard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.idcard
     *
     * @param idcard the value for tbl_person_info.idcard
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.status
     *
     * @return the value of tbl_person_info.status
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.status
     *
     * @param status the value for tbl_person_info.status
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.update_time
     *
     * @return the value of tbl_person_info.update_time
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.update_time
     *
     * @param updateTime the value for tbl_person_info.update_time
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.sync_flag
     *
     * @return the value of tbl_person_info.sync_flag
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public Integer getSyncFlag() {
        return syncFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.sync_flag
     *
     * @param syncFlag the value for tbl_person_info.sync_flag
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setSyncFlag(Integer syncFlag) {
        this.syncFlag = syncFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.person_type
     *
     * @return the value of tbl_person_info.person_type
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getPersonType() {
        return personType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.person_type
     *
     * @param personType the value for tbl_person_info.person_type
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setPersonType(String personType) {
        this.personType = personType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.cardnum
     *
     * @return the value of tbl_person_info.cardnum
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getCardnum() {
        return cardnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.cardnum
     *
     * @param cardnum the value for tbl_person_info.cardnum
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setCardnum(String cardnum) {
        this.cardnum = cardnum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.cardtype
     *
     * @return the value of tbl_person_info.cardtype
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public String getCardtype() {
        return cardtype;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.cardtype
     *
     * @param cardtype the value for tbl_person_info.cardtype
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setCardtype(String cardtype) {
        this.cardtype = cardtype;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_info.person_picture
     *
     * @return the value of tbl_person_info.person_picture
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public byte[] getPersonPicture() {
        return personPicture;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_info.person_picture
     *
     * @param personPicture the value for tbl_person_info.person_picture
     *
     * @mbggenerated Thu Aug 23 19:54:09 CST 2018
     */
    public void setPersonPicture(byte[] personPicture) {
        this.personPicture = personPicture;
    }

    /**
     * 获取区域编码/宿舍编码
     * @return 区域编码
     */
    public String getAreaCode() {
        return areaCode;
    }

    /**
     * 设置区域编码/宿舍编码
     * @param areaCode 区域编码
     */
    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    @Override
    public String toString() {
        return "PersonInfo{" +
                "seqid='" + seqid + '\'' +
                ", personName='" + personName + '\'' +
                ", personCode='" + personCode + '\'' +
                ", departmentCode='" + departmentCode + '\'' +
                ", gender=" + gender +
                ", telephone='" + telephone + '\'' +
                ", idcard='" + idcard + '\'' +
                ", status=" + status +
                ", updateTime='" + updateTime + '\'' +
                ", syncFlag=" + syncFlag +
                ", personType='" + personType + '\'' +
                ", cardnum='" + cardnum + '\'' +
                ", cardtype='" + cardtype + '\'' +
                '}';
    }
}