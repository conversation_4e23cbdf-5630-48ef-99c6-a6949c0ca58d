package fastgatedemo.demo.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @description 人员宿舍关联实体类
 * 映射数据库表：person_dormitory_relation
 */
@Entity
@Table(name = "person_dormitory_relation")
public class PersonDormitoryRelation {

    @Id
    @Column(name = "id", length = 50)
    private String id;

    @Column(name = "person_code", nullable = false, length = 50)
    private String personCode;

    @Column(name = "dormitory_code", nullable = false, length = 50)
    private String dormitoryCode;

    @Column(name = "bed_no", length = 20)
    private String bedNo;

    @Column(name = "assign_date", length = 20)
    private String assignDate;

    @Column(name = "status")
    private Integer status;

    @Column(name = "sync_flag")
    private Integer syncFlag;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    // 构造函数
    public PersonDormitoryRelation() {
        this.status = 1;
        this.syncFlag = 0;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    public String getDormitoryCode() {
        return dormitoryCode;
    }

    public void setDormitoryCode(String dormitoryCode) {
        this.dormitoryCode = dormitoryCode;
    }

    public String getBedNo() {
        return bedNo;
    }

    public void setBedNo(String bedNo) {
        this.bedNo = bedNo;
    }

    public String getAssignDate() {
        return assignDate;
    }

    public void setAssignDate(String assignDate) {
        this.assignDate = assignDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSyncFlag() {
        return syncFlag;
    }

    public void setSyncFlag(Integer syncFlag) {
        this.syncFlag = syncFlag;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @PreUpdate
    public void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "PersonDormitoryRelation{" +
                "id=" + id +
                ", personCode='" + personCode + '\'' +
                ", dormitoryCode='" + dormitoryCode + '\'' +
                ", bedNo='" + bedNo + '\'' +
                ", assignDate='" + assignDate + '\'' +
                ", status=" + status +
                ", syncFlag=" + syncFlag +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
