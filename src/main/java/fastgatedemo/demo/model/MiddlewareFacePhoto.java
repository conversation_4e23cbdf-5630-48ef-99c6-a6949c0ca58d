package fastgatedemo.demo.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @description 中间库人脸照片实体
 * 映射数据库表：face_photo
 */
@Entity
@Table(name = "face_photo")
public class MiddlewareFacePhoto {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    // 必填字段
    @Column(name = "per_code", nullable = false, length = 50)
    private String perCode;

    @Column(name = "acc_num", nullable = false, length = 50)
    private String accNum;

    @Column(name = "photo_data", nullable = false, columnDefinition = "TEXT")
    private String photoData;

    @Column(name = "photo_path", nullable = false, length = 255)
    private String photoPath;

    @Column(name = "face_status", nullable = false)
    private Integer faceStatus;

    @Column(name = "application_status", nullable = false)
    private Integer applicationStatus;

    // 可选字段
    @Column(name = "photo_fix_id", length = 50)
    private String photoFixId;

    @Column(name = "feature_path", length = 255)
    private String featurePath;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @Column(name = "create_by", length = 255)
    private String createBy;

    @Column(name = "update_by", length = 255)
    private String updateBy;

    @Column(name = "sync_flag")
    private Integer syncFlag;

    // 构造函数
    public MiddlewareFacePhoto() {}

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPerCode() {
        return perCode;
    }

    public void setPerCode(String perCode) {
        this.perCode = perCode;
    }

    public String getAccNum() {
        return accNum;
    }

    public void setAccNum(String accNum) {
        this.accNum = accNum;
    }

    public String getPhotoData() {
        return photoData;
    }

    public void setPhotoData(String photoData) {
        this.photoData = photoData;
    }

    public String getPhotoPath() {
        return photoPath;
    }

    public void setPhotoPath(String photoPath) {
        this.photoPath = photoPath;
    }

    public Integer getFaceStatus() {
        return faceStatus;
    }

    public void setFaceStatus(Integer faceStatus) {
        this.faceStatus = faceStatus;
    }

    public Integer getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(Integer applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public String getPhotoFixId() {
        return photoFixId;
    }

    public void setPhotoFixId(String photoFixId) {
        this.photoFixId = photoFixId;
    }

    public String getFeaturePath() {
        return featurePath;
    }

    public void setFeaturePath(String featurePath) {
        this.featurePath = featurePath;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getSyncFlag() {
        return syncFlag;
    }

    public void setSyncFlag(Integer syncFlag) {
        this.syncFlag = syncFlag;
    }

    @Override
    public String toString() {
        return "MiddlewareFacePhoto{" +
                "id=" + id +
                ", perCode='" + perCode + '\'' +
                ", accNum='" + accNum + '\'' +
                ", photoPath='" + photoPath + '\'' +
                ", faceStatus=" + faceStatus +
                ", applicationStatus=" + applicationStatus +
                ", photoFixId='" + photoFixId + '\'' +
                ", featurePath='" + featurePath + '\'' +
                '}';
    }
} 