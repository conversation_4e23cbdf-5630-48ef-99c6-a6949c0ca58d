package fastgatedemo.demo.model;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * Created by lW4720 on 2017/11/23.
 *
 * 黑名单,员工表,部门关联bean
 */
public class DepartPerson {

    //是否为用户
    @JSONField(name = "IsUser")
    private boolean isUser;
    //部门名字
    @JSONField(name ="Department")
    private String department;
    //部门编码
    @JSONField(name ="DepartCode")
    private String departCode;
    //提取状态
    @JSONField(name ="Feature")
    private Integer feature;
    //头像总数
    @JSONField(name ="PicNum")
    private Integer fnum;

    //待预处理
    @JSONField(name ="PicMinusOne")
    private Integer fminusone;
    //未提取
    @JSONField(name ="PicZero")
    private Integer fzero;
    //提取成功
    @JSONField(name ="PicOne")
    private Integer fone;
    //提取失败
    @JSONField(name ="PicTwo")
    private Integer ftwo;
    //头像集合
    @JSONField(name ="ImageList")
    private List<String> imageList;
    //区域名字
    @JSONField(name ="AreaName")
    private String areaname;
    //区域编码
    @JSONField(name ="AreaCode")
    private String areacode;
    /**
     * 头像bean
     */
    @JSONField(name ="Pictures")
    private List<PersonPictureRecord> pictures;


    @JSONField(name ="Seqid")
    private Integer seqid;

    @JSONField(name ="Code")
    private String code;

    @JSONField(name ="Name")
    private String name;

    @JSONField(name ="IdCard")
    private String idcard;

    @JSONField(name ="Email")
    private String email;

    @JSONField(name ="Sex")
    private Integer sex;

    @JSONField(name ="Status")
    private Integer status;

    @JSONField(name ="Spell")
    private String spell;


    @JSONField(name ="CardType")
    private String cardtype;

    @JSONField(name ="Telephone")
    private String telephone;
    @JSONField(name ="Address")
    private String address;
    @JSONField(name ="UpdateTime")
    private Integer updatetime;

    @JSONField(name ="PerCardStatus")
    private Integer perCardStatus;

    @JSONField(name ="PerType")
    private Integer perType;

    @JSONField(name ="Remark")
    private String remark;

    public boolean isUser() {
        return isUser;
    }

    public void setUser(boolean user) {
        isUser = user;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDepartCode() {
        return departCode;
    }

    public void setDepartCode(String departCode) {
        this.departCode = departCode;
    }

    public Integer getFeature() {
        return feature;
    }

    public void setFeature(Integer feature) {
        this.feature = feature;
    }

    public Integer getFnum() {
        return fnum;
    }

    public void setFnum(Integer fnum) {
        this.fnum = fnum;
    }

    public Integer getFminusone() {
        return fminusone;
    }

    public void setFminusone(Integer fminusone) {
        this.fminusone = fminusone;
    }

    public Integer getFzero() {
        return fzero;
    }

    public void setFzero(Integer fzero) {
        this.fzero = fzero;
    }

    public Integer getFone() {
        return fone;
    }

    public void setFone(Integer fone) {
        this.fone = fone;
    }

    public Integer getFtwo() {
        return ftwo;
    }

    public void setFtwo(Integer ftwo) {
        this.ftwo = ftwo;
    }

    public List<String> getImageList() {
        return imageList;
    }

    public void setImageList(List<String> imageList) {
        this.imageList = imageList;
    }

    public String getAreaname() {
        return areaname;
    }

    public void setAreaname(String areaname) {
        this.areaname = areaname;
    }

    public String getAreacode() {
        return areacode;
    }

    public void setAreacode(String areacode) {
        this.areacode = areacode;
    }

    public List<PersonPictureRecord> getPictures() {
        return pictures;
    }

    public void setPictures(List<PersonPictureRecord> pictures) {
        this.pictures = pictures;
    }

    public Integer getSeqid() {
        return seqid;
    }

    public void setSeqid(Integer seqid) {
        this.seqid = seqid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSpell() {
        return spell;
    }

    public void setSpell(String spell) {
        this.spell = spell;
    }

    public String getCardtype() {
        return cardtype;
    }

    public void setCardtype(String cardtype) {
        this.cardtype = cardtype;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Integer updatetime) {
        this.updatetime = updatetime;
    }

    public Integer getPerCardStatus() {
        return perCardStatus;
    }

    public void setPerCardStatus(Integer perCardStatus) {
        this.perCardStatus = perCardStatus;
    }

    public Integer getPerType() {
        return perType;
    }

    public void setPerType(Integer perType) {
        this.perType = perType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "DepartPerson{" +
                "isUser=" + isUser +
                ", department='" + department + '\'' +
                ", departCode='" + departCode + '\'' +
                ", feature=" + feature +
                ", fnum=" + fnum +
                ", fminusone=" + fminusone +
                ", fzero=" + fzero +
                ", fone=" + fone +
                ", ftwo=" + ftwo +
                ", imageList=" + imageList +
                ", areaname='" + areaname + '\'' +
                ", areacode='" + areacode + '\'' +
                ", pictures=" + pictures +
                ", seqid=" + seqid +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", idcard='" + idcard + '\'' +
                ", email='" + email + '\'' +
                ", sex=" + sex +
                ", status=" + status +
                ", spell='" + spell + '\'' +
                ", cardtype='" + cardtype + '\'' +
                ", telephone='" + telephone + '\'' +
                ", address='" + address + '\'' +
                ", updatetime=" + updatetime +
                ", perCardStatus=" + perCardStatus +
                ", perType=" + perType +
                ", remark='" + remark + '\'' +
                '}';
    }
}
