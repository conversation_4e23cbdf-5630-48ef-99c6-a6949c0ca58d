package fastgatedemo.demo.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @description 中间库人员信息实体
 * 映射数据库表：person_info
 */
@Entity
@Table(name = "middleware_person_info")
public class MiddlewarePersonInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    // 必填字段
    @Column(name = "per_code", nullable = false, length = 50)
    private String perCode;

    @Column(name = "acc_num", nullable = false, length = 50)
    private String accNum;

    @Column(name = "acc_name", nullable = false, length = 100)
    private String accName;

    @Column(name = "gender", nullable = false)
    private Integer gender;

    @Column(name = "status", nullable = false)
    private Integer status;

    @Column(name = "per_type", nullable = false)
    private Integer perType;

    // 可选字段
    @Column(name = "id_card", length = 50)
    private String idCard;

    @Column(name = "telephone", length = 255)
    private String telephone;

    @Column(name = "email", length = 255)
    private String email;

    @Column(name = "acc_type")
    private Integer accType;

    @Column(name = "department_code", length = 255)
    private String departmentCode;

    @Column(name = "department_name", length = 255)
    private String departmentName;

    @Column(name = "supervisor_job_no", length = 50)
    private String supervisorJobNo;

    @Column(name = "birthday", length = 20)
    private String birthday;

    @Column(name = "address", length = 255)
    private String address;

    @Column(name = "nation", length = 50)
    private String nation;

    @Column(name = "ic_card", length = 50)
    private String icCard;

    @Column(name = "qrcode", length = 50)
    private String qrcode;

    @Column(name = "bluetooth_card", length = 50)
    private String bluetoothCard;

    @Column(name = "register_time")
    private LocalDateTime registerTime;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Column(name = "remark", length = 255)
    private String remark;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @Column(name = "create_by", length = 255)
    private String createBy;

    @Column(name = "update_by", length = 255)
    private String updateBy;

    // 构造函数
    public MiddlewarePersonInfo() {}

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPerCode() {
        return perCode;
    }

    public void setPerCode(String perCode) {
        this.perCode = perCode;
    }

    public String getAccNum() {
        return accNum;
    }

    public void setAccNum(String accNum) {
        this.accNum = accNum;
    }

    public String getAccName() {
        return accName;
    }

    public void setAccName(String accName) {
        this.accName = accName;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPerType() {
        return perType;
    }

    public void setPerType(Integer perType) {
        this.perType = perType;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getAccType() {
        return accType;
    }

    public void setAccType(Integer accType) {
        this.accType = accType;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getSupervisorJobNo() {
        return supervisorJobNo;
    }

    public void setSupervisorJobNo(String supervisorJobNo) {
        this.supervisorJobNo = supervisorJobNo;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getIcCard() {
        return icCard;
    }

    public void setIcCard(String icCard) {
        this.icCard = icCard;
    }

    public String getQrcode() {
        return qrcode;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode;
    }

    public String getBluetoothCard() {
        return bluetoothCard;
    }

    public void setBluetoothCard(String bluetoothCard) {
        this.bluetoothCard = bluetoothCard;
    }

    public LocalDateTime getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(LocalDateTime registerTime) {
        this.registerTime = registerTime;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public String toString() {
        return "MiddlewarePersonInfo{" +
                "id=" + id +
                ", perCode='" + perCode + '\'' +
                ", accNum='" + accNum + '\'' +
                ", accName='" + accName + '\'' +
                ", gender=" + gender +
                ", status=" + status +
                ", perType=" + perType +
                ", idCard='" + idCard + '\'' +
                ", telephone='" + telephone + '\'' +
                ", email='" + email + '\'' +
                ", departmentCode='" + departmentCode + '\'' +
                ", departmentName='" + departmentName + '\'' +
                '}';
    }
} 