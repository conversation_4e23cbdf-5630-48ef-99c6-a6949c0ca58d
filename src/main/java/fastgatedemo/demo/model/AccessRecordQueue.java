package fastgatedemo.demo.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @description 异步写入队列表
 * 主数据源表，用于缓冲待写入FastGate数据库的通行记录
 * 确保在高并发场景下数据不丢失
 * <AUTHOR>
 * @date 2025-01-29
 */
@Entity
@Table(name = "access_record_queue")
public class AccessRecordQueue {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 人员编码
     */
    @Column(name = "person_code", length = 50)
    private String personCode;

    /**
     * 人员姓名
     */
    @Column(name = "person_name", length = 100)
    private String personName;

    /**
     * 设备编码
     */
    @Column(name = "device_code", length = 50)
    private String deviceCode;

    /**
     * 设备名称
     */
    @Column(name = "device_name", length = 100)
    private String deviceName;

    /**
     * 地点编码
     */
    @Column(name = "place_code", length = 50)
    private String placeCode;

    /**
     * 地点名称
     */
    @Column(name = "place_name", length = 100)
    private String placeName;

    /**
     * 区域编码
     */
    @Column(name = "area_code", length = 50)
    private String areaCode;

    /**
     * 区域名称
     */
    @Column(name = "area_name", length = 100)
    private String areaName;

    /**
     * 进出标志
     * 1 = 进入 (进入寝室)
     * 2 = 离开 (离开寝室)
     */
    @Column(name = "inorout")
    private Integer inOrOut;

    /**
     * 通行时间
     */
    @Column(name = "pass_time")
    private LocalDateTime passTime;

    /**
     * 记录日期 (格式: YYYY-MM-DD)
     */
    @Column(name = "record_date", length = 20)
    private String recordDate;

    /**
     * 记录时间 (格式: HH:mm:ss)
     */
    @Column(name = "record_time", length = 20)
    private String recordTime;

    /**
     * 匹配置信度
     */
    @Column(name = "match_confidence")
    private Integer matchConfidence;

    /**
     * 设备IP
     */
    @Column(name = "device_ip", length = 50)
    private String deviceIp;

    /**
     * 体温
     */
    @Column(name = "temperature")
    private Double temperature;

    /**
     * 处理状态
     * 0 = 待处理
     * 1 = 已处理
     * 2 = 处理失败
     */
    @Column(name = "process_status")
    private Integer processStatus = 0;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 处理时间
     */
    @Column(name = "process_time")
    private LocalDateTime processTime;

    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    private Integer retryCount = 0;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    public AccessRecordQueue() {
        this.createTime = LocalDateTime.now();
        this.processStatus = 0;
        this.retryCount = 0;
    }

    // ==================== Getter和Setter方法 ====================

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getPlaceCode() {
        return placeCode;
    }

    public void setPlaceCode(String placeCode) {
        this.placeCode = placeCode;
    }

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getInOrOut() {
        return inOrOut;
    }

    public void setInOrOut(Integer inOrOut) {
        this.inOrOut = inOrOut;
    }

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public String getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(String recordDate) {
        this.recordDate = recordDate;
    }

    public String getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(String recordTime) {
        this.recordTime = recordTime;
    }

    public Integer getMatchConfidence() {
        return matchConfidence;
    }

    public void setMatchConfidence(Integer matchConfidence) {
        this.matchConfidence = matchConfidence;
    }

    public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getProcessTime() {
        return processTime;
    }

    public void setProcessTime(LocalDateTime processTime) {
        this.processTime = processTime;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    // ==================== 业务方法 ====================

    /**
     * 标记为处理成功
     */
    public void markAsProcessed() {
        this.processStatus = 1;
        this.processTime = LocalDateTime.now();
    }

    /**
     * 标记为处理失败
     * @param errorMessage 错误信息
     */
    public void markAsFailed(String errorMessage) {
        this.processStatus = 2;
        this.processTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
        this.retryCount++;
    }

    /**
     * 重置为待处理状态（用于重试）
     */
    public void resetForRetry() {
        this.processStatus = 0;
        this.processTime = null;
        this.errorMessage = null;
    }

    /**
     * 判断是否可以重试
     * @param maxRetries 最大重试次数
     * @return 是否可以重试
     */
    public boolean canRetry(int maxRetries) {
        return this.retryCount < maxRetries;
    }

    @Override
    public String toString() {
        return "AccessRecordQueue{" +
                "id=" + id +
                ", personCode='" + personCode + '\'' +
                ", personName='" + personName + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", inOrOut=" + inOrOut +
                ", passTime=" + passTime +
                ", processStatus=" + processStatus +
                ", retryCount=" + retryCount +
                ", createTime=" + createTime +
                '}';
    }
}