package fastgatedemo.demo.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @description 宿舍信息数据模型类（用于EGS平台API调用）
 */
public class DormitoryInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 宿舍名称
     */
    private String name;

    /**
     * 宿舍编码（唯一标识）
     */
    private String code;

    /**
     * 楼层数
     */
    private String floor;

    /**
     * 房间数
     */
    private String amount;

    /**
     * 父级编码（固定值：area）
     */
    private String parentcode;

    /**
     * 资源类型（固定值：3）
     */
    private Integer restype;

    /**
     * 标签（固定值：1）
     */
    private Integer label;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 默认构造函数
     */
    public DormitoryInfo() {
        this.parentcode = "area";
        this.restype = 3;
        this.label = 1;
        this.updateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 带参构造函数
     */
    public DormitoryInfo(String name, String code, String floor, String amount) {
        this();
        this.name = name;
        this.code = code;
        this.floor = floor;
        this.amount = amount;
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getParentcode() {
        return parentcode;
    }

    public void setParentcode(String parentcode) {
        this.parentcode = parentcode;
    }

    public Integer getRestype() {
        return restype;
    }

    public void setRestype(Integer restype) {
        this.restype = restype;
    }

    public Integer getLabel() {
        return label;
    }

    public void setLabel(Integer label) {
        this.label = label;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "DormitoryInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", floor='" + floor + '\'' +
                ", amount='" + amount + '\'' +
                ", parentcode='" + parentcode + '\'' +
                ", restype=" + restype +
                ", label=" + label +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }
} 