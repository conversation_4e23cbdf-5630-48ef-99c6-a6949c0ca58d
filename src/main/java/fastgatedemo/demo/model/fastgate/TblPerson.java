package fastgatedemo.demo.model.fastgate;

import javax.persistence.*;

/**
 * @description FastGate数据源人员实体类
 * 对应fastgate数据库中的tbl_person表
 * <AUTHOR>
 * @date 2025-01-25
 */
@Entity
@Table(name = "tbl_person")
public class TblPerson {
    
    /**
     * 序号 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seqid")
    private Long seqid;
    
    /**
     * 人员编号 -唯一约束
     */
    @Column(name = "code")
    private String code;
    
    /**
     * 人员姓名
     */
    @Column(name = "name")
    private String name;
    
    /**
     * 身份证号
     */
    @Column(name = "idcard")
    private String idcard;
    
    /**
     * 邮箱
     */
    @Column(name = "email")
    private String email;
    
    /**
     * 性别  1 男，2 女
     */
    @Column(name = "sex")
    private Integer sex;
    
    /**
     * 人员状态 1 -- 未删除，0 -- 已删除
     */
    @Column(name = "status")
    private Integer status;
    
    /**
     * 拼音
     */
    @Column(name = "spell")
    private String spell;
    
    /**
     * 证件类型
     */
    @Column(name = "cardtype")
    private String cardtype;
    
    /**
     * 手机号
     */
    @Column(name = "telephone")
    private String telephone;
    
    /**
     * 地址
     */
    @Column(name = "address")
    private String address;
    
    /**
     * 更新时间戳
     */
    @Column(name = "updatetime")
    private Integer updatetime;
    
    /**
     * 登记时间
     */
    @Column(name = "register_time")
    private java.sql.Timestamp registerTime;
    
    /**
     * 有效开始时间
     */
    @Column(name = "start_time")
    private java.sql.Timestamp startTime;
    
    /**
     * 有效结束时间
     */
    @Column(name = "end_time")
    private java.sql.Timestamp endTime;
    
    /**
     * 数据来源
     */
    @Column(name = "data_source")
    private String dataSource;
    
    /**
     * 人员类型
     */
    @Column(name = "per_type")
    private Integer perType;
    
    /**
     * IC卡号
     */
    @Column(name = "ic_card")
    private String icCard;
    
    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
    
    // ==================== 构造函数 ====================
    
    public TblPerson() {
    }
    
    public TblPerson(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    // ==================== Getter和Setter方法 ====================
    
    public Long getSeqid() {
        return seqid;
    }
    
    public void setSeqid(Long seqid) {
        this.seqid = seqid;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getIdcard() {
        return idcard;
    }
    
    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public Integer getSex() {
        return sex;
    }
    
    public void setSex(Integer sex) {
        this.sex = sex;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getPerType() {
        return perType;
    }
    
    public void setPerType(Integer perType) {
        this.perType = perType;
    }
    
    public String getTelephone() {
        return telephone;
    }
    
    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
    
    public String getSpell() {
        return spell;
    }
    
    public void setSpell(String spell) {
        this.spell = spell;
    }
    
    public String getCardtype() {
        return cardtype;
    }
    
    public void setCardtype(String cardtype) {
        this.cardtype = cardtype;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public Integer getUpdatetime() {
        return updatetime;
    }
    
    public void setUpdatetime(Integer updatetime) {
        this.updatetime = updatetime;
    }
    
    public java.sql.Timestamp getRegisterTime() {
        return registerTime;
    }
    
    public void setRegisterTime(java.sql.Timestamp registerTime) {
        this.registerTime = registerTime;
    }
    
    public java.sql.Timestamp getStartTime() {
        return startTime;
    }
    
    public void setStartTime(java.sql.Timestamp startTime) {
        this.startTime = startTime;
    }
    
    public java.sql.Timestamp getEndTime() {
        return endTime;
    }
    
    public void setEndTime(java.sql.Timestamp endTime) {
        this.endTime = endTime;
    }
    
    public String getDataSource() {
        return dataSource;
    }
    
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }
    
    public String getIcCard() {
        return icCard;
    }
    
    public void setIcCard(String icCard) {
        this.icCard = icCard;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    @Override
    public String toString() {
        return "TblPerson{" +
                "seqid=" + seqid +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", status=" + status +
                ", perType=" + perType +
                '}';
    }
}
