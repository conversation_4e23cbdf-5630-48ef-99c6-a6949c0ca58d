package fastgatedemo.demo.model.fastgate;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;

/**
 * @description FastGate数据源人员信息实体类
 * 对应fastgate数据库中的person_info表
 * <AUTHOR>
 * @date 2025-01-25
 */
@Entity
@Table(name = "person_info")
public class FastGatePersonInfo {
    
    /**
     * 主键ID
     */
    @Id
    @Column(name = "seqid")
    private String seqid;

    /**
     * 人员姓名
     */
    @NotBlank(message = "人员姓名不能为空")
    @Column(name = "person_name")
    private String personName;

    /**
     * 人员编码
     */
    @NotBlank(message = "人员编码不能为空")
    @Column(name = "person_code", unique = true)
    private String personCode;

    /**
     * 部门编码
     */
    @Column(name = "department_code")
    private String departmentCode;

    /**
     * 部门名称
     */
    @Column(name = "department_name")
    private String departmentName;

    /**
     * 人员类型 (1-学生, 2-教师, 3-其他)
     */
    @Column(name = "person_type")
    private Integer personType;

    /**
     * 状态 (1-正常, 0-禁用)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 同步标志 (0-未同步, 1-已同步)
     */
    @Column(name = "sync_flag")
    private Integer syncFlag;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private String updateTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    // ==================== 构造函数 ====================
    
    public FastGatePersonInfo() {
    }

    public FastGatePersonInfo(String seqid, String personName, String personCode) {
        this.seqid = seqid;
        this.personName = personName;
        this.personCode = personCode;
    }

    // ==================== Getter和Setter方法 ====================
    
    public String getSeqid() {
        return seqid;
    }

    public void setSeqid(String seqid) {
        this.seqid = seqid;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getPersonType() {
        return personType;
    }

    public void setPersonType(Integer personType) {
        this.personType = personType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSyncFlag() {
        return syncFlag;
    }

    public void setSyncFlag(Integer syncFlag) {
        this.syncFlag = syncFlag;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // ==================== toString方法 ====================
    
    @Override
    public String toString() {
        return "FastGatePersonInfo{" +
                "seqid='" + seqid + '\'' +
                ", personName='" + personName + '\'' +
                ", personCode='" + personCode + '\'' +
                ", departmentCode='" + departmentCode + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", personType=" + personType +
                ", status=" + status +
                ", syncFlag=" + syncFlag +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
