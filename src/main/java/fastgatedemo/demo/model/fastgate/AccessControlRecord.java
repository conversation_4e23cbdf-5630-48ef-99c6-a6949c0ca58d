package fastgatedemo.demo.model.fastgate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @description FastGate数据源通行记录实体类
 * 对应fastgate数据库中的tbl_access_control_record表
 * 用于查询人员进出记录，判断寝室归宿状态
 * <AUTHOR>
 * @date 2025-01-25
 */
@Entity
@Table(name = "tbl_access_control_record")
public class AccessControlRecord {
    
    /**
     * 记录ID - 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "record_id")
    private Long recordId;

    /**
     * 人员编码
     */
    @Column(name = "person_code")
    private String personCode;

    /**
     * 人员姓名
     */
    @Column(name = "person_name")
    private String personName;

    /**
     * 设备编码
     */
    @Column(name = "device_code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @Column(name = "device_name")
    private String deviceName;

    /**
     * 地点名称
     */
    @Column(name = "place_name")
    private String placeName;

    /**
     * 地点编码
     */
    @Column(name = "place_code")
    private String placeCode;

    /**
     * 记录日期 (格式: YYYY-MM-DD)
     */
    @Column(name = "record_date")
    private String recordDate;

    /**
     * 记录时间 (格式: HH:mm:ss)
     */
    @Column(name = "record_time")
    private String recordTime;

    /**
     * 通行时间
     */
    @Column(name = "pass_time")
    private LocalDateTime passTime;

    /**
     * 区域编码
     */
    @Column(name = "area_code")
    private String areaCode;

    /**
     * 区域名称
     */
    @Column(name = "area_name")
    private String areaName;

    /**
     * 进出标志
     * 1 = 进入 (进入寝室)
     * 2 = 离开 (离开寝室)
     */
    @Column(name = "inorout")
    private Integer inOrOut;

    /**
     * 人员类型
     */
    @Column(name = "person_type")
    private Integer personType;

    /**
     * 人脸ID
     */
    @Column(name = "face_id")
    private Long faceId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 匹配状态
     */
    @Column(name = "match_status")
    private Integer matchStatus;

    /**
     * 匹配置信度
     */
    @Column(name = "match_confidence")
    private Integer matchConfidence;

    /**
     * 设备IP
     */
    @Column(name = "device_ip")
    private String deviceIp;

    /**
     * 人员性别
     */
    @Column(name = "person_sex")
    private Integer personSex;

    /**
     * 体温
     */
    @Column(name = "temperature")
    private Double temperature;

    // ==================== 构造函数 ====================
    
    public AccessControlRecord() {
    }

    public AccessControlRecord(String personCode, String personName, Integer inOrOut, LocalDateTime passTime) {
        this.personCode = personCode;
        this.personName = personName;
        this.inOrOut = inOrOut;
        this.passTime = passTime;
    }

    // ==================== Getter和Setter方法 ====================
    
    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getPersonCode() {
        return personCode;
    }

    public void setPersonCode(String personCode) {
        this.personCode = personCode;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName;
    }

    public String getPlaceCode() {
        return placeCode;
    }

    public void setPlaceCode(String placeCode) {
        this.placeCode = placeCode;
    }

    public String getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(String recordDate) {
        this.recordDate = recordDate;
    }

    public String getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(String recordTime) {
        this.recordTime = recordTime;
    }

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getInOrOut() {
        return inOrOut;
    }

    public void setInOrOut(Integer inOrOut) {
        this.inOrOut = inOrOut;
    }

    public Integer getPersonType() {
        return personType;
    }

    public void setPersonType(Integer personType) {
        this.personType = personType;
    }

    public Long getFaceId() {
        return faceId;
    }

    public void setFaceId(Long faceId) {
        this.faceId = faceId;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getMatchStatus() {
        return matchStatus;
    }

    public void setMatchStatus(Integer matchStatus) {
        this.matchStatus = matchStatus;
    }

    public Integer getMatchConfidence() {
        return matchConfidence;
    }

    public void setMatchConfidence(Integer matchConfidence) {
        this.matchConfidence = matchConfidence;
    }

    public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    public Integer getPersonSex() {
        return personSex;
    }

    public void setPersonSex(Integer personSex) {
        this.personSex = personSex;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    // ==================== 业务方法 ====================
    
    /**
     * 判断是否为进入记录
     * @return true-进入, false-离开
     */
    public boolean isEntering() {
        return inOrOut != null && inOrOut == 1;
    }

    /**
     * 判断是否为离开记录
     * @return true-离开, false-进入
     */
    public boolean isLeaving() {
        return inOrOut != null && inOrOut == 2;
    }

    /**
     * 获取进出状态描述
     * @return 进出状态文字描述
     */
    public String getInOrOutDescription() {
        if (inOrOut == null) return "未知";
        return inOrOut == 1 ? "进入" : "离开";
    }

    // ==================== toString方法 ====================
    
    @Override
    public String toString() {
        return "AccessControlRecord{" +
                "recordId=" + recordId +
                ", personCode='" + personCode + '\'' +
                ", personName='" + personName + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", recordDate='" + recordDate + '\'' +
                ", recordTime='" + recordTime + '\'' +
                ", passTime=" + passTime +
                ", inOrOut=" + inOrOut +
                ", areaName='" + areaName + '\'' +
                '}';
    }
}
