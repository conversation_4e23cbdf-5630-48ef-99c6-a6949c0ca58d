package fastgatedemo.demo.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @description 定时任务信息实体类
 */
@Entity
@Table(name = "scheduled_task_info")
public class ScheduledTaskInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_name", nullable = false, unique = true, length = 100)
    private String taskName;

    @Column(name = "task_description", length = 500)
    private String taskDescription;

    @Column(name = "task_class", nullable = false, length = 200)
    private String taskClass;

    @Column(name = "task_method", nullable = false, length = 100)
    private String taskMethod;

    @Column(name = "cron_expression", length = 100)
    private String cronExpression;

    @Column(name = "fixed_rate")
    private Long fixedRate;

    @Column(name = "fixed_delay")
    private Long fixedDelay;

    @Column(name = "enabled")
    private Boolean enabled;

    @Column(name = "last_execution_time")
    private LocalDateTime lastExecutionTime;

    @Column(name = "last_execution_status", length = 20)
    private String lastExecutionStatus; // SUCCESS, FAILED, RUNNING

    @Column(name = "last_execution_duration")
    private Long lastExecutionDuration; // 执行时长（毫秒）

    @Column(name = "last_error_message", length = 1000)
    private String lastErrorMessage;

    @Column(name = "execution_count")
    private Long executionCount;

    @Column(name = "success_count")
    private Long successCount;

    @Column(name = "failure_count")
    private Long failureCount;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    // 构造函数
    public ScheduledTaskInfo() {
        this.enabled = true;
        this.executionCount = 0L;
        this.successCount = 0L;
        this.failureCount = 0L;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskDescription() {
        return taskDescription;
    }

    public void setTaskDescription(String taskDescription) {
        this.taskDescription = taskDescription;
    }

    public String getTaskClass() {
        return taskClass;
    }

    public void setTaskClass(String taskClass) {
        this.taskClass = taskClass;
    }

    public String getTaskMethod() {
        return taskMethod;
    }

    public void setTaskMethod(String taskMethod) {
        this.taskMethod = taskMethod;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public Long getFixedRate() {
        return fixedRate;
    }

    public void setFixedRate(Long fixedRate) {
        this.fixedRate = fixedRate;
    }

    public Long getFixedDelay() {
        return fixedDelay;
    }

    public void setFixedDelay(Long fixedDelay) {
        this.fixedDelay = fixedDelay;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getLastExecutionTime() {
        return lastExecutionTime;
    }

    public void setLastExecutionTime(LocalDateTime lastExecutionTime) {
        this.lastExecutionTime = lastExecutionTime;
    }

    public String getLastExecutionStatus() {
        return lastExecutionStatus;
    }

    public void setLastExecutionStatus(String lastExecutionStatus) {
        this.lastExecutionStatus = lastExecutionStatus;
    }

    public Long getLastExecutionDuration() {
        return lastExecutionDuration;
    }

    public void setLastExecutionDuration(Long lastExecutionDuration) {
        this.lastExecutionDuration = lastExecutionDuration;
    }

    public String getLastErrorMessage() {
        return lastErrorMessage;
    }

    public void setLastErrorMessage(String lastErrorMessage) {
        this.lastErrorMessage = lastErrorMessage;
    }

    public Long getExecutionCount() {
        return executionCount;
    }

    public void setExecutionCount(Long executionCount) {
        this.executionCount = executionCount;
    }

    public Long getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }

    public Long getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(Long failureCount) {
        this.failureCount = failureCount;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @PreUpdate
    public void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "ScheduledTaskInfo{" +
                "id=" + id +
                ", taskName='" + taskName + '\'' +
                ", taskDescription='" + taskDescription + '\'' +
                ", enabled=" + enabled +
                ", lastExecutionTime=" + lastExecutionTime +
                ", lastExecutionStatus='" + lastExecutionStatus + '\'' +
                ", executionCount=" + executionCount +
                ", successCount=" + successCount +
                ", failureCount=" + failureCount +
                '}';
    }
}
