package fastgatedemo.demo.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @description 中间库宿舍信息实体
 * 映射数据库表：dormitory_info
 */
@Entity
@Table(name = "dormitory_info")
public class MiddlewareDormitoryInfo {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "dormitory_code", nullable = false, length = 50, unique = true)
    private String dormitoryCode;

    @Column(name = "building_code", nullable = false, length = 50)
    private String buildingCode;

    @Column(name = "building_name", nullable = false, length = 50)
    private String buildingName;

    @Column(name = "floor", nullable = false)
    private Integer floor;

    @Column(name = "room_num", nullable = false)
    private Integer roomNum;

    @Column(name = "room_name", nullable = false, length = 100)
    private String roomName;

    @Column(name = "parent_code", nullable = false, length = 50)
    private String parentCode;

    @Column(name = "floor_name", length = 100)
    private String floorName;

    @Column(name = "gender_code", length = 10)
    private String genderCode;

    @Column(name = "bed_count")
    private Integer bedCount;

    @Column(name = "status")
    private Integer status;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    // 构造函数
    public MiddlewareDormitoryInfo() {}

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDormitoryCode() {
        return dormitoryCode;
    }

    public void setDormitoryCode(String dormitoryCode) {
        this.dormitoryCode = dormitoryCode;
    }

    public String getBuildingCode() {
        return buildingCode;
    }

    public void setBuildingCode(String buildingCode) {
        this.buildingCode = buildingCode;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public Integer getFloor() {
        return floor;
    }

    public void setFloor(Integer floor) {
        this.floor = floor;
    }

    public Integer getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(Integer roomNum) {
        this.roomNum = roomNum;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getFloorName() {
        return floorName;
    }

    public void setFloorName(String floorName) {
        this.floorName = floorName;
    }

    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }

    public Integer getBedCount() {
        return bedCount;
    }

    public void setBedCount(Integer bedCount) {
        this.bedCount = bedCount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "MiddlewareDormitoryInfo{" +
                "id=" + id +
                ", dormitoryCode='" + dormitoryCode + '\'' +
                ", buildingCode='" + buildingCode + '\'' +
                ", buildingName='" + buildingName + '\'' +
                ", floor=" + floor +
                ", roomNum=" + roomNum +
                ", roomName='" + roomName + '\'' +
                ", parentCode='" + parentCode + '\'' +
                ", floorName='" + floorName + '\'' +
                ", genderCode='" + genderCode + '\'' +
                ", bedCount=" + bedCount +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
} 