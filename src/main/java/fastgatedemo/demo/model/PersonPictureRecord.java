package fastgatedemo.demo.model;

import com.alibaba.fastjson.annotation.JSONField;

/**
* @Date: 2018/8/2
*/
public class PersonPictureRecord {


   @JSONField(name ="Cause")
    private String cause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_picture.seqid
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
   @JSONField(name ="Seqid")
    private Integer seqid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_picture.person_picture_path
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
   @JSONField(name ="PersonPicturePath")
    private String personPicturePath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_picture.person_seqid
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
   @JSONField(name ="PersonSeqid")
    private Integer personSeqid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tbl_person_picture.status
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
   @JSONField(name ="Status")
    private Integer status;
   @JSONField(name ="PersonFeaturePath")
    private String personFeaturePath;

    public String getPersonFeaturePath() {
        return personFeaturePath;
    }

    public void setPersonFeaturePath(String personFeaturePath) {
        this.personFeaturePath = personFeaturePath;
    }

   @JSONField(name ="ApplicationStatus")
    private Integer applicationStatus;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_picture.seqid
     *
     * @return the value of tbl_person_picture.seqid
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }
    public Integer getSeqid() {
        return seqid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_picture.seqid
     *
     * @param seqid the value for tbl_person_picture.seqid
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
    public void setSeqid(Integer seqid) {
        this.seqid = seqid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_picture.person_picture_path
     *
     * @return the value of tbl_person_picture.person_picture_path
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
    public String getPersonPicturePath() {
        return personPicturePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_picture.person_picture_path
     *
     * @param personPicturePath the value for tbl_person_picture.person_picture_path
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
    public void setPersonPicturePath(String personPicturePath) {
        this.personPicturePath = personPicturePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_picture.person_seqid
     *
     * @return the value of tbl_person_picture.person_seqid
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
    public Integer getPersonSeqid() {
        return personSeqid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_picture.person_seqid
     *
     * @param personSeqid the value for tbl_person_picture.person_seqid
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
    public void setPersonSeqid(Integer personSeqid) {
        this.personSeqid = personSeqid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_picture.status
     *
     * @return the value of tbl_person_picture.status
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_picture.status
     *
     * @param status the value for tbl_person_picture.status
     *
     * @mbggenerated Wed Nov 29 13:53:00 CST 2017
     */
    public void setStatus(Integer status) {
        this.status = status;
    }
    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tbl_person_picture.application_status
     *
     * @return the value of tbl_person_picture.application_status
     *
     * @mbggenerated Fri Mar 09 17:12:20 CST 2018
     */
    public Integer getApplicationStatus() {
        return applicationStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tbl_person_picture.application_status
     *
     * @param applicationStatus the value for tbl_person_picture.application_status
     *
     * @mbggenerated Fri Mar 09 17:12:20 CST 2018
     */
    public void setApplicationStatus(Integer applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    @Override
    public String toString() {
        return "PersonPictureRecord{" +
                "cause='" + cause + '\'' +
                ", seqid=" + seqid +
                ", personPicturePath='" + personPicturePath + '\'' +
                ", personSeqid=" + personSeqid +
                ", status=" + status +
                ", personFeaturePath='" + personFeaturePath + '\'' +
                ", applicationStatus=" + applicationStatus +
                '}';
    }
}