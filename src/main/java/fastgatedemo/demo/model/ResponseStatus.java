package fastgatedemo.demo.model;

/**
 * 响应状态Bean
 */
public class ResponseStatus {
    private String RequestURL;
    private int StatusCode;
    private String StatusString;

    public ResponseStatus(String requestURL) {
        this.RequestURL = requestURL;
        this.StatusCode = 0;
        this.StatusString = "OK";
    }

    public ResponseStatus(String requestURL, int statusCode, String statusString) {
        this.RequestURL = requestURL;
        this.StatusCode = statusCode;
        this.StatusString = statusString;
    }

    public String getRequestURL() {
        return RequestURL;
    }

    public void setRequestURL(String requestURL) {
        RequestURL = requestURL;
    }

    public int getStatusCode() {
        return StatusCode;
    }

    public void setStatusCode(int statusCode) {
        StatusCode = statusCode;
    }

    public String getStatusString() {
        return StatusString;
    }

    public void setStatusString(String statusString) {
        StatusString = statusString;
    }
}