package fastgatedemo.demo.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * @description 定时任务执行日志实体类
 */
@Entity
@Table(name = "task_execution_log")
public class TaskExecutionLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_name", nullable = false, length = 100)
    private String taskName;

    @Column(name = "execution_start_time", nullable = false)
    private LocalDateTime executionStartTime;

    @Column(name = "execution_end_time")
    private LocalDateTime executionEndTime;

    @Column(name = "execution_duration")
    private Long executionDuration; // 执行时长（毫秒）

    @Column(name = "execution_status", nullable = false, length = 20)
    private String executionStatus; // SUCCESS, FAILED, RUNNING

    @Column(name = "result_message", length = 2000)
    private String resultMessage;

    @Column(name = "error_message", length = 2000)
    private String errorMessage;

    @Column(name = "processed_records")
    private Integer processedRecords;

    @Column(name = "success_records")
    private Integer successRecords;

    @Column(name = "failed_records")
    private Integer failedRecords;

    @Column(name = "server_ip", length = 50)
    private String serverIp;

    @Column(name = "thread_name", length = 100)
    private String threadName;

    // 构造函数
    public TaskExecutionLog() {
        this.executionStartTime = LocalDateTime.now();
        this.executionStatus = "RUNNING";
    }

    public TaskExecutionLog(String taskName) {
        this();
        this.taskName = taskName;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public LocalDateTime getExecutionStartTime() {
        return executionStartTime;
    }

    public void setExecutionStartTime(LocalDateTime executionStartTime) {
        this.executionStartTime = executionStartTime;
    }

    public LocalDateTime getExecutionEndTime() {
        return executionEndTime;
    }

    public void setExecutionEndTime(LocalDateTime executionEndTime) {
        this.executionEndTime = executionEndTime;
        if (this.executionStartTime != null && executionEndTime != null) {
            this.executionDuration = java.time.Duration.between(this.executionStartTime, executionEndTime).toMillis();
        }
    }

    public Long getExecutionDuration() {
        return executionDuration;
    }

    public void setExecutionDuration(Long executionDuration) {
        this.executionDuration = executionDuration;
    }

    public String getExecutionStatus() {
        return executionStatus;
    }

    public void setExecutionStatus(String executionStatus) {
        this.executionStatus = executionStatus;
    }

    public String getResultMessage() {
        return resultMessage;
    }

    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getProcessedRecords() {
        return processedRecords;
    }

    public void setProcessedRecords(Integer processedRecords) {
        this.processedRecords = processedRecords;
    }

    public Integer getSuccessRecords() {
        return successRecords;
    }

    public void setSuccessRecords(Integer successRecords) {
        this.successRecords = successRecords;
    }

    public Integer getFailedRecords() {
        return failedRecords;
    }

    public void setFailedRecords(Integer failedRecords) {
        this.failedRecords = failedRecords;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public String getThreadName() {
        return threadName;
    }

    public void setThreadName(String threadName) {
        this.threadName = threadName;
    }

    /**
     * 标记任务执行成功
     */
    public void markSuccess(String message) {
        this.executionEndTime = LocalDateTime.now();
        this.executionStatus = "SUCCESS";
        this.resultMessage = message;
        calculateDuration();
    }

    /**
     * 标记任务执行失败
     */
    public void markFailure(String errorMessage) {
        this.executionEndTime = LocalDateTime.now();
        this.executionStatus = "FAILED";
        this.errorMessage = errorMessage;
        calculateDuration();
    }

    /**
     * 计算执行时长
     */
    private void calculateDuration() {
        if (this.executionStartTime != null && this.executionEndTime != null) {
            this.executionDuration = java.time.Duration.between(this.executionStartTime, this.executionEndTime).toMillis();
        }
    }

    @Override
    public String toString() {
        return "TaskExecutionLog{" +
                "id=" + id +
                ", taskName='" + taskName + '\'' +
                ", executionStartTime=" + executionStartTime +
                ", executionEndTime=" + executionEndTime +
                ", executionDuration=" + executionDuration +
                ", executionStatus='" + executionStatus + '\'' +
                ", processedRecords=" + processedRecords +
                ", successRecords=" + successRecords +
                ", failedRecords=" + failedRecords +
                '}';
    }
}
