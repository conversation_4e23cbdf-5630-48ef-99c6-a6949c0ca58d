package fastgatedemo.demo.model;

/**
 * 设备信息Bean
 */
public class GaDevice {
    private String DeviceID;
    private String Name;
    private String Model;
    private String Owner;
    private String CivilCode;
    private String RegisterWay;
    private String Secrecy;
    private String IPAddr;
    private String Port;
    private String Password;
    private String Status;
    private String Longitude;
    private String Latitude;

    // 构造函数
    public GaDevice() {}

    // Getter和Setter方法
    public String getDeviceID() {
        return DeviceID;
    }

    public void setDeviceID(String deviceID) {
        DeviceID = deviceID;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getModel() {
        return Model;
    }

    public void setModel(String model) {
        Model = model;
    }

    public String getOwner() {
        return Owner;
    }

    public void setOwner(String owner) {
        Owner = owner;
    }

    public String getCivilCode() {
        return CivilCode;
    }

    public void setCivilCode(String civilCode) {
        CivilCode = civilCode;
    }

    public String getRegisterWay() {
        return RegisterWay;
    }

    public void setRegisterWay(String registerWay) {
        RegisterWay = registerWay;
    }

    public String getSecrecy() {
        return Secrecy;
    }

    public void setSecrecy(String secrecy) {
        Secrecy = secrecy;
    }

    public String getIPAddr() {
        return IPAddr;
    }

    public void setIPAddr(String IPAddr) {
        this.IPAddr = IPAddr;
    }

    public String getPort() {
        return Port;
    }

    public void setPort(String port) {
        Port = port;
    }

    public String getPassword() {
        return Password;
    }

    public void setPassword(String password) {
        Password = password;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String status) {
        Status = status;
    }

    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String longitude) {
        Longitude = longitude;
    }

    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String latitude) {
        Latitude = latitude;
    }
}