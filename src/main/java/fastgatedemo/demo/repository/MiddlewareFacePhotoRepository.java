package fastgatedemo.demo.repository;

import fastgatedemo.demo.model.MiddlewareFacePhoto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * @description 中间库人脸照片数据访问层
 */
@Repository
public interface MiddlewareFacePhotoRepository extends JpaRepository<MiddlewareFacePhoto, Long> {
    
    /**
     * 根据人员编码查询照片
     */
    Optional<MiddlewareFacePhoto> findByPerCode(String perCode);
    
    /**
     * 根据学号/工号查询照片
     */
    Optional<MiddlewareFacePhoto> findByAccNum(String accNum);
    
    /**
     * 根据人员编码列表查询照片
     */
    @Query("SELECT f FROM MiddlewareFacePhoto f WHERE f.perCode IN :perCodes")
    List<MiddlewareFacePhoto> findByPerCodeIn(@Param("perCodes") List<String> perCodes);
    
    /**
     * 查询有效的照片
     */
    @Query("SELECT f FROM MiddlewareFacePhoto f WHERE f.faceStatus = 1 AND f.applicationStatus = 1")
    List<MiddlewareFacePhoto> findValidPhotos();
    
    /**
     * 统计有效照片数量
     */
    @Query("SELECT COUNT(f) FROM MiddlewareFacePhoto f WHERE f.faceStatus = 1 AND f.applicationStatus = 1")
    Long countValidPhotos();
    
    /**
     * 根据人员编码列表批量更新同步标识为0
     * @param perCodes 人员编码列表
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query("UPDATE MiddlewareFacePhoto f SET f.syncFlag = 0, f.updateTime = CURRENT_TIMESTAMP WHERE f.perCode IN :perCodes")
    int updateSyncFlagToZeroByPerCodes(@Param("perCodes") List<String> perCodes);
    
    /**
     * 根据人员编码更新单个记录的同步标识为0
     * @param perCode 人员编码
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query("UPDATE MiddlewareFacePhoto f SET f.syncFlag = 0, f.updateTime = CURRENT_TIMESTAMP WHERE f.perCode = :perCode")
    int updateSyncFlagToZeroByPerCode(@Param("perCode") String perCode);
    
    /**
     * 查询指定人员编码列表中存在face_photo记录的人员编码
     * @param perCodes 人员编码列表
     * @return 存在记录的人员编码列表
     */
    @Query("SELECT DISTINCT f.perCode FROM MiddlewareFacePhoto f WHERE f.perCode IN :perCodes")
    List<String> findExistingPerCodes(@Param("perCodes") List<String> perCodes);
} 