package fastgatedemo.demo.repository;

import fastgatedemo.demo.model.PersonDormitoryRelation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * @description 人员宿舍关联数据访问层
 */
@Repository
public interface PersonDormitoryRelationRepository extends JpaRepository<PersonDormitoryRelation, String> {
    
    /**
     * 根据人员编码查询关联关系
     * @param personCode 人员编码
     * @return 关联关系列表
     */
    List<PersonDormitoryRelation> findByPersonCode(String personCode);
    
    /**
     * 根据宿舍编码查询关联关系
     * @param dormitoryCode 宿舍编码
     * @return 关联关系列表
     */
    List<PersonDormitoryRelation> findByDormitoryCode(String dormitoryCode);
    
    /**
     * 根据状态查询关联关系
     * @param status 状态 (1-有效, 0-无效)
     * @return 关联关系列表
     */
    List<PersonDormitoryRelation> findByStatus(Integer status);
    
    /**
     * 根据同步标识查询关联关系
     * @param syncFlag 同步标识 (0-未同步, 1-已同步)
     * @return 关联关系列表
     */
    List<PersonDormitoryRelation> findBySyncFlag(Integer syncFlag);
    
    /**
     * 查询有效且未同步的关联关系
     * @return 未同步的关联关系列表
     */
    List<PersonDormitoryRelation> findByStatusAndSyncFlag(Integer status, Integer syncFlag);

    /**
     * 分页查询有效且未同步的关联关系
     * @param status 状态
     * @param syncFlag 同步标识
     * @param pageable 分页参数
     * @return 分页的未同步关联关系列表
     */
    Page<PersonDormitoryRelation> findByStatusAndSyncFlag(Integer status, Integer syncFlag, Pageable pageable);
    
    /**
     * 根据人员编码和宿舍编码查询关联关系
     * @param personCode 人员编码
     * @param dormitoryCode 宿舍编码
     * @return 关联关系
     */
    Optional<PersonDormitoryRelation> findByPersonCodeAndDormitoryCode(String personCode, String dormitoryCode);
    

    
    /**
     * 批量更新同步标识
     * @param ids 关联关系ID列表
     * @param syncFlag 同步标识
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE person_dormitory_relation SET sync_flag = :syncFlag, update_time = CURRENT_TIMESTAMP WHERE id IN :ids", nativeQuery = true)
    int updateSyncFlagByIds(@Param("ids") List<String> ids, @Param("syncFlag") Integer syncFlag);

    /**
     * 根据人员编码更新同步标识
     * @param personCodes 人员编码列表
     * @param syncFlag 同步标识
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE person_dormitory_relation SET sync_flag = :syncFlag, update_time = CURRENT_TIMESTAMP WHERE person_code IN :personCodes AND status = 1", nativeQuery = true)
    int updateSyncFlagByPersonCodes(@Param("personCodes") List<String> personCodes, @Param("syncFlag") Integer syncFlag);
    
    /**
     * 统计未同步的关联关系数量
     * @return 未同步的关联关系数量
     */
    @Query(value = "SELECT COUNT(*) FROM person_dormitory_relation WHERE status = 1 AND sync_flag = 0", nativeQuery = true)
    Long countUnsyncedRelations();

    /**
     * 统计已同步的关联关系数量
     * @return 已同步的关联关系数量
     */
    @Query(value = "SELECT COUNT(*) FROM person_dormitory_relation WHERE status = 1 AND sync_flag = 1", nativeQuery = true)
    Long countSyncedRelations();
}
