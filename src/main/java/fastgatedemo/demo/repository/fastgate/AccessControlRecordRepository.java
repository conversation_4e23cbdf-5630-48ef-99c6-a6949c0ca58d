package fastgatedemo.demo.repository.fastgate;

import com.baomidou.dynamic.datasource.annotation.DS;
import fastgatedemo.demo.model.fastgate.AccessControlRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @description FastGate数据源通行记录数据访问层
 * 专门用于访问fastgate数据库中的tbl_access_control_record表
 * <AUTHOR>
 * @date 2025-01-25
 */
@Repository
@DS("fastgate")
public interface AccessControlRecordRepository extends JpaRepository<AccessControlRecord, Long> {
    
    /**
     * 根据人员编码查询最后一条通行记录
     * 用于判断人员当前是否在寝室
     * @param personCode 人员编码
     * @return 最后一条通行记录
     */
    @Query("SELECT r FROM AccessControlRecord r WHERE r.personCode = :personCode " +
           "ORDER BY r.passTime DESC")
    List<AccessControlRecord> findTopByPersonCodeOrderByPassTimeDesc(@Param("personCode") String personCode);

    /**
     * 根据人员编码和日期查询最后一条通行记录
     * @param personCode 人员编码
     * @param recordDate 记录日期 (格式: YYYY-MM-DD)
     * @return 最后一条通行记录
     */
    @Query("SELECT r FROM AccessControlRecord r WHERE r.personCode = :personCode " +
           "AND r.recordDate = :recordDate " +
           "ORDER BY r.passTime DESC")
    List<AccessControlRecord> findTopByPersonCodeAndRecordDateOrderByPassTimeDesc(
            @Param("personCode") String personCode, 
            @Param("recordDate") String recordDate);

    /**
     * 查询指定日期所有人员的最后一条通行记录
     * 用于批量判断寝室归宿状态
     * @param recordDate 记录日期 (格式: YYYY-MM-DD)
     * @return 通行记录列表
     */
    @Query("SELECT r1 FROM AccessControlRecord r1 WHERE r1.recordDate = :recordDate " +
           "AND r1.passTime = (SELECT MAX(r2.passTime) FROM AccessControlRecord r2 " +
           "WHERE r2.personCode = r1.personCode AND r2.recordDate = :recordDate) " +
           "AND r1.personCode IS NOT NULL AND r1.personCode != '-' " +
           "ORDER BY r1.personCode")
    List<AccessControlRecord> findLastRecordsByDate(@Param("recordDate") String recordDate);

    /**
     * 查询指定日期未归寝室的人员记录
     * 最后一条记录为离开(inorout=2)的人员
     * @param recordDate 记录日期 (格式: YYYY-MM-DD)
     * @return 未归寝室人员的通行记录
     */
    @Query("SELECT r1 FROM AccessControlRecord r1 WHERE r1.recordDate = :recordDate " +
           "AND r1.inOrOut = 2 " +
           "AND r1.passTime = (SELECT MAX(r2.passTime) FROM AccessControlRecord r2 " +
           "WHERE r2.personCode = r1.personCode AND r2.recordDate = :recordDate) " +
           "AND r1.personCode IS NOT NULL AND r1.personCode != '-' " +
           "ORDER BY r1.passTime DESC")
    List<AccessControlRecord> findNotReturnedPersonsByDate(@Param("recordDate") String recordDate);

    /**
     * 查询指定日期已归寝室的人员记录
     * 最后一条记录为进入(inorout=1)的人员
     * @param recordDate 记录日期 (格式: YYYY-MM-DD)
     * @return 已归寝室人员的通行记录
     */
    @Query("SELECT r1 FROM AccessControlRecord r1 WHERE r1.recordDate = :recordDate " +
           "AND r1.inOrOut = 1 " +
           "AND r1.passTime = (SELECT MAX(r2.passTime) FROM AccessControlRecord r2 " +
           "WHERE r2.personCode = r1.personCode AND r2.recordDate = :recordDate) " +
           "AND r1.personCode IS NOT NULL AND r1.personCode != '-' " +
           "ORDER BY r1.passTime DESC")
    List<AccessControlRecord> findReturnedPersonsByDate(@Param("recordDate") String recordDate);

    /**
     * 分页查询指定日期未归寝室的人员记录
     * @param recordDate 记录日期 (格式: YYYY-MM-DD)
     * @param pageable 分页参数
     * @return 分页的未归寝室人员记录
     */
    @Query("SELECT r1 FROM AccessControlRecord r1 WHERE r1.recordDate = :recordDate " +
           "AND r1.inOrOut = 2 " +
           "AND r1.passTime = (SELECT MAX(r2.passTime) FROM AccessControlRecord r2 " +
           "WHERE r2.personCode = r1.personCode AND r2.recordDate = :recordDate) " +
           "AND r1.personCode IS NOT NULL AND r1.personCode != '-' " +
           "ORDER BY r1.passTime DESC")
    Page<AccessControlRecord> findNotReturnedPersonsByDateWithPage(@Param("recordDate") String recordDate, Pageable pageable);

    /**
     * 根据人员编码列表查询指定日期的最后通行记录
     * @param personCodes 人员编码列表
     * @param recordDate 记录日期
     * @return 通行记录列表
     */
    @Query("SELECT r1 FROM AccessControlRecord r1 WHERE r1.recordDate = :recordDate " +
           "AND r1.personCode IN :personCodes " +
           "AND r1.passTime = (SELECT MAX(r2.passTime) FROM AccessControlRecord r2 " +
           "WHERE r2.personCode = r1.personCode AND r2.recordDate = :recordDate) " +
           "ORDER BY r1.personCode")
    List<AccessControlRecord> findLastRecordsByPersonCodesAndDate(
            @Param("personCodes") List<String> personCodes, 
            @Param("recordDate") String recordDate);

    /**
     * 统计指定日期的通行记录总数
     * @param recordDate 记录日期
     * @return 记录总数
     */
    long countByRecordDate(String recordDate);

    /**
     * 优化的统计查询 - 使用原生SQL一次性获取所有统计信息
     * 返回结果：[未归寝室人数, 已归寝室人数, 总记录数]
     * @param recordDate 记录日期
     * @return 统计结果数组
     */
    @Query(value = "WITH last_records AS (" +
            "    SELECT person_code, inorout, " +
            "           ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "    FROM tbl_access_control_record " +
            "    WHERE record_date = :recordDate " +
            "    AND person_code IS NOT NULL " +
            "    AND person_code != '-' " +
            "), " +
            "person_status AS (" +
            "    SELECT person_code, inorout " +
            "    FROM last_records " +
            "    WHERE rn = 1 " +
            ") " +
            "SELECT " +
            "    COALESCE(SUM(CASE WHEN inorout = 2 THEN 1 ELSE 0 END), 0) as not_returned_count, " +
            "    COALESCE(SUM(CASE WHEN inorout = 1 THEN 1 ELSE 0 END), 0) as returned_count, " +
            "    (SELECT COUNT(*) FROM tbl_access_control_record WHERE record_date = :recordDate) as total_records " +
            "FROM person_status",
            nativeQuery = true)
    Object[] getOptimizedStatisticsByDate(@Param("recordDate") String recordDate);

    /**
     * 简化版统计查询 - 适用于不支持CTE的情况
     * 使用EXISTS子查询优化性能
     * @param recordDate 记录日期
     * @return 未归寝室人员数量
     */
    @Query(value = "SELECT COUNT(DISTINCT r1.person_code) " +
            "FROM tbl_access_control_record r1 " +
            "WHERE r1.record_date = :recordDate " +
            "AND r1.inorout = 2 " +
            "AND r1.person_code IS NOT NULL " +
            "AND r1.person_code != '-' " +
            "AND r1.pass_time = (" +
            "    SELECT MAX(r2.pass_time) " +
            "    FROM tbl_access_control_record r2 " +
            "    WHERE r2.person_code = r1.person_code " +
            "    AND r2.record_date = :recordDate" +
            ")",
            nativeQuery = true)
    long countNotReturnedPersonsByDateOptimized(@Param("recordDate") String recordDate);

    /**
     * 简化版统计查询 - 已归寝室人员数量
     * @param recordDate 记录日期
     * @return 已归寝室人员数量
     */
    @Query(value = "SELECT COUNT(DISTINCT r1.person_code) " +
            "FROM tbl_access_control_record r1 " +
            "WHERE r1.record_date = :recordDate " +
            "AND r1.inorout = 1 " +
            "AND r1.person_code IS NOT NULL " +
            "AND r1.person_code != '-' " +
            "AND r1.pass_time = (" +
            "    SELECT MAX(r2.pass_time) " +
            "    FROM tbl_access_control_record r2 " +
            "    WHERE r2.person_code = r1.person_code " +
            "    AND r2.record_date = :recordDate" +
            ")",
            nativeQuery = true)
    long countReturnedPersonsByDateOptimized(@Param("recordDate") String recordDate);

    /**
     * 统计指定日期未归寝室的人员数量
     * @param recordDate 记录日期
     * @return 未归寝室人员数量
     */
    @Query("SELECT COUNT(DISTINCT r1.personCode) FROM AccessControlRecord r1 WHERE r1.recordDate = :recordDate " +
           "AND r1.inOrOut = 2 " +
           "AND r1.passTime = (SELECT MAX(r2.passTime) FROM AccessControlRecord r2 " +
           "WHERE r2.personCode = r1.personCode AND r2.recordDate = :recordDate) " +
           "AND r1.personCode IS NOT NULL AND r1.personCode != '-'")
    long countNotReturnedPersonsByDate(@Param("recordDate") String recordDate);

    /**
     * 统计指定日期有通行记录的人员数量
     * @param recordDate 记录日期
     * @return 有通行记录的人员数量
     */
    @Query("SELECT COUNT(DISTINCT r.personCode) FROM AccessControlRecord r " +
           "WHERE r.recordDate = :recordDate " +
           "AND r.personCode IS NOT NULL AND r.personCode != '-'")
    long countPersonsWithRecordsByDate(@Param("recordDate") String recordDate);

    /**
     * 统计指定日期已归寝室的人员数量
     * @param recordDate 记录日期
     * @return 已归寝室人员数量
     */
    @Query("SELECT COUNT(DISTINCT r1.personCode) FROM AccessControlRecord r1 WHERE r1.recordDate = :recordDate " +
           "AND r1.inOrOut = 1 " +
           "AND r1.passTime = (SELECT MAX(r2.passTime) FROM AccessControlRecord r2 " +
           "WHERE r2.personCode = r1.personCode AND r2.recordDate = :recordDate) " +
           "AND r1.personCode IS NOT NULL AND r1.personCode != '-'")
    long countReturnedPersonsByDate(@Param("recordDate") String recordDate);

    /**
     * 查询指定日期范围内的通行记录
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 通行记录列表
     */
    @Query("SELECT r FROM AccessControlRecord r WHERE r.recordDate >= :startDate " +
           "AND r.recordDate <= :endDate " +
           "AND r.personCode IS NOT NULL AND r.personCode != '-' " +
           "ORDER BY r.passTime DESC")
    List<AccessControlRecord> findByRecordDateBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 根据人员姓名模糊查询指定日期的通行记录
     * @param recordDate 记录日期
     * @param personName 人员姓名（支持模糊查询）
     * @return 通行记录列表
     */
    @Query("SELECT r FROM AccessControlRecord r WHERE r.recordDate = :recordDate " +
           "AND r.personName LIKE %:personName% " +
           "AND r.personCode IS NOT NULL AND r.personCode != '-' " +
           "ORDER BY r.passTime DESC")
    List<AccessControlRecord> findByRecordDateAndPersonNameContaining(
            @Param("recordDate") String recordDate, 
            @Param("personName") String personName);

    /**
     * 查询指定时间范围内的通行记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 通行记录列表
     */
    List<AccessControlRecord> findByPassTimeBetweenOrderByPassTimeDesc(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据设备编码查询指定日期的通行记录
     * @param deviceCode 设备编码
     * @param recordDate 记录日期
     * @return 通行记录列表
     */
    List<AccessControlRecord> findByDeviceCodeAndRecordDateOrderByPassTimeDesc(String deviceCode, String recordDate);

    /**
     * 根据区域编码查询指定日期的通行记录
     * @param areaCode 区域编码
     * @param recordDate 记录日期
     * @return 通行记录列表
     */
    List<AccessControlRecord> findByAreaCodeAndRecordDateOrderByPassTimeDesc(String areaCode, String recordDate);

    /**
     * 统计当前在寝人数（按最新通行记录）
     * @return 在寝人数
     */
    @Query(value = "WITH last_records AS (" +
            "    SELECT person_code, inorout, " +
            "           ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "    FROM tbl_access_control_record " +
            "    WHERE person_code IS NOT NULL " +
            "    AND person_code != '-' " +
            ") " +
            "SELECT COUNT(*) " +
            "FROM last_records " +
            "WHERE rn = 1 AND inorout = 1",
            nativeQuery = true)
    long countCurrentReturnedPersons();

    /**
     * 统计当前外出人数（按最新通行记录）
     * @return 外出人数
     */
    @Query(value = "WITH last_records AS (" +
            "    SELECT person_code, inorout, " +
            "           ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "    FROM tbl_access_control_record " +
            "    WHERE person_code IS NOT NULL " +
            "    AND person_code != '-' " +
            ") " +
            "SELECT COUNT(*) " +
            "FROM last_records " +
            "WHERE rn = 1 AND inorout = 2",
            nativeQuery = true)
    long countCurrentNotReturnedPersons();

    /**
     * 统计有通行记录的人员总数（按最新通行记录）
     * @return 有记录人员总数
     */
    @Query(value = "WITH last_records AS (" +
            "    SELECT person_code, inorout, " +
            "           ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "    FROM tbl_access_control_record " +
            "    WHERE person_code IS NOT NULL " +
            "    AND person_code != '-' " +
            ") " +
            "SELECT COUNT(*) " +
            "FROM last_records " +
            "WHERE rn = 1",
            nativeQuery = true)
    long countCurrentPersonsWithRecords();

    /**
     * 统计人员表中有效人员的在寝人数（基于最新通行记录）
     * 严格按照业务要求：总人数从人员表，状态从记录表
     * @return 在寝人数
     */
    @Query(value = "WITH person_status AS (" +
            "    SELECT p.code, " +
            "           COALESCE(latest.inorout, 0) as last_inorout " +
            "    FROM tbl_person p " +
            "    LEFT JOIN (" +
            "        SELECT person_code, inorout, " +
            "               ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "        FROM tbl_access_control_record " +
            "        WHERE person_code IS NOT NULL AND person_code != '-' " +
            "    ) latest ON p.code = latest.person_code AND latest.rn = 1 " +
            "    WHERE p.status = 1 " +
            ") " +
            "SELECT COUNT(*) FROM person_status WHERE last_inorout = 1",
            nativeQuery = true)
    long countPersonTableReturnedPersons();

    /**
     * 统计人员表中有效人员的外出人数（基于最新通行记录）
     * @return 外出人数
     */
    @Query(value = "WITH person_status AS (" +
            "    SELECT p.code, " +
            "           COALESCE(latest.inorout, 0) as last_inorout " +
            "    FROM tbl_person p " +
            "    LEFT JOIN (" +
            "        SELECT person_code, inorout, " +
            "               ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "        FROM tbl_access_control_record " +
            "        WHERE person_code IS NOT NULL AND person_code != '-' " +
            "    ) latest ON p.code = latest.person_code AND latest.rn = 1 " +
            "    WHERE p.status = 1 " +
            ") " +
            "SELECT COUNT(*) FROM person_status WHERE last_inorout = 2",
            nativeQuery = true)
    long countPersonTableNotReturnedPersons();

    /**
     * 统计人员表中有效人员的无记录人数
     * @return 无记录人数
     */
    @Query(value = "WITH person_status AS (" +
            "    SELECT p.code, " +
            "           COALESCE(latest.inorout, 0) as last_inorout " +
            "    FROM tbl_person p " +
            "    LEFT JOIN (" +
            "        SELECT person_code, inorout, " +
            "               ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "        FROM tbl_access_control_record " +
            "        WHERE person_code IS NOT NULL AND person_code != '-' " +
            "    ) latest ON p.code = latest.person_code AND latest.rn = 1 " +
            "    WHERE p.status = 1 " +
            ") " +
            "SELECT COUNT(*) FROM person_status WHERE last_inorout = 0",
            nativeQuery = true)
    long countPersonTableNoRecordPersons();

    /**
     * 优化的统一统计查询 - 一次性获取所有人员统计数据
     * 返回结果：[总人数, 在寝人数, 外出人数, 无记录人数]
     * @return 统计结果数组
     */
    @Query(value = "WITH person_status AS (" +
            "    SELECT p.code, " +
            "           COALESCE(latest.inorout, 0) as last_inorout " +
            "    FROM tbl_person p " +
            "    LEFT JOIN (" +
            "        SELECT person_code, inorout, " +
            "               ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "        FROM tbl_access_control_record " +
            "        WHERE person_code IS NOT NULL AND person_code != '-' " +
            "    ) latest ON p.code = latest.person_code AND latest.rn = 1 " +
            "    WHERE p.status = 1 " +
            ") " +
            "SELECT " +
            "    COUNT(*) as total_persons, " +
            "    SUM(CASE WHEN last_inorout = 1 THEN 1 ELSE 0 END) as returned_persons, " +
            "    SUM(CASE WHEN last_inorout = 2 THEN 1 ELSE 0 END) as not_returned_persons, " +
            "    SUM(CASE WHEN last_inorout = 0 THEN 1 ELSE 0 END) as no_record_persons " +
            "FROM person_status",
            nativeQuery = true)
    Object[] getPersonTableStatisticsOptimized();

    /**
     * 查询所有有通行记录的人员编码（去重）
     * 用于筛选出无记录的人员
     * @return 有记录的人员编码列表
     */
    @Query(value = "SELECT DISTINCT person_code FROM tbl_access_control_record " +
            "WHERE person_code IS NOT NULL AND person_code != '-'",
            nativeQuery = true)
    List<String> findDistinctPersonCodes();

    /**
     * 高性能分页查询所有学生状态（简化版，使用主表查询）
     * 优先查询主表，性能更稳定
     * @param personName 人员姓名筛选（可选）
     * @param dormitoryStatus 状态筛选（可选：1=在寝，2=外出，0=无记录）
     * @param offset 分页偏移量
     * @param limit 分页大小
     * @return 学生状态记录列表
     */
    @Query(value = "WITH latest_records AS (" +
            "    SELECT person_code, person_name, inorout, pass_time, device_name, area_name, " +
            "           ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "    FROM tbl_access_control_record " +
            "    WHERE person_code IS NOT NULL AND person_code != '-' " +
            "), " +
            "person_status AS (" +
            "    SELECT p.code as person_code, p.name as person_name, " +
            "           COALESCE(lr.inorout, 0) as last_inorout, " +
            "           lr.pass_time as last_pass_time, " +
            "           COALESCE(lr.device_name, '无记录') as last_device_name, " +
            "           COALESCE(lr.area_name, '无记录') as last_area_name, " +
            "           CASE " +
            "               WHEN lr.inorout = 1 THEN '进入寝室' " +
            "               WHEN lr.inorout = 2 THEN '离开寝室' " +
            "               ELSE '无通行记录' " +
            "           END as last_inorout_desc, " +
            "           CASE " +
            "               WHEN lr.inorout = 1 THEN true " +
            "               ELSE false " +
            "           END as is_in_dormitory, " +
            "           CASE " +
            "               WHEN lr.inorout = 1 THEN '已归寝室' " +
            "               WHEN lr.inorout = 2 THEN '未归寝室' " +
            "               ELSE '无记录' " +
            "           END as dormitory_status_desc " +
            "    FROM tbl_person p " +
            "    LEFT JOIN latest_records lr ON p.code = lr.person_code AND lr.rn = 1 " +
            "    WHERE p.status = 1 " +
            "    AND (:personName IS NULL OR p.name ILIKE CONCAT('%', :personName, '%')) " +
            "    AND (:dormitoryStatus IS NULL OR " +
            "         (:dormitoryStatus = 0 AND lr.inorout IS NULL) OR " +
            "         (:dormitoryStatus = 1 AND lr.inorout = 1) OR " +
            "         (:dormitoryStatus = 2 AND lr.inorout = 2)) " +
            ") " +
            "SELECT person_code, person_name, last_inorout, last_pass_time, " +
            "       last_device_name, last_area_name, last_inorout_desc, " +
            "       is_in_dormitory, dormitory_status_desc " +
            "FROM person_status " +
            "ORDER BY last_pass_time DESC NULLS LAST, person_name " +
            "LIMIT :limit OFFSET :offset",
            nativeQuery = true)
    List<Object[]> findAllStudentsStatusOptimized(@Param("personName") String personName,
                                                   @Param("dormitoryStatus") Integer dormitoryStatus,
                                                   @Param("offset") int offset,
                                                   @Param("limit") int limit);

    /**
     * 统计符合条件的学生总数（用于分页）
     * @param personName 人员姓名筛选（可选）
     * @param dormitoryStatus 状态筛选（可选）
     * @return 总数
     */
    @Query(value = "WITH latest_records AS (" +
            "    SELECT person_code, inorout, " +
            "           ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn " +
            "    FROM tbl_access_control_record " +
            "    WHERE person_code IS NOT NULL AND person_code != '-' " +
            ") " +
            "SELECT COUNT(*) " +
            "FROM tbl_person p " +
            "LEFT JOIN latest_records lr ON p.code = lr.person_code AND lr.rn = 1 " +
            "WHERE p.status = 1 " +
            "AND (:personName IS NULL OR p.name ILIKE CONCAT('%', :personName, '%')) " +
            "AND (:dormitoryStatus IS NULL OR " +
            "     (:dormitoryStatus = 0 AND lr.inorout IS NULL) OR " +
            "     (:dormitoryStatus = 1 AND lr.inorout = 1) OR " +
            "     (:dormitoryStatus = 2 AND lr.inorout = 2))",
            nativeQuery = true)
    long countAllStudentsStatusOptimized(@Param("personName") String personName,
                                         @Param("dormitoryStatus") Integer dormitoryStatus);
}
