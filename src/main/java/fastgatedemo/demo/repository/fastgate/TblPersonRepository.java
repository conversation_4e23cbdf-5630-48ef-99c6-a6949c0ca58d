package fastgatedemo.demo.repository.fastgate;

import fastgatedemo.demo.model.fastgate.TblPerson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * @description FastGate数据源人员表数据访问层
 * 专门用于访问fastgate数据库中的tbl_person表
 * <AUTHOR>
 * @date 2025-01-25
 */
@Repository
@DS("fastgate")
public interface TblPersonRepository extends JpaRepository<TblPerson, Long> {
    
    /**
     * 统计有效人员数量（status = 1）
     * @return 有效人员数量
     */
    @Query("SELECT COUNT(p) FROM TblPerson p WHERE p.status = 1")
    long countActivePersons();
    
    /**
     * 统计所有人员数量
     * @return 总人员数量
     */
    @Query("SELECT COUNT(p) FROM TblPerson p")
    long countAllPersons();
    
    /**
     * 根据人员编码查询
     * @param code 人员编码
     * @return 人员信息
     */
    @Query("SELECT p FROM TblPerson p WHERE p.code = :code")
    TblPerson findByCode(@Param("code") String code);
    
    /**
     * 查询所有有效人员
     * @return 有效人员列表
     */
    @Query("SELECT p FROM TblPerson p WHERE p.status = 1")
    java.util.List<TblPerson> findActivePersons();
    
    /**
     * 根据人员类型统计有效人员数量
     * @param perType 人员类型
     * @return 人员数量
     */
    @Query("SELECT COUNT(p) FROM TblPerson p WHERE p.status = 1 AND p.perType = :perType")
    long countActivePersonsByType(@Param("perType") Integer perType);
    
    /**
     * 统计学生人数（per_type = 3）
     * @return 学生人数
     */
    @Query("SELECT COUNT(p) FROM TblPerson p WHERE p.status = 1 AND p.perType = 3")
    long countActiveStudents();
}
