package fastgatedemo.demo.repository.fastgate;

import com.baomidou.dynamic.datasource.annotation.DS;
import fastgatedemo.demo.model.fastgate.FastGatePersonInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @description FastGate数据源人员信息数据访问层
 * 专门用于访问fastgate数据库中的person_info表
 * <AUTHOR>
 * @date 2025-01-25
 */
@Repository
@DS("fastgate")
public interface FastGatePersonRepository extends JpaRepository<FastGatePersonInfo, String> {
    
    /**
     * 根据同步标志查询人员信息
     * @param syncFlag 同步标志 (0-未同步, 1-已同步)
     * @return 人员信息列表
     */
    List<FastGatePersonInfo> findBySyncFlag(Integer syncFlag);
    
    /**
     * 根据状态查询人员信息
     * @param status 状态
     * @return 人员信息列表
     */
    List<FastGatePersonInfo> findByStatus(Integer status);
    
    /**
     * 根据人员编码查询
     * @param personCode 人员编码
     * @return 人员信息
     */
    Optional<FastGatePersonInfo> findByPersonCode(String personCode);
    
    /**
     * 根据更新时间查询需要同步的人员
     * @param lastSyncTime 最后同步时间
     * @return 人员信息列表
     */
    @Query("SELECT p FROM FastGatePersonInfo p WHERE p.updateTime > :lastSyncTime")
    List<FastGatePersonInfo> findByUpdateTimeAfter(@Param("lastSyncTime") String lastSyncTime);
    
    /**
     * 查询所有未同步的人员
     * @return 未同步的人员列表
     */
    @Query("SELECT p FROM FastGatePersonInfo p WHERE p.syncFlag = 0 OR p.syncFlag IS NULL")
    List<FastGatePersonInfo> findUnsyncedPersons();
    
    /**
     * 根据部门编码查询人员
     * @param departmentCode 部门编码
     * @return 人员信息列表
     */
    List<FastGatePersonInfo> findByDepartmentCode(String departmentCode);
    
    /**
     * 根据人员类型查询
     * @param personType 人员类型 (1-学生, 2-教师, 3-其他)
     * @return 人员信息列表
     */
    List<FastGatePersonInfo> findByPersonType(Integer personType);
    
    // ========== 分页查询方法 ==========
    
    /**
     * 分页查询所有人员信息
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    Page<FastGatePersonInfo> findAll(Pageable pageable);
    
    /**
     * 根据人员姓名模糊分页查询
     * @param name 人员姓名
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    @Query("SELECT p FROM FastGatePersonInfo p WHERE p.personName LIKE %:name%")
    Page<FastGatePersonInfo> findByPersonNameContaining(@Param("name") String name, Pageable pageable);
    
    /**
     * 根据同步状态分页查询
     * @param syncFlag 同步标志
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    Page<FastGatePersonInfo> findBySyncFlag(Integer syncFlag, Pageable pageable);
    
    /**
     * 根据姓名和同步状态组合条件分页查询
     * @param name 人员姓名
     * @param syncFlag 同步标志
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    @Query("SELECT p FROM FastGatePersonInfo p WHERE p.personName LIKE %:name% AND p.syncFlag = :syncFlag")
    Page<FastGatePersonInfo> findByPersonNameContainingAndSyncFlag(@Param("name") String name, @Param("syncFlag") Integer syncFlag, Pageable pageable);
    
    // ========== 统计方法 ==========
    
    /**
     * 统计总人数
     * @return 总人数
     */
    long count();
    
    /**
     * 统计有效人员数量（status = 1）
     * @return 有效人员数量
     */
    @Query("SELECT COUNT(p) FROM FastGatePersonInfo p WHERE p.status = 1")
    long countActivePersons();
    
    /**
     * 根据同步状态统计人数
     * @param syncFlag 同步标志
     * @return 人数
     */
    long countBySyncFlag(Integer syncFlag);
    
    /**
     * 根据人员类型统计人数
     * @param personType 人员类型
     * @return 人数
     */
    long countByPersonType(Integer personType);
    
    /**
     * 根据部门统计人数
     * @param departmentCode 部门编码
     * @return 人数
     */
    long countByDepartmentCode(String departmentCode);
    
    // ========== 批量查询方法 ==========
    
    /**
     * 根据人员编码列表批量查询人员信息
     * @param personCodes 人员编码列表
     * @return 人员信息列表
     */
    List<FastGatePersonInfo> findByPersonCodeIn(List<String> personCodes);
    
    /**
     * 根据部门编码列表批量查询人员信息
     * @param departmentCodes 部门编码列表
     * @return 人员信息列表
     */
    List<FastGatePersonInfo> findByDepartmentCodeIn(List<String> departmentCodes);
    
    /**
     * 查询指定时间范围内更新的人员信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 人员信息列表
     */
    @Query("SELECT p FROM FastGatePersonInfo p WHERE p.updateTime >= :startTime AND p.updateTime <= :endTime")
    List<FastGatePersonInfo> findByUpdateTimeBetween(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
