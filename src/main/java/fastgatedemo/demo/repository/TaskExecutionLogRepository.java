package fastgatedemo.demo.repository;

import fastgatedemo.demo.model.TaskExecutionLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description 任务执行日志数据访问层
 */
@Repository
public interface TaskExecutionLogRepository extends JpaRepository<TaskExecutionLog, Long> {
    
    /**
     * 根据任务名称查询执行日志
     * @param taskName 任务名称
     * @param pageable 分页参数
     * @return 分页的执行日志
     */
    Page<TaskExecutionLog> findByTaskNameOrderByExecutionStartTimeDesc(String taskName, Pageable pageable);
    
    /**
     * 根据执行状态查询日志
     * @param status 执行状态
     * @param pageable 分页参数
     * @return 分页的执行日志
     */
    Page<TaskExecutionLog> findByExecutionStatusOrderByExecutionStartTimeDesc(String status, Pageable pageable);
    
    /**
     * 查询指定时间范围内的执行日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 分页的执行日志
     */
    Page<TaskExecutionLog> findByExecutionStartTimeBetweenOrderByExecutionStartTimeDesc(
            LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 查询任务的最近执行日志
     * @param taskName 任务名称
     * @param limit 限制数量
     * @return 最近的执行日志
     */
    @Query("SELECT t FROM TaskExecutionLog t WHERE t.taskName = :taskName ORDER BY t.executionStartTime DESC")
    List<TaskExecutionLog> findRecentExecutionLogs(@Param("taskName") String taskName, Pageable pageable);
    
    /**
     * 查询执行时间超过指定时长的日志
     * @param minDuration 最小执行时长（毫秒）
     * @param pageable 分页参数
     * @return 分页的执行日志
     */
    @Query("SELECT t FROM TaskExecutionLog t WHERE t.executionDuration >= :minDuration ORDER BY t.executionDuration DESC")
    Page<TaskExecutionLog> findLongRunningTasks(@Param("minDuration") Long minDuration, Pageable pageable);
    
    /**
     * 统计任务执行次数
     * @param taskName 任务名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行次数
     */
    @Query("SELECT COUNT(t) FROM TaskExecutionLog t WHERE t.taskName = :taskName AND t.executionStartTime BETWEEN :startTime AND :endTime")
    Long countExecutions(@Param("taskName") String taskName, 
                        @Param("startTime") LocalDateTime startTime, 
                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计任务成功执行次数
     * @param taskName 任务名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功执行次数
     */
    @Query("SELECT COUNT(t) FROM TaskExecutionLog t WHERE t.taskName = :taskName AND t.executionStatus = 'SUCCESS' AND t.executionStartTime BETWEEN :startTime AND :endTime")
    Long countSuccessfulExecutions(@Param("taskName") String taskName, 
                                  @Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计任务失败执行次数
     * @param taskName 任务名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 失败执行次数
     */
    @Query("SELECT COUNT(t) FROM TaskExecutionLog t WHERE t.taskName = :taskName AND t.executionStatus = 'FAILED' AND t.executionStartTime BETWEEN :startTime AND :endTime")
    Long countFailedExecutions(@Param("taskName") String taskName, 
                              @Param("startTime") LocalDateTime startTime, 
                              @Param("endTime") LocalDateTime endTime);
    
    /**
     * 计算任务平均执行时长
     * @param taskName 任务名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均执行时长（毫秒）
     */
    @Query("SELECT AVG(t.executionDuration) FROM TaskExecutionLog t WHERE t.taskName = :taskName AND t.executionStatus = 'SUCCESS' AND t.executionStartTime BETWEEN :startTime AND :endTime")
    Double getAverageExecutionDuration(@Param("taskName") String taskName, 
                                      @Param("startTime") LocalDateTime startTime, 
                                      @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的日志
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    @Modifying
    @Transactional
    @Query(value = "DELETE FROM task_execution_log WHERE execution_start_time < :beforeTime", nativeQuery = true)
    int deleteLogsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 删除指定任务的日志
     * @param taskName 任务名称
     * @return 删除的记录数
     */
    @Modifying
    @Transactional
    @Query(value = "DELETE FROM task_execution_log WHERE task_name = :taskName", nativeQuery = true)
    int deleteLogsByTaskName(@Param("taskName") String taskName);
    
    /**
     * 查询所有任务的执行统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Query(value = "SELECT task_name, " +
           "COUNT(*) as totalCount, " +
           "SUM(CASE WHEN execution_status = 'SUCCESS' THEN 1 ELSE 0 END) as successCount, " +
           "SUM(CASE WHEN execution_status = 'FAILED' THEN 1 ELSE 0 END) as failureCount, " +
           "AVG(execution_duration) as avgDuration " +
           "FROM task_execution_log " +
           "WHERE execution_start_time BETWEEN :startTime AND :endTime " +
           "GROUP BY task_name", nativeQuery = true)
    List<Object[]> getTaskExecutionStatistics(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询正在运行的任务
     * @return 正在运行的任务日志
     */
    List<TaskExecutionLog> findByExecutionStatusOrderByExecutionStartTimeDesc(String executionStatus);

    /**
     * 查询超时的任务（运行时间超过指定时长且状态仍为RUNNING）
     * @param timeoutTime 超时时间点
     * @return 超时的任务日志
     */
    List<TaskExecutionLog> findByExecutionStatusAndExecutionStartTimeBefore(String executionStatus, LocalDateTime timeoutTime);
}
