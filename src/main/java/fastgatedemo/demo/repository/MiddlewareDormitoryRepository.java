package fastgatedemo.demo.repository;

import fastgatedemo.demo.model.MiddlewareDormitoryInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @description 中间库宿舍信息数据访问层
 */
@Repository
public interface MiddlewareDormitoryRepository extends JpaRepository<MiddlewareDormitoryInfo, String> {
    
    /**
     * 根据宿舍编码查询
     * @param dormitoryCode 宿舍编码
     * @return 宿舍信息
     */
    Optional<MiddlewareDormitoryInfo> findByDormitoryCode(String dormitoryCode);
    
    /**
     * 根据状态查询宿舍信息
     * @param status 状态 (1-启用, 0-禁用)
     * @return 宿舍信息列表
     */
    List<MiddlewareDormitoryInfo> findByStatus(Integer status);
    
    /**
     * 根据楼栋编码查询宿舍
     * @param buildingCode 楼栋编码
     * @return 宿舍信息列表
     */
    List<MiddlewareDormitoryInfo> findByBuildingCode(String buildingCode);
    
    /**
     * 根据楼层查询宿舍
     * @param floor 楼层
     * @return 宿舍信息列表
     */
    List<MiddlewareDormitoryInfo> findByFloor(Integer floor);
    
    /**
     * 查询启用状态的宿舍
     * @return 启用状态的宿舍列表
     */
    @Query("SELECT d FROM MiddlewareDormitoryInfo d WHERE d.status = 1")
    List<MiddlewareDormitoryInfo> findActiveDormitories();
    
    /**
     * 分页查询启用状态的宿舍
     * @param pageable 分页参数
     * @return 分页的宿舍列表
     */
    @Query("SELECT d FROM MiddlewareDormitoryInfo d WHERE d.status = 1")
    Page<MiddlewareDormitoryInfo> findActiveDormitories(Pageable pageable);
    
    /**
     * 根据楼栋编码和楼层查询
     * @param buildingCode 楼栋编码
     * @param floor 楼层
     * @return 宿舍信息列表
     */
    @Query("SELECT d FROM MiddlewareDormitoryInfo d WHERE d.buildingCode = :buildingCode AND d.floor = :floor AND d.status = 1")
    List<MiddlewareDormitoryInfo> findByBuildingCodeAndFloor(@Param("buildingCode") String buildingCode, 
                                                              @Param("floor") Integer floor);
    
    /**
     * 根据房间名称模糊查询
     * @param roomName 房间名称
     * @return 宿舍信息列表
     */
    @Query("SELECT d FROM MiddlewareDormitoryInfo d WHERE d.roomName LIKE %:roomName% AND d.status = 1")
    List<MiddlewareDormitoryInfo> findByRoomNameContaining(@Param("roomName") String roomName);
    
    /**
     * 统计启用状态的宿舍总数
     * @return 启用状态的宿舍总数
     */
    @Query("SELECT COUNT(d) FROM MiddlewareDormitoryInfo d WHERE d.status = 1")
    Long countActiveDormitories();
    
    /**
     * 根据楼栋编码统计宿舍数
     * @param buildingCode 楼栋编码
     * @return 该楼栋的宿舍数
     */
    @Query("SELECT COUNT(d) FROM MiddlewareDormitoryInfo d WHERE d.buildingCode = :buildingCode AND d.status = 1")
    Long countByBuildingCode(@Param("buildingCode") String buildingCode);
} 