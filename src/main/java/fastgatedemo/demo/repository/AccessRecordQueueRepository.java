package fastgatedemo.demo.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import fastgatedemo.demo.model.AccessRecordQueue;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description 异步写入队列Repository
 * 使用主数据源存储待处理的通行记录
 * <AUTHOR>
 * @date 2025-01-29
 */
@Repository
@DS("master")
public interface AccessRecordQueueRepository extends JpaRepository<AccessRecordQueue, Long> {

    /**
     * 查询待处理的记录
     * @param pageable 分页参数
     * @return 待处理记录列表
     */
    @Query("SELECT q FROM AccessRecordQueue q WHERE q.processStatus = 0 ORDER BY q.createTime")
    List<AccessRecordQueue> findPendingRecords(Pageable pageable);

    /**
     * 查询需要重试的失败记录
     * @param maxRetries 最大重试次数
     * @param retryAfter 重试间隔时间点
     * @param pageable 分页参数
     * @return 需要重试的记录列表
     */
    @Query("SELECT q FROM AccessRecordQueue q WHERE q.processStatus = 2 " +
           "AND q.retryCount < :maxRetries " +
           "AND q.processTime < :retryAfter " +
           "ORDER BY q.createTime")
    List<AccessRecordQueue> findRetryRecords(@Param("maxRetries") int maxRetries, 
                                           @Param("retryAfter") LocalDateTime retryAfter,
                                           Pageable pageable);

    /**
     * 统计待处理记录数量
     * @return 待处理记录数量
     */
    @Query("SELECT COUNT(q) FROM AccessRecordQueue q WHERE q.processStatus = 0")
    long countPendingRecords();

    /**
     * 统计失败记录数量
     * @return 失败记录数量
     */
    @Query("SELECT COUNT(q) FROM AccessRecordQueue q WHERE q.processStatus = 2")
    long countFailedRecords();

    /**
     * 统计今日处理记录数量
     * @param startOfDay 今日开始时间
     * @return 今日处理记录数量
     */
    @Query("SELECT COUNT(q) FROM AccessRecordQueue q WHERE q.processStatus = 1 " +
           "AND q.processTime >= :startOfDay")
    long countTodayProcessedRecords(@Param("startOfDay") LocalDateTime startOfDay);

    /**
     * 删除指定时间之前的已处理记录
     * @param beforeTime 指定时间
     * @return 删除的记录数量
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM AccessRecordQueue q WHERE q.processStatus = 1 " +
           "AND q.processTime < :beforeTime")
    int deleteProcessedRecordsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 删除超过最大重试次数的失败记录
     * @param maxRetries 最大重试次数
     * @return 删除的记录数量
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM AccessRecordQueue q WHERE q.processStatus = 2 " +
           "AND q.retryCount >= :maxRetries")
    int deleteFailedRecordsOverMaxRetries(@Param("maxRetries") int maxRetries);

    /**
     * 批量更新记录状态为处理成功
     * @param ids 记录ID列表
     * @return 更新的记录数量
     */
    @Modifying
    @Transactional
    @Query("UPDATE AccessRecordQueue q SET q.processStatus = 1, q.processTime = CURRENT_TIMESTAMP " +
           "WHERE q.id IN :ids")
    int batchMarkAsProcessed(@Param("ids") List<Long> ids);

    /**
     * 批量更新记录状态为处理失败
     * @param ids 记录ID列表
     * @param errorMessage 错误信息
     * @return 更新的记录数量
     */
    @Modifying
    @Transactional
    @Query("UPDATE AccessRecordQueue q SET q.processStatus = 2, q.processTime = CURRENT_TIMESTAMP, " +
           "q.errorMessage = :errorMessage, q.retryCount = q.retryCount + 1 " +
           "WHERE q.id IN :ids")
    int batchMarkAsFailed(@Param("ids") List<Long> ids, @Param("errorMessage") String errorMessage);

    /**
     * 根据人员编码和时间范围查询记录
     * @param personCode 人员编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录列表
     */
    @Query("SELECT q FROM AccessRecordQueue q WHERE q.personCode = :personCode " +
           "AND q.passTime BETWEEN :startTime AND :endTime " +
           "ORDER BY q.passTime DESC")
    List<AccessRecordQueue> findByPersonCodeAndTimeRange(@Param("personCode") String personCode,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取队列统计信息
     * @return 统计信息数组 [待处理数量, 失败数量, 今日已处理数量]
     */
    @Query(value = "SELECT " +
           "SUM(CASE WHEN process_status = 0 THEN 1 ELSE 0 END) as pending_count, " +
           "SUM(CASE WHEN process_status = 2 THEN 1 ELSE 0 END) as failed_count, " +
           "SUM(CASE WHEN process_status = 1 AND DATE(process_time) = CURRENT_DATE THEN 1 ELSE 0 END) as today_processed " +
           "FROM access_record_queue",
           nativeQuery = true)
    Object[] getQueueStatistics();
}