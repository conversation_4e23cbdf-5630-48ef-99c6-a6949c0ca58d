package fastgatedemo.demo.repository;

import fastgatedemo.demo.model.ScheduledTaskInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @description 定时任务信息数据访问层
 */
@Repository
public interface ScheduledTaskInfoRepository extends JpaRepository<ScheduledTaskInfo, Long> {
    
    /**
     * 根据任务名称查询
     * @param taskName 任务名称
     * @return 任务信息
     */
    Optional<ScheduledTaskInfo> findByTaskName(String taskName);
    
    /**
     * 查询所有启用的任务
     * @return 启用的任务列表
     */
    List<ScheduledTaskInfo> findByEnabledTrue();
    
    /**
     * 查询所有禁用的任务
     * @return 禁用的任务列表
     */
    List<ScheduledTaskInfo> findByEnabledFalse();
    
    /**
     * 根据任务类查询
     * @param taskClass 任务类名
     * @return 任务列表
     */
    List<ScheduledTaskInfo> findByTaskClass(String taskClass);
    
    /**
     * 根据最后执行状态查询
     * @param status 执行状态
     * @return 任务列表
     */
    List<ScheduledTaskInfo> findByLastExecutionStatus(String status);
    
    /**
     * 查询长时间未执行的任务
     * @param beforeTime 时间点
     * @return 任务列表
     */
    @Query("SELECT t FROM ScheduledTaskInfo t WHERE t.enabled = true AND (t.lastExecutionTime IS NULL OR t.lastExecutionTime < :beforeTime)")
    List<ScheduledTaskInfo> findTasksNotExecutedSince(@Param("beforeTime") LocalDateTime beforeTime);
    
    /**
     * 查询执行失败次数较多的任务
     * @param minFailureCount 最小失败次数
     * @return 任务列表
     */
    @Query("SELECT t FROM ScheduledTaskInfo t WHERE t.failureCount >= :minFailureCount ORDER BY t.failureCount DESC")
    List<ScheduledTaskInfo> findTasksWithHighFailureCount(@Param("minFailureCount") Long minFailureCount);
    
    /**
     * 更新任务执行统计信息
     * @param taskName 任务名称
     * @param lastExecutionTime 最后执行时间
     * @param status 执行状态
     * @param duration 执行时长
     * @param errorMessage 错误信息
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE scheduled_task_info SET " +
           "last_execution_time = :lastExecutionTime, " +
           "last_execution_status = :status, " +
           "last_execution_duration = :duration, " +
           "last_error_message = :errorMessage, " +
           "execution_count = execution_count + 1, " +
           "success_count = CASE WHEN :status = 'SUCCESS' THEN success_count + 1 ELSE success_count END, " +
           "failure_count = CASE WHEN :status = 'FAILED' THEN failure_count + 1 ELSE failure_count END, " +
           "update_time = CURRENT_TIMESTAMP " +
           "WHERE task_name = :taskName", nativeQuery = true)
    int updateTaskExecutionInfo(@Param("taskName") String taskName,
                               @Param("lastExecutionTime") LocalDateTime lastExecutionTime,
                               @Param("status") String status,
                               @Param("duration") Long duration,
                               @Param("errorMessage") String errorMessage);
    
    /**
     * 启用/禁用任务
     * @param taskName 任务名称
     * @param enabled 是否启用
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE scheduled_task_info SET enabled = :enabled, update_time = CURRENT_TIMESTAMP WHERE task_name = :taskName", nativeQuery = true)
    int updateTaskEnabled(@Param("taskName") String taskName, @Param("enabled") Boolean enabled);
    
    /**
     * 重置任务统计信息
     * @param taskName 任务名称
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE scheduled_task_info SET " +
           "execution_count = 0, " +
           "success_count = 0, " +
           "failure_count = 0, " +
           "last_error_message = NULL, " +
           "update_time = CURRENT_TIMESTAMP " +
           "WHERE task_name = :taskName", nativeQuery = true)
    int resetTaskStatistics(@Param("taskName") String taskName);
    
    /**
     * 分页查询任务信息
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<ScheduledTaskInfo> findAll(Pageable pageable);
    
    /**
     * 根据启用状态分页查询
     * @param enabled 是否启用
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<ScheduledTaskInfo> findByEnabled(Boolean enabled, Pageable pageable);
    
    /**
     * 统计任务数量
     * @return 总任务数
     */
    @Query(value = "SELECT COUNT(*) FROM scheduled_task_info", nativeQuery = true)
    Long countAllTasks();

    /**
     * 统计启用的任务数量
     * @return 启用任务数
     */
    @Query(value = "SELECT COUNT(*) FROM scheduled_task_info WHERE enabled = true", nativeQuery = true)
    Long countEnabledTasks();

    /**
     * 统计正在运行的任务数量
     * @return 运行中任务数
     */
    @Query(value = "SELECT COUNT(*) FROM scheduled_task_info WHERE last_execution_status = 'RUNNING'", nativeQuery = true)
    Long countRunningTasks();
}
