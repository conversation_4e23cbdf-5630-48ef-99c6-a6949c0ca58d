package fastgatedemo.demo.repository;

import fastgatedemo.demo.model.MiddlewarePersonInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @description 中间库人员信息数据访问层
 */
@Repository
public interface MiddlewarePersonRepository extends JpaRepository<MiddlewarePersonInfo, Long> {
    
    /**
     * 根据人员编码查询
     * @param perCode 人员编码
     * @return 人员信息
     */
    Optional<MiddlewarePersonInfo> findByPerCode(String perCode);
    
    /**
     * 根据学号/工号查询
     * @param accNum 学号/工号
     * @return 人员信息
     */
    Optional<MiddlewarePersonInfo> findByAccNum(String accNum);
    
    /**
     * 根据状态查询人员信息
     * @param status 状态 (1-启用, 0-禁用)
     * @return 人员信息列表
     */
    List<MiddlewarePersonInfo> findByStatus(Integer status);
    
    /**
     * 根据人员类型查询
     * @param perType 人员类型 (3-学生, 5-教职工)
     * @return 人员信息列表
     */
    List<MiddlewarePersonInfo> findByPerType(Integer perType);
    
    /**
     * 根据部门编码查询人员
     * @param departmentCode 部门编码
     * @return 人员信息列表
     */
    List<MiddlewarePersonInfo> findByDepartmentCode(String departmentCode);
    
    /**
     * 查询启用状态的人员
     * @return 启用状态的人员列表
     */
    @Query("SELECT p FROM MiddlewarePersonInfo p WHERE p.status = 1")
    List<MiddlewarePersonInfo> findActivePersons();
    
    /**
     * 分页查询启用状态的人员
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    @Query("SELECT p FROM MiddlewarePersonInfo p WHERE p.status = 1")
    Page<MiddlewarePersonInfo> findActivePersons(Pageable pageable);
    
    /**
     * 根据人员类型分页查询
     * @param perType 人员类型
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    @Query("SELECT p FROM MiddlewarePersonInfo p WHERE p.perType = :perType AND p.status = 1")
    Page<MiddlewarePersonInfo> findByPerType(@Param("perType") Integer perType, Pageable pageable);
    
    /**
     * 根据部门编码分页查询
     * @param departmentCode 部门编码
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    @Query("SELECT p FROM MiddlewarePersonInfo p WHERE p.departmentCode = :departmentCode AND p.status = 1")
    Page<MiddlewarePersonInfo> findByDepartmentCode(@Param("departmentCode") String departmentCode, Pageable pageable);
    
    /**
     * 根据姓名模糊分页查询
     * @param name 姓名
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    @Query("SELECT p FROM MiddlewarePersonInfo p WHERE p.accName LIKE %:name% AND p.status = 1")
    Page<MiddlewarePersonInfo> findByNameContaining(@Param("name") String name, Pageable pageable);
    
    /**
     * 查询学生人员
     * @return 学生人员列表
     */
    @Query("SELECT p FROM MiddlewarePersonInfo p WHERE p.perType = 3 AND p.status = 1")
    List<MiddlewarePersonInfo> findActiveStudents();
    
    /**
     * 查询教职工人员
     * @return 教职工人员列表
     */
    @Query("SELECT p FROM MiddlewarePersonInfo p WHERE p.perType = 5 AND p.status = 1")
    List<MiddlewarePersonInfo> findActiveStaff();
    
    /**
     * 根据姓名模糊查询
     * @param name 姓名
     * @return 人员信息列表
     */
    @Query("SELECT p FROM MiddlewarePersonInfo p WHERE p.accName LIKE %:name%")
    List<MiddlewarePersonInfo> findByNameContaining(@Param("name") String name);
    
    /**
     * 统计总人数
     * @return 总人数
     */
    @Query("SELECT COUNT(p) FROM MiddlewarePersonInfo p WHERE p.status = 1")
    Long countActivePersons();
    
    /**
     * 统计学生人数
     * @return 学生人数
     */
    @Query("SELECT COUNT(p) FROM MiddlewarePersonInfo p WHERE p.perType = 3 AND p.status = 1")
    Long countActiveStudents();
    
    /**
     * 统计教职工人数
     * @return 教职工人数
     */
    @Query("SELECT COUNT(p) FROM MiddlewarePersonInfo p WHERE p.perType = 5 AND p.status = 1")
    Long countActiveStaff();
    
    /**
     * 根据工号列表批量查询人员信息
     * @param accNums 工号列表
     * @return 人员信息列表
     */
    List<MiddlewarePersonInfo> findByAccNumIn(List<String> accNums);
} 