package fastgatedemo.demo.repository;

import fastgatedemo.demo.model.PersonInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * @description 人员信息数据访问层
 */
@Repository
public interface PersonRepository extends JpaRepository<PersonInfo, String> {
    
    /**
     * 根据同步标志查询人员信息
     * @param syncFlag 同步标志 (0-未同步, 1-已同步)
     * @return 人员信息列表
     */
    List<PersonInfo> findBySyncFlag(Integer syncFlag);
    
    /**
     * 根据状态查询人员信息
     * @param status 状态
     * @return 人员信息列表
     */
    List<PersonInfo> findByStatus(Integer status);
    
    /**
     * 根据人员编码查询
     * @param personCode 人员编码
     * @return 人员信息
     */
    Optional<PersonInfo> findByPersonCode(String personCode);
    
    /**
     * 根据更新时间查询需要同步的人员
     * @param lastSyncTime 最后同步时间
     * @return 人员信息列表
     */
    @Query("SELECT p FROM PersonInfo p WHERE p.updateTime > :lastSyncTime")
    List<PersonInfo> findByUpdateTimeAfter(@Param("lastSyncTime") String lastSyncTime);
    
    /**
     * 查询所有未同步的人员
     * @return 未同步的人员列表
     */
    @Query("SELECT p FROM PersonInfo p WHERE p.syncFlag = 0 OR p.syncFlag IS NULL")
    List<PersonInfo> findUnsyncedPersons();
    
    /**
     * 根据部门编码查询人员
     * @param departmentCode 部门编码
     * @return 人员信息列表
     */
    List<PersonInfo> findByDepartmentCode(String departmentCode);
    
    // ========== 新增分页查询方法 ==========
    
    /**
     * 分页查询所有人员信息
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    Page<PersonInfo> findAll(Pageable pageable);
    
    /**
     * 根据人员姓名模糊分页查询
     * @param name 人员姓名
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    @Query("SELECT p FROM PersonInfo p WHERE p.personName LIKE %:name%")
    Page<PersonInfo> findByPersonNameContaining(@Param("name") String name, Pageable pageable);
    
    /**
     * 根据同步状态分页查询
     * @param syncFlag 同步标志
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    Page<PersonInfo> findBySyncFlag(Integer syncFlag, Pageable pageable);
    
    /**
     * 根据姓名和同步状态组合条件分页查询
     * @param name 人员姓名
     * @param syncFlag 同步标志
     * @param pageable 分页参数
     * @return 分页的人员列表
     */
    @Query("SELECT p FROM PersonInfo p WHERE p.personName LIKE %:name% AND p.syncFlag = :syncFlag")
    Page<PersonInfo> findByPersonNameContainingAndSyncFlag(@Param("name") String name, @Param("syncFlag") Integer syncFlag, Pageable pageable);
    
    /**
     * 统计总人数
     * @return 总人数
     */
    long count();
    
    /**
     * 根据同步状态统计人数
     * @param syncFlag 同步标志
     * @return 人数
     */
    long countBySyncFlag(Integer syncFlag);
    
    /**
     * 根据人员编码列表批量查询人员信息
     * @param personCodes 人员编码列表
     * @return 人员信息列表
     */
    List<PersonInfo> findByPersonCodeIn(List<String> personCodes);
    
    /**
     * 根据工号列表批量查询人员信息（用于Excel导入匹配）
     * @param jobNumbers 工号列表（对应person_code字段）
     * @return 人员信息列表
     */
    @Query("SELECT p FROM PersonInfo p WHERE p.personCode IN :jobNumbers")
    List<PersonInfo> findByJobNumberIn(@Param("jobNumbers") List<String> jobNumbers);
    
    /**
     * 查询所有已分配宿舍的学生编码
     * @return 学生编码列表
     */
    @Query(value = "SELECT DISTINCT pdr.person_code " +
                   "FROM person_dormitory_relation pdr " +
                   "JOIN person_info p ON pdr.person_code = p.person_code " +
                   "WHERE pdr.status = 1 AND p.status = 1 " +
                   "ORDER BY pdr.person_code", nativeQuery = true)
    List<String> findAssignedDormitoryStudentCodes();
} 