package fastgatedemo.demo.repository;

import fastgatedemo.demo.model.MiddlewareFacePhoto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description 人脸照片数据访问层
 */
@Repository
public interface FacePhotoRepository extends JpaRepository<MiddlewareFacePhoto, String> {
    
    /**
     * 根据人员编码查找人脸照片
     * @param perCode 人员编码
     * @return 人脸照片列表
     */
    List<MiddlewareFacePhoto> findByPerCode(String perCode);
    
    /**
     * 查询已同步人员但人脸照片存在同步问题的记录
     * @param pageable 分页参数
     * @return 问题记录页面
     */
    @Query(value = "SELECT " +
           "p.person_code, " +
           "p.person_name, " +
           "p.department_code, " +
           "p.telephone, " +
           "p.idcard, " +
           "p.sync_flag as person_sync_flag, " +
           "f.id as face_photo_id, " +
           "f.sync_flag as face_sync_flag, " +
           "CASE " +
           "    WHEN f.per_code IS NULL THEN '无人脸照片记录' " +
           "    WHEN f.sync_flag IS NULL THEN '人脸照片同步标识为空' " +
           "    WHEN f.sync_flag != 1 THEN '人脸照片未同步' " +
           "    ELSE '正常' " +
           "END as issue_type, " +
           "CASE " +
           "    WHEN f.per_code IS NULL THEN '该人员已同步但没有对应的人脸照片记录' " +
           "    WHEN f.sync_flag IS NULL THEN '该人员的人脸照片记录存在但同步标识为空' " +
           "    WHEN f.sync_flag != 1 THEN '该人员的人脸照片记录存在但未标记为已同步' " +
           "    ELSE '正常状态' " +
           "END as issue_description " +
           "FROM person_info p " +
           "LEFT JOIN face_photo f ON p.person_code = f.per_code " +
           "WHERE p.sync_flag = 1 " +
           "AND (f.per_code IS NULL OR f.sync_flag IS NULL OR f.sync_flag != 1) " +
           "ORDER BY p.person_code",
           countQuery = "SELECT COUNT(*) " +
                       "FROM person_info p " +
                       "LEFT JOIN face_photo f ON p.person_code = f.per_code " +
                       "WHERE p.sync_flag = 1 " +
                       "AND (f.per_code IS NULL OR f.sync_flag IS NULL OR f.sync_flag != 1)",
           nativeQuery = true)
    Page<Object[]> findPersonsWithFacePhotoIssues(Pageable pageable);
    
    /**
     * 统计人脸照片同步问题数量
     * @return 问题记录总数
     */
    @Query(value = "SELECT COUNT(*) " +
           "FROM person_info p " +
           "LEFT JOIN face_photo f ON p.person_code = f.per_code " +
           "WHERE p.sync_flag = 1 " +
           "AND (f.per_code IS NULL OR f.sync_flag IS NULL OR f.sync_flag != 1)",
           nativeQuery = true)
    Long countPersonsWithFacePhotoIssues();
    
    /**
     * 根据同步标识查找人脸照片
     * @param syncFlag 同步标识
     * @return 人脸照片列表
     */
    List<MiddlewareFacePhoto> findBySyncFlag(Integer syncFlag);
    
    /**
     * 根据同步标识为空查找人脸照片
     * @return 人脸照片列表
     */
    @Query("SELECT f FROM MiddlewareFacePhoto f WHERE f.syncFlag IS NULL")
    List<MiddlewareFacePhoto> findBySyncFlagIsNull();

    /**
     * 查询需要修复的人脸照片记录（仅包含有人脸照片但同步标识不为1的记录）
     * @param pageable 分页参数
     * @return 需要修复的记录页面
     */
    @Query(value = "SELECT " +
           "p.person_code, " +
           "p.person_name, " +
           "p.department_code, " +
           "p.telephone, " +
           "p.idcard, " +
           "p.sync_flag as person_sync_flag, " +
           "f.id as face_photo_id, " +
           "f.sync_flag as face_sync_flag, " +
           "CASE " +
           "    WHEN f.sync_flag IS NULL THEN '人脸照片同步标识为空' " +
           "    WHEN f.sync_flag != 1 THEN '人脸照片未同步' " +
           "    ELSE '正常' " +
           "END as issue_type, " +
           "CASE " +
           "    WHEN f.sync_flag IS NULL THEN '该人员的人脸照片记录存在但同步标识为空' " +
           "    WHEN f.sync_flag != 1 THEN '该人员的人脸照片记录存在但未标记为已同步' " +
           "    ELSE '正常状态' " +
           "END as issue_description " +
           "FROM person_info p " +
           "INNER JOIN face_photo f ON p.person_code = f.per_code " +
           "WHERE p.sync_flag = 1 " +
           "AND (f.sync_flag IS NULL OR f.sync_flag != 1) " +
           "ORDER BY p.person_code",
           countQuery = "SELECT COUNT(*) " +
                       "FROM person_info p " +
                       "INNER JOIN face_photo f ON p.person_code = f.per_code " +
                       "WHERE p.sync_flag = 1 " +
                       "AND (f.sync_flag IS NULL OR f.sync_flag != 1)",
           nativeQuery = true)
    Page<Object[]> findPersonsNeedingFacePhotoSync(Pageable pageable);

    /**
     * 统计需要修复的人脸照片记录数量（仅包含有人脸照片但同步标识不为1的记录）
     * @return 需要修复的记录总数
     */
    @Query(value = "SELECT COUNT(*) " +
           "FROM person_info p " +
           "INNER JOIN face_photo f ON p.person_code = f.per_code " +
           "WHERE p.sync_flag = 1 " +
           "AND (f.sync_flag IS NULL OR f.sync_flag != 1)",
           nativeQuery = true)
    Long countPersonsNeedingFacePhotoSync();

    /**
     * 获取所有需要修复的人员编码和姓名列表（避免分页修改数据的问题）
     * @return 需要修复的人员信息列表 [person_code, person_name]
     */
    @Query(value = "SELECT p.person_code, p.person_name " +
           "FROM person_info p " +
           "INNER JOIN face_photo f ON p.person_code = f.per_code " +
           "WHERE p.sync_flag = 1 " +
           "AND (f.sync_flag IS NULL OR f.sync_flag != 1) " +
           "ORDER BY p.person_code",
           nativeQuery = true)
    List<Object[]> findAllPersonsNeedingFacePhotoSync();

    /**
     * 根据人员编码更新人脸照片同步标识
     * @param perCode 人员编码
     * @param syncFlag 同步标识
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE face_photo SET sync_flag = :syncFlag WHERE per_code = :perCode", nativeQuery = true)
    void updateSyncFlagByPerCode(@Param("perCode") String perCode, @Param("syncFlag") Integer syncFlag);
} 