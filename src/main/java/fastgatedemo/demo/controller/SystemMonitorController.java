package fastgatedemo.demo.controller;

import fastgatedemo.demo.service.DatabasePressureMonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @description 系统监控管理控制器
 * 提供数据库压力监控、性能统计等管理接口
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/api/monitor")
public class SystemMonitorController {

    private static final Logger logger = LoggerFactory.getLogger(SystemMonitorController.class);

    @Autowired
    private DatabasePressureMonitorService dbMonitorService;

    /**
     * 获取数据库监控统计信息
     * @return 监控统计
     */
    @GetMapping("/database/statistics")
    public ResponseEntity<Map<String, Object>> getDatabaseStatistics() {
        try {
            Map<String, Object> stats = dbMonitorService.getMonitoringStatistics();
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            logger.error("获取数据库监控统计失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取监控统计失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 获取数据库详细信息
     * @return 数据库详细信息
     */
    @GetMapping("/database/details")
    public ResponseEntity<Map<String, Object>> getDatabaseDetails() {
        try {
            Map<String, Object> details = dbMonitorService.getDatabaseDetails();
            return ResponseEntity.ok(details);
            
        } catch (Exception e) {
            logger.error("获取数据库详细信息失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取数据库详细信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 检查数据库压力状态
     * @return 压力状态
     */
    @GetMapping("/database/pressure")
    public ResponseEntity<Map<String, Object>> getDatabasePressure() {
        try {
            Map<String, Object> pressure = new HashMap<>();
            pressure.put("isUnderPressure", dbMonitorService.isUnderPressure());
            pressure.put("currentTPS", dbMonitorService.getCurrentTPS());
            pressure.put("currentResponseTime", dbMonitorService.getCurrentResponseTime());
            pressure.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(pressure);
            
        } catch (Exception e) {
            logger.error("获取数据库压力状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取压力状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 动态调整监控阈值
     * @param maxConnections 最大连接数
     * @param maxTPS 最大TPS
     * @param maxResponseTime 最大响应时间（毫秒）
     * @return 调整结果
     */
    @PostMapping("/database/thresholds")
    public ResponseEntity<Map<String, Object>> updateThresholds(
            @RequestParam(required = false) Integer maxConnections,
            @RequestParam(required = false) Integer maxTPS,
            @RequestParam(required = false) Long maxResponseTime) {
        
        try {
            dbMonitorService.updateThresholds(maxConnections, maxTPS, maxResponseTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "监控阈值更新成功");
            result.put("maxConnections", maxConnections);
            result.put("maxTPS", maxTPS);
            result.put("maxResponseTime", maxResponseTime);
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("监控阈值更新: maxConnections={}, maxTPS={}, maxResponseTime={}ms", 
                       maxConnections, maxTPS, maxResponseTime);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("更新监控阈值失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "更新监控阈值失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 启用/禁用数据库监控
     * @param enabled 是否启用
     * @return 操作结果
     */
    @PostMapping("/database/toggle")
    public ResponseEntity<Map<String, Object>> toggleDatabaseMonitor(@RequestParam boolean enabled) {
        try {
            dbMonitorService.setMonitorEnabled(enabled);
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "数据库监控状态已调整为: " + (enabled ? "启用" : "禁用"));
            result.put("enabled", enabled);
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("数据库监控状态调整: {}", enabled);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("调整数据库监控状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "调整监控状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 重置监控统计信息
     * @return 重置结果
     */
    @PostMapping("/database/reset")
    public ResponseEntity<Map<String, Object>> resetMonitoringStatistics() {
        try {
            dbMonitorService.resetStatistics();
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "监控统计信息已重置");
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("监控统计信息已重置");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("重置监控统计失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "重置监控统计失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 获取系统整体健康状态
     * @return 系统健康状态
     */
    @GetMapping("/health/overview")
    public ResponseEntity<Map<String, Object>> getSystemHealthOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();
            
            // 数据库健康状态
            Map<String, Object> dbStats = dbMonitorService.getMonitoringStatistics();
            boolean dbHealthy = !(Boolean) dbStats.getOrDefault("isUnderPressure", false);
            overview.put("databaseHealthy", dbHealthy);
            overview.put("databaseStats", dbStats);
            
            // JVM内存信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> jvmStats = new HashMap<>();
            jvmStats.put("totalMemory", runtime.totalMemory());
            jvmStats.put("freeMemory", runtime.freeMemory());
            jvmStats.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            jvmStats.put("maxMemory", runtime.maxMemory());
            
            double memoryUsageRate = (double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.maxMemory() * 100;
            boolean jvmHealthy = memoryUsageRate < 85; // 内存使用率低于85%认为健康
            jvmStats.put("memoryUsageRate", Math.round(memoryUsageRate * 100.0) / 100.0);
            overview.put("jvmHealthy", jvmHealthy);
            overview.put("jvmStats", jvmStats);
            
            // 系统整体健康状态
            boolean overallHealthy = dbHealthy && jvmHealthy;
            overview.put("overallHealthy", overallHealthy);
            overview.put("timestamp", System.currentTimeMillis());
            
            // 线程信息
            ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
            while (rootGroup.getParent() != null) {
                rootGroup = rootGroup.getParent();
            }
            overview.put("activeThreads", rootGroup.activeCount());
            
            return ResponseEntity.ok(overview);
            
        } catch (Exception e) {
            logger.error("获取系统健康概览失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("overallHealthy", false);
            error.put("error", "获取系统健康概览失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 获取系统性能指标
     * @return 性能指标
     */
    @GetMapping("/performance/metrics")
    public ResponseEntity<Map<String, Object>> getPerformanceMetrics() {
        try {
            Map<String, Object> metrics = new HashMap<>();
            
            // 数据库性能指标
            Map<String, Object> dbStats = dbMonitorService.getMonitoringStatistics();
            metrics.put("database", dbStats);
            
            // JVM性能指标
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> jvm = new HashMap<>();
            jvm.put("processors", runtime.availableProcessors());
            jvm.put("totalMemoryMB", runtime.totalMemory() / 1024 / 1024);
            jvm.put("freeMemoryMB", runtime.freeMemory() / 1024 / 1024);
            jvm.put("usedMemoryMB", (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024);
            jvm.put("maxMemoryMB", runtime.maxMemory() / 1024 / 1024);
            metrics.put("jvm", jvm);
            
            // 系统启动时间
            long uptime = java.lang.management.ManagementFactory.getRuntimeMXBean().getUptime();
            metrics.put("uptimeMs", uptime);
            metrics.put("uptimeHours", Math.round(uptime / 1000.0 / 3600.0 * 100.0) / 100.0);
            
            metrics.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(metrics);
            
        } catch (Exception e) {
            logger.error("获取性能指标失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取性能指标失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 手动触发垃圾回收
     * @return 操作结果
     */
    @PostMapping("/jvm/gc")
    public ResponseEntity<Map<String, Object>> triggerGarbageCollection() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long beforeGC = runtime.totalMemory() - runtime.freeMemory();
            
            System.gc();
            
            // 等待一下让GC完成
            Thread.sleep(1000);
            
            long afterGC = runtime.totalMemory() - runtime.freeMemory();
            long released = beforeGC - afterGC;
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "垃圾回收已触发");
            result.put("memoryBeforeGC", beforeGC);
            result.put("memoryAfterGC", afterGC);
            result.put("memoryReleased", released);
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("手动触发垃圾回收，释放内存: {} bytes", released);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("触发垃圾回收失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "触发垃圾回收失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
}