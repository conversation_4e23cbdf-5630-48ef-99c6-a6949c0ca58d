package fastgatedemo.demo.controller;

import com.alibaba.fastjson.JSON;
import fastgatedemo.demo.model.GaDevice;
import fastgatedemo.demo.model.ResponseStatus;
import fastgatedemo.demo.service.ViewMessageProcessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 接收速通门视图化转发
 */
@RestController
public class ViewForwardController {

    private static final Logger logger = LoggerFactory.getLogger(ViewForwardController.class);

    @Autowired
    private ViewMessageProcessService messageProcessService;

    /**
     * 注册接口
     */
    @PostMapping(value = "/VIID/System/Register")
    public ResponseStatus register(HttpServletRequest request, @RequestBody GaDevice gaDevice) {
        String host = request.getRemoteHost();
        String addr = request.getLocalAddr();
        logger.info(host + " register " + addr);
        logger.info(JSON.toJSONString(gaDevice));
        return new ResponseStatus("/VIID/System/Register");
    }

    /**
     * 保活接口
     */
    @PostMapping(value = "/VIID/System/Keepalive")
    public ResponseStatus keepAlive(HttpServletRequest request, @RequestBody GaDevice gaDevice) {
        String host = request.getRemoteHost();
        String addr = request.getLocalAddr();
        logger.info(host + " keepAlive " + addr);
        logger.info(JSON.toJSONString(gaDevice));
        return new ResponseStatus("/VIID/System/Keepalive");
    }

    /**
     * 通知接受接口
     */
    @PostMapping(value = "/VIID/SubscribeNotifications")
    public ResponseStatus notifications(HttpServletRequest request,
                                        @RequestBody String strSubscribeNotification) {

        logger.info("=================================收到消息=======================================");
        logger.info(strSubscribeNotification);
        
        String host = request.getRemoteHost();
        logger.info(host + " notifications");
        
        boolean processResult = false;
        String responseMessage = "处理成功";
        int statusCode = 0;
        
        try {
            processResult = messageProcessService.processMessage(strSubscribeNotification);
            if (processResult) {
                logger.info("消息处理完成");
            } else {
                logger.warn("消息处理失败或跳过");
                responseMessage = "消息处理失败或跳过";
            }
        } catch (Exception e) {
            logger.error("处理消息时发生错误: {}", e.getMessage(), e);
            responseMessage = "处理失败: " + e.getMessage();
            statusCode = 1;
        }
        
        logger.info("=================================处理完成=======================================");
        
        ResponseStatus responseStatus = new ResponseStatus("/VIID/SubscribeNotifications",
                statusCode, responseMessage);

        return responseStatus;
    }
}