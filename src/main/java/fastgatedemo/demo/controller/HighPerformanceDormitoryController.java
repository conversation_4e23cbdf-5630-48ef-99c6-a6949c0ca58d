package fastgatedemo.demo.controller;

import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.service.AsyncBatchPersistenceService;
import fastgatedemo.demo.service.CachedDormitoryStatsService;
import fastgatedemo.demo.service.StudentStatusCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @description 高性能寝室状态管理控制器
 * 基于Redis缓存的高性能查询接口
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/api/v2/dormitory")
public class HighPerformanceDormitoryController {

    private static final Logger logger = LoggerFactory.getLogger(HighPerformanceDormitoryController.class);

    @Autowired
    private CachedDormitoryStatsService cachedStatsService;

    @Autowired
    private StudentStatusCacheService statusCacheService;

    @Autowired
    private AsyncBatchPersistenceService persistenceService;

    /**
     * 获取寝室归宿统计信息（高性能版本）
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getDormitoryStatistics() {
        try {
            long startTime = System.currentTimeMillis();
            Map<String, Object> statistics = cachedStatsService.getDormitoryStatistics();
            long duration = System.currentTimeMillis() - startTime;
            
            statistics.put("apiDuration", duration);
            logger.info("获取寝室统计信息成功，耗时: {}ms", duration);
            
            return ResponseEntity.ok(statistics);
            
        } catch (Exception e) {
            logger.error("获取寝室统计信息失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 分页查询指定状态的学生列表
     * @param status 状态 (1=在寝, 2=外出, 0=无记录)
     * @param page 页码 (从0开始)
     * @param size 页大小
     * @param name 姓名筛选
     * @return 分页的学生状态列表
     */
    @GetMapping("/students/by-status")
    public ResponseEntity<Page<DormitoryStatusDTO>> getStudentsByStatus(
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String name) {
        
        try {
            long startTime = System.currentTimeMillis();
            Pageable pageable = PageRequest.of(page, size);
            
            Page<DormitoryStatusDTO> result = cachedStatsService.getStudentsByStatus(status, pageable, name);
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("查询学生状态列表成功: status={}, page={}, size={}, name={}, 总数={}, 耗时={}ms", 
                       status, page, size, name, result.getTotalElements(), duration);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("查询学生状态列表失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(Page.empty());
        }
    }

    /**
     * 分页查询所有学生状态
     * @param page 页码
     * @param size 页大小
     * @param name 姓名筛选
     * @param status 状态筛选
     * @return 分页的学生状态列表
     */
    @GetMapping("/students/all")
    public ResponseEntity<Page<DormitoryStatusDTO>> getAllStudentsStatus(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer status) {
        
        try {
            long startTime = System.currentTimeMillis();
            Pageable pageable = PageRequest.of(page, size);
            
            Page<DormitoryStatusDTO> result = cachedStatsService.getAllStudentsStatus(pageable, name, status);
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("查询所有学生状态成功: page={}, size={}, name={}, status={}, 总数={}, 耗时={}ms", 
                       page, size, name, status, result.getTotalElements(), duration);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("查询所有学生状态失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(Page.empty());
        }
    }

    /**
     * 获取单个学生状态
     * @param personCode 人员编码
     * @return 学生状态
     */
    @GetMapping("/student/{personCode}")
    public ResponseEntity<DormitoryStatusDTO> getStudentStatus(@PathVariable String personCode) {
        try {
            Optional<DormitoryStatusDTO> result = cachedStatsService.getStudentStatus(personCode);
            
            if (result.isPresent()) {
                logger.debug("获取学生状态成功: personCode={}", personCode);
                return ResponseEntity.ok(result.get());
            } else {
                logger.warn("学生状态不存在: personCode={}", personCode);
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            logger.error("获取学生状态失败: personCode={}, error={}", personCode, e.getMessage());
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 刷新统计缓存
     * @return 刷新结果
     */
    @PostMapping("/cache/refresh")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        try {
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = cachedStatsService.refreshStatisticsCache();
            long duration = System.currentTimeMillis() - startTime;
            
            result.put("refreshDuration", duration);
            result.put("message", "缓存刷新成功");
            
            logger.info("手动刷新缓存成功，耗时: {}ms", duration);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("刷新缓存失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "刷新缓存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 清除所有缓存
     * @return 清除结果
     */
    @PostMapping("/cache/clear")
    public ResponseEntity<Map<String, Object>> clearCache() {
        try {
            cachedStatsService.clearAllCache();
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "所有缓存已清除");
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("手动清除所有缓存成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("清除缓存失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "清除缓存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 获取缓存统计信息
     * @return 缓存统计
     */
    @GetMapping("/cache/statistics")
    public ResponseEntity<Map<String, Object>> getCacheStatistics() {
        try {
            Map<String, Object> cacheStats = cachedStatsService.getCacheStatistics();
            cacheStats.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(cacheStats);
            
        } catch (Exception e) {
            logger.error("获取缓存统计失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取缓存统计失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 获取异步持久化队列统计
     * @return 队列统计
     */
    @GetMapping("/queue/statistics")
    public ResponseEntity<Map<String, Object>> getQueueStatistics() {
        try {
            Map<String, Object> queueStats = persistenceService.getQueueStatistics();
            queueStats.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(queueStats);
            
        } catch (Exception e) {
            logger.error("获取队列统计失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取队列统计失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 动态调整持久化开关
     * @param enabled 是否启用
     * @return 调整结果
     */
    @PostMapping("/persistence/toggle")
    public ResponseEntity<Map<String, Object>> togglePersistence(@RequestParam boolean enabled) {
        try {
            persistenceService.setPersistenceEnabled(enabled);
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "持久化状态已调整为: " + (enabled ? "启用" : "禁用"));
            result.put("enabled", enabled);
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("动态调整持久化状态: {}", enabled);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("调整持久化状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "调整持久化状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 动态调整最大TPS
     * @param maxTPS 最大TPS
     * @return 调整结果
     */
    @PostMapping("/persistence/max-tps")
    public ResponseEntity<Map<String, Object>> setMaxTPS(@RequestParam int maxTPS) {
        try {
            if (maxTPS < 100 || maxTPS > 10000) {
                Map<String, Object> error = new HashMap<>();
                error.put("error", "TPS值必须在100-10000之间");
                return ResponseEntity.badRequest().body(error);
            }
            
            persistenceService.setMaxTPS(maxTPS);
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "最大TPS已调整为: " + maxTPS);
            result.put("maxTPS", maxTPS);
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("动态调整最大TPS: {}", maxTPS);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("调整最大TPS失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "调整最大TPS失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 获取系统健康状态
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 缓存健康状态
            Map<String, Object> cacheStats = cachedStatsService.getCacheStatistics();
            boolean cacheHealthy = (Integer) cacheStats.getOrDefault("studentStatusCount", 0) > 0;
            health.put("cacheHealthy", cacheHealthy);
            health.put("cacheStats", cacheStats);
            
            // 队列健康状态
            Map<String, Object> queueStats = persistenceService.getQueueStatistics();
            Long pendingCount = (Long) queueStats.getOrDefault("pendingCount", 0L);
            Long failedCount = (Long) queueStats.getOrDefault("failedCount", 0L);
            boolean queueHealthy = pendingCount < 10000 && failedCount < 1000; // 队列积压不超过1万，失败不超过1千
            health.put("queueHealthy", queueHealthy);
            health.put("queueStats", queueStats);
            
            // 整体健康状态
            boolean overallHealthy = cacheHealthy && queueHealthy;
            health.put("overallHealthy", overallHealthy);
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            logger.error("获取系统健康状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("overallHealthy", false);
            error.put("error", "获取健康状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
}