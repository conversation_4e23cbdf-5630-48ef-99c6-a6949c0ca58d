package fastgatedemo.demo.controller;

import fastgatedemo.demo.service.DormitoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description 大屏API控制器
 * 为前端大屏提供楼栋列表等基础数据接口
 * <AUTHOR>
 * @date 2025-01-31
 */
@RestController
@RequestMapping("/api/dashboard")
@CrossOrigin(origins = "*")
public class DashboardApiController {

    private static final Logger logger = LoggerFactory.getLogger(DashboardApiController.class);

    @Autowired
    private DormitoryService dormitoryService;

    /**
     * 获取楼栋列表
     * GET /api/dashboard/buildings
     */
    @GetMapping("/buildings")
    public ResponseEntity<Map<String, Object>> getBuildingList() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("获取楼栋列表请求");
            
            // 获取所有楼栋信息
            List<Map<String, Object>> buildings = dormitoryService.getBuildingList();
            
            // 添加"全部楼栋"选项
            Map<String, Object> allBuilding = new HashMap<>();
            allBuilding.put("buildingCode", "ALL");
            allBuilding.put("buildingName", "全部楼栋");
            allBuilding.put("roomCount", 0);
            allBuilding.put("validFloorRooms", 0);
            buildings.add(0, allBuilding);
            
            response.put("success", true);
            response.put("message", "获取楼栋列表成功");
            response.put("data", buildings);
            response.put("count", buildings.size());
            
            logger.info("获取楼栋列表成功，共{}个楼栋", buildings.size());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取楼栋列表失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取楼栋列表失败: " + e.getMessage());
            response.put("data", null);
            response.put("count", 0);
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取WebSocket连接信息
     * GET /api/dashboard/websocket/info
     */
    @GetMapping("/websocket/info")
    public ResponseEntity<Map<String, Object>> getWebSocketInfo() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // WebSocket连接信息
            Map<String, Object> wsInfo = new HashMap<>();
            wsInfo.put("endpoint", "/ws/dashboard/records");
            wsInfo.put("sockjsEndpoint", "/ws/dashboard/records");
            wsInfo.put("nativeEndpoint", "/ws/dashboard/records/native");
            wsInfo.put("messageTypes", new String[]{"new_record", "batch_records", "heartbeat"});
            
            response.put("success", true);
            response.put("message", "获取WebSocket信息成功");
            response.put("data", wsInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取WebSocket信息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取WebSocket信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
