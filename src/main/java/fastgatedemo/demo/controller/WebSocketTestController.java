package fastgatedemo.demo.controller;

import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.service.DashboardWebSocketService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * @description WebSocket测试控制器
 * 用于测试WebSocket推送功能
 * <AUTHOR>
 * @date 2025-01-30
 */
@RestController
@RequestMapping("/api/websocket/test")
@CrossOrigin(origins = "*")
public class WebSocketTestController {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketTestController.class);

    @Autowired
    private DashboardWebSocketService webSocketService;

    private final Random random = new Random();
    private final String[] names = {"张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"};
    private final String[] buildings = {"1号楼", "2号楼", "3号楼", "4号楼", "5号楼"};

    /**
     * 推送测试记录
     * GET /api/websocket/test/push
     */
    @GetMapping("/push")
    public ResponseEntity<Map<String, Object>> pushTestRecord() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("收到推送测试记录请求");
            
            // 创建测试记录
            DormitoryStatusDTO testRecord = createTestRecord();
            
            // 推送记录
            webSocketService.pushNewRecord(testRecord);
            
            response.put("success", true);
            response.put("message", "测试记录推送成功");
            response.put("record", testRecord);
            response.put("connectionCount", webSocketService.getConnectionCount());
            
            logger.info("测试记录推送成功: {}", testRecord.getPersonName());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("推送测试记录失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "推送失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 批量推送测试记录
     * POST /api/websocket/test/push/batch
     */
    @PostMapping("/push/batch")
    public ResponseEntity<Map<String, Object>> pushBatchTestRecords(
            @RequestParam(defaultValue = "5") int count) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("收到批量推送测试记录请求: count={}", count);
            
            for (int i = 0; i < count; i++) {
                DormitoryStatusDTO testRecord = createTestRecord();
                webSocketService.pushNewRecord(testRecord);
                
                // 间隔一点时间，模拟真实场景
                Thread.sleep(500);
            }
            
            response.put("success", true);
            response.put("message", "批量测试记录推送成功");
            response.put("count", count);
            response.put("connectionCount", webSocketService.getConnectionCount());
            
            logger.info("批量测试记录推送成功: {}条", count);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("批量推送测试记录失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "批量推送失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取WebSocket连接状态
     * GET /api/websocket/test/status
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getWebSocketStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            int connectionCount = webSocketService.getConnectionCount();
            
            response.put("success", true);
            response.put("message", "获取状态成功");
            response.put("connectionCount", connectionCount);
            response.put("status", connectionCount > 0 ? "有连接" : "无连接");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取WebSocket状态失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取楼栋列表（供前端大屏选择）
     * GET /api/websocket/test/buildings
     */
    @GetMapping("/buildings")
    public ResponseEntity<Map<String, Object>> getBuildingList() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 返回测试楼栋列表
            String[] buildingList = {"全部楼栋", "1号楼", "2号楼", "3号楼", "4号楼", "5号楼"};

            response.put("success", true);
            response.put("message", "获取楼栋列表成功");
            response.put("buildings", buildingList);
            response.put("count", buildingList.length);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取楼栋列表失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取楼栋列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 创建测试记录
     */
    private DormitoryStatusDTO createTestRecord() {
        DormitoryStatusDTO record = new DormitoryStatusDTO();
        
        // 随机生成测试数据
        String personCode = "TEST" + String.format("%04d", random.nextInt(9999));
        String personName = names[random.nextInt(names.length)];
        String buildingName = buildings[random.nextInt(buildings.length)];
        int inOrOut = random.nextInt(2) + 1; // 1=进入, 2=离开
        
        record.setPersonCode(personCode);
        record.setPersonName(personName);
        record.setLastInOrOut(inOrOut);
        record.setLastInOrOutDesc(inOrOut == 1 ? "进入寝室" : "离开寝室");
        record.setIsInDormitory(inOrOut == 1);
        record.setDormitoryStatusDesc(inOrOut == 1 ? "已归寝室" : "未归寝室");
        record.setLastPassTime(LocalDateTime.now());
        record.setLastPassTimeStr(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        record.setBuildingName(buildingName);
        record.setDormitoryName(buildingName + String.format("%d%02d",
                random.nextInt(6) + 1, random.nextInt(20) + 1));
        record.setLastDeviceName("门禁设备" + (random.nextInt(10) + 1));
        record.setLastAreaName(buildingName);
        record.setFloor(String.valueOf(random.nextInt(6) + 1));
        record.setRoomNumber(String.format("%02d", random.nextInt(20) + 1));
        record.setQueryDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        
        return record;
    }
}
