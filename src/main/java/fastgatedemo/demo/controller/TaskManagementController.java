package fastgatedemo.demo.controller;

import fastgatedemo.demo.model.ScheduledTaskInfo;
import fastgatedemo.demo.model.TaskExecutionLog;
import fastgatedemo.demo.service.ScheduledTaskManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @description 定时任务管理控制器
 */
@Controller
@RequestMapping("/task-management")
public class TaskManagementController {

    private static final Logger logger = LoggerFactory.getLogger(TaskManagementController.class);

    @Autowired
    private ScheduledTaskManagementService taskManagementService;

    @Autowired
    private fastgatedemo.demo.service.FacePhotoSyncCheckService facePhotoSyncCheckService;

    @Autowired
    private fastgatedemo.demo.service.EGSFailedFaceExtractionService egsFailedFaceExtractionService;

    @Autowired
    private fastgatedemo.demo.service.DailyAccessRecordStatsService dailyAccessRecordStatsService;

    /**
     * 定时任务管理主页面
     */
    @GetMapping("")
    public String taskManagementPage(Model model) {
        try {
            // 获取仪表板数据
            Map<String, Object> dashboardData = taskManagementService.getTaskDashboardData();
            model.addAttribute("dashboardData", dashboardData);
            
            return "task-management";
        } catch (Exception e) {
            logger.error("加载定时任务管理页面失败", e);
            model.addAttribute("error", "加载页面失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 获取所有任务列表（API）
     */
    @GetMapping("/tasks")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<ScheduledTaskInfo> tasks = taskManagementService.getAllTasks(page, size);
            
            result.put("success", true);
            result.put("data", tasks.getContent());
            result.put("totalElements", tasks.getTotalElements());
            result.put("totalPages", tasks.getTotalPages());
            result.put("currentPage", page);
            result.put("pageSize", size);
            
        } catch (Exception e) {
            logger.error("获取任务列表失败", e);
            result.put("success", false);
            result.put("message", "获取任务列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取任务执行日志（API）
     */
    @GetMapping("/tasks/{taskName}/logs")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getTaskLogs(
            @PathVariable String taskName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<TaskExecutionLog> logs = taskManagementService.getTaskExecutionLogs(taskName, page, size);
            
            result.put("success", true);
            result.put("data", logs.getContent());
            result.put("totalElements", logs.getTotalElements());
            result.put("totalPages", logs.getTotalPages());
            result.put("currentPage", page);
            result.put("pageSize", size);
            
        } catch (Exception e) {
            logger.error("获取任务执行日志失败: taskName={}", taskName, e);
            result.put("success", false);
            result.put("message", "获取执行日志失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有任务执行日志（API）
     */
    @GetMapping("/logs")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAllTaskLogs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<TaskExecutionLog> logs = taskManagementService.getTaskExecutionLogs(null, page, size);
            
            result.put("success", true);
            result.put("data", logs.getContent());
            result.put("totalElements", logs.getTotalElements());
            result.put("totalPages", logs.getTotalPages());
            result.put("currentPage", page);
            result.put("pageSize", size);
            
        } catch (Exception e) {
            logger.error("获取所有任务执行日志失败", e);
            result.put("success", false);
            result.put("message", "获取执行日志失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 启用/禁用任务（API）
     */
    @PostMapping("/tasks/{taskName}/toggle")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> toggleTask(@PathVariable String taskName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = taskManagementService.toggleTaskEnabled(taskName);
            
            if (success) {
                result.put("success", true);
                result.put("message", "任务状态切换成功");
            } else {
                result.put("success", false);
                result.put("message", "任务不存在");
            }
            
        } catch (Exception e) {
            logger.error("切换任务状态失败: taskName={}", taskName, e);
            result.put("success", false);
            result.put("message", "切换任务状态失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 重置任务统计信息（API）
     */
    @PostMapping("/tasks/{taskName}/reset")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> resetTaskStatistics(@PathVariable String taskName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = taskManagementService.resetTaskStatistics(taskName);
            
            if (success) {
                result.put("success", true);
                result.put("message", "任务统计信息重置成功");
            } else {
                result.put("success", false);
                result.put("message", "任务不存在");
            }
            
        } catch (Exception e) {
            logger.error("重置任务统计信息失败: taskName={}", taskName, e);
            result.put("success", false);
            result.put("message", "重置统计信息失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取任务详细信息（API）
     */
    @GetMapping("/tasks/{taskName}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getTaskDetail(@PathVariable String taskName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Optional<ScheduledTaskInfo> taskOpt = taskManagementService.getTaskInfo(taskName);
            
            if (taskOpt.isPresent()) {
                ScheduledTaskInfo task = taskOpt.get();
                List<TaskExecutionLog> recentLogs = taskManagementService.getRecentExecutionLogs(taskName, 10);
                
                result.put("success", true);
                result.put("taskInfo", task);
                result.put("recentLogs", recentLogs);
            } else {
                result.put("success", false);
                result.put("message", "任务不存在");
            }
            
        } catch (Exception e) {
            logger.error("获取任务详细信息失败: taskName={}", taskName, e);
            result.put("success", false);
            result.put("message", "获取任务详细信息失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取仪表板数据（API）
     */
    @GetMapping("/dashboard")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDashboardData() {
        try {
            Map<String, Object> dashboardData = taskManagementService.getTaskDashboardData();
            return ResponseEntity.ok(dashboardData);
        } catch (Exception e) {
            logger.error("获取仪表板数据失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取仪表板数据失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 清理历史执行日志（API）
     */
    @PostMapping("/api/cleanup-logs")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> cleanupLogs(
            @RequestParam(defaultValue = "30") int daysToKeep) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            int deletedCount = taskManagementService.cleanupExecutionLogs(daysToKeep);
            
            result.put("success", true);
            result.put("message", String.format("成功清理了 %d 条历史执行日志", deletedCount));
            result.put("deletedCount", deletedCount);
            
        } catch (Exception e) {
            logger.error("清理历史执行日志失败", e);
            result.put("success", false);
            result.put("message", "清理历史日志失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取人脸照片同步统计信息（API）
     */
    @GetMapping("/api/face-photo-sync-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getFacePhotoSyncStats() {
        try {
            Map<String, Object> stats = facePhotoSyncCheckService.getSyncCheckStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("获取人脸照片同步统计信息失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取人脸照片同步问题列表（API）
     */
    @GetMapping("/api/face-photo-issues")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getFacePhotoIssues(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            org.springframework.data.domain.Page<fastgatedemo.demo.dto.FacePhotoSyncIssueDto> issues = 
                facePhotoSyncCheckService.getPersonsWithFacePhotoIssues(page, size);
            
            result.put("success", true);
            result.put("data", issues.getContent());
            result.put("totalElements", issues.getTotalElements());
            result.put("totalPages", issues.getTotalPages());
            result.put("currentPage", page);
            result.put("pageSize", size);
            
        } catch (Exception e) {
            logger.error("获取人脸照片同步问题列表失败", e);
            result.put("success", false);
            result.put("message", "获取问题列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 手动检查人脸照片同步问题（API）
     */
    @PostMapping("/api/face-photo-issues/manual-check")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> manualCheckFacePhotoIssues() {
        try {
            Map<String, Object> result = facePhotoSyncCheckService.manualCheckFacePhotoSyncIssues();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("手动检查人脸照片同步问题失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "手动检查失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 单个人员人脸照片同步（API）
     */
    @PostMapping("/api/face-photo-issues/sync-single")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> syncSinglePersonFacePhoto(@RequestParam String personCode) {
        try {
            logger.info("接收到单个人员人脸照片同步请求: personCode={}", personCode);
            
            // 调用FacePhotoSyncCheckService进行实际的同步操作
            Map<String, Object> result = facePhotoSyncCheckService.syncSinglePersonFacePhoto(personCode);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("单个人员人脸照片同步Controller异常: personCode={}", personCode, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "同步过程异常: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 手动触发EGS平台提取失败人脸记录查询（API）
     */
    @PostMapping("/api/egs-failed-face-extraction/manual-check")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> manualQueryEGSFailedFaceExtraction() {
        try {
            Map<String, Object> result = egsFailedFaceExtractionService.manualQueryFailedFaceExtraction();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("手动查询EGS提取失败记录失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "手动查询失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取EGS平台提取失败人脸记录列表（API）
     */
    @GetMapping("/api/egs-failed-face-extraction/list")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getEGSFailedFaceExtractionList() {
        try {
            Map<String, Object> result = egsFailedFaceExtractionService.getLastQueryResults();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取EGS提取失败记录列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取列表失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 手动更新face_photo表同步标识为0（API）
     */
    @PostMapping("/api/egs-failed-face-extraction/update-sync-flags")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> updateFacePhotoSyncFlags(@RequestBody Map<String, Object> requestData) {
        try {
            @SuppressWarnings("unchecked")
            List<String> personCodes = (List<String>) requestData.get("personCodes");
            
            if (personCodes == null || personCodes.isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "人员编码列表不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            logger.info("接收到手动更新face_photo表同步标识请求，人员编码数量: {}", personCodes.size());
            
            Map<String, Object> result = egsFailedFaceExtractionService.manualUpdateFacePhotoSyncFlags(personCodes);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("手动更新face_photo表同步标识失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 手动触发每日出入记录成功人数统计（API）
     */
    @PostMapping("/api/daily-access-stats/manual-check")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> manualQueryDailyAccessStats() {
        try {
            Map<String, Object> result = dailyAccessRecordStatsService.manualQueryDailyAccessStats();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("手动查询每日出入记录统计失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "手动查询失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取每日出入记录成功人数统计结果（API）
     */
    @GetMapping("/api/daily-access-stats/results")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDailyAccessStatsResults() {
        try {
            Map<String, Object> result = dailyAccessRecordStatsService.getLastQueryResults();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取每日出入记录统计结果失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取统计结果失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }







    /**
     * 手动执行任务（Vue前端API）
     * POST /task-management/tasks/{taskName}/execute
     */
    @PostMapping("/tasks/{taskName}/execute")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> executeVueTask(@PathVariable String taskName) {
        logger.info("Vue前端手动执行任务: {}", taskName);

        Map<String, Object> response = new HashMap<>();

        try {
            // 暂时不支持手动执行任务功能
            response.put("success", false);
            response.put("message", "手动执行任务功能暂未实现");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Vue前端手动执行任务失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "手动执行任务失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }


}
