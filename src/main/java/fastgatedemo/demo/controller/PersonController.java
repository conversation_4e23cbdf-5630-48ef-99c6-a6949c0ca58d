package fastgatedemo.demo.controller;

import fastgatedemo.demo.dto.PersonImportResultDTO;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.repository.PersonRepository;
import fastgatedemo.demo.service.ExcelImportService;
import fastgatedemo.demo.service.PersonMatchService;
import fastgatedemo.demo.service.PersonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 人员管理控制器
 */
@Controller
public class PersonController {

    private static final Logger logger = LoggerFactory.getLogger(PersonController.class);

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private PersonService personService;

    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private PersonMatchService personMatchService;

    /**
     * 人员列表页面
     */
    @GetMapping("/")
    public String personList(Model model,
                           @RequestParam(value = "name", required = false) String name,
                           @RequestParam(value = "syncFlag", required = false) Integer syncFlag,
                           @RequestParam(value = "page", defaultValue = "0") int page,
                           @RequestParam(value = "size", defaultValue = "10") int size) {
        
        // 创建分页对象，按更新时间倒序排列
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updateTime"));
        
        // 根据条件进行分页查询
        Page<PersonInfo> personPage;
        if (name != null && !name.trim().isEmpty() && syncFlag != null) {
            // 姓名和同步状态组合查询
            personPage = personRepository.findByPersonNameContainingAndSyncFlag(name.trim(), syncFlag, pageable);
        } else if (name != null && !name.trim().isEmpty()) {
            // 按姓名模糊查询
            personPage = personRepository.findByPersonNameContaining(name.trim(), pageable);
        } else if (syncFlag != null) {
            // 按同步状态查询
            personPage = personRepository.findBySyncFlag(syncFlag, pageable);
        } else {
            // 查询所有
            personPage = personRepository.findAll(pageable);
        }
        
        // 获取统计信息
        long totalPersons = personRepository.count();
        long syncedPersons = personRepository.countBySyncFlag(1);
        long unsyncedPersons = personRepository.countBySyncFlag(0);
        
        // 设置模型属性
        model.addAttribute("personPage", personPage);
        model.addAttribute("persons", personPage.getContent());
        model.addAttribute("personInfo", new PersonInfo());
        
        // 分页信息
        model.addAttribute("currentPage", page);
        model.addAttribute("pageSize", size);
        model.addAttribute("totalPages", personPage.getTotalPages());
        model.addAttribute("totalElements", personPage.getTotalElements());
        model.addAttribute("hasNext", personPage.hasNext());
        model.addAttribute("hasPrevious", personPage.hasPrevious());
        
        // 统计信息
        Map<String, Long> statistics = new HashMap<>();
        statistics.put("totalPersons", totalPersons);
        statistics.put("syncedPersons", syncedPersons);
        statistics.put("unsyncedPersons", unsyncedPersons);
        model.addAttribute("statistics", statistics);
        
        // 保持筛选条件
        model.addAttribute("filterName", name);
        model.addAttribute("filterSyncFlag", syncFlag);
        
        return "person-list";
    }

    /**
     * 新增人员页面
     */
    @GetMapping("/person/add")
    public String addPersonPage(Model model) {
        model.addAttribute("personInfo", new PersonInfo());
        return "person-form";
    }

    /**
     * 保存人员信息
     */
    @PostMapping("/person/save")
    public String savePerson(@Valid @ModelAttribute PersonInfo personInfo, 
                           BindingResult bindingResult,
                           RedirectAttributes redirectAttributes) {
        if (bindingResult.hasErrors()) {
            return "person-form";
        }

        try {
            // 设置默认值
            if (personInfo.getSeqid() == null || personInfo.getSeqid().isEmpty()) {
                personInfo.setSeqid(UUID.randomUUID().toString());
            }
            if (personInfo.getUpdateTime() == null) {
                personInfo.setUpdateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (personInfo.getSyncFlag() == null) {
                personInfo.setSyncFlag(0); // 默认未同步
            }
            if (personInfo.getStatus() == null) {
                personInfo.setStatus(1); // 默认启用
            }

            // 保存到数据库
            personRepository.save(personInfo);
            logger.info("保存人员信息成功: {}", personInfo.getPersonName());
            
            redirectAttributes.addFlashAttribute("successMessage", "人员信息保存成功！");
        } catch (Exception e) {
            logger.error("保存人员信息失败", e);
            redirectAttributes.addFlashAttribute("errorMessage", "保存失败：" + e.getMessage());
        }

        return "redirect:/";
    }

    /**
     * 同步单个人员到速通门系统
     */
    @PostMapping("/api/person/{id}/sync")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> syncPerson(@PathVariable String id) {
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            PersonInfo personInfo = personRepository.findById(id).orElse(null);
            if (personInfo == null) {
                result.put("success", false);
                result.put("message", "人员信息不存在");
                return ResponseEntity.badRequest().body(result);
            }

            // 调用现有的同步服务
            boolean syncSuccess = personService.addPerson(personInfo);
            
            if (syncSuccess) {
                // 更新同步标志
                personInfo.setSyncFlag(1);
                personRepository.save(personInfo);

                result.put("success", true);
                result.put("message", "同步成功");
                logger.info("同步人员成功: {}", personInfo.getPersonName());
            } else {
                result.put("success", false);
                result.put("message", "同步失败：EGS平台返回错误");
                logger.warn("同步人员失败: {} - addPerson返回false", personInfo.getPersonName());
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
            logger.error("同步人员失败", e);
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 批量同步所有未同步人员
     */
    @PostMapping("/api/persons/sync-all")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> syncAllPersons() {
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            List<PersonInfo> unsyncedPersons = personRepository.findUnsyncedPersons();
            int successCount = 0;
            int failCount = 0;

            for (PersonInfo person : unsyncedPersons) {
                try {

                    boolean syncSuccess = personService.addPerson(person);
                    if (syncSuccess) {
                        person.setSyncFlag(1);
                        personRepository.save(person);
                        successCount++;
                        logger.info("批量同步成功: {}", person.getPersonName());
                    } else {
                        failCount++;
                        logger.warn("批量同步失败: {} - addPerson返回false", person.getPersonName());
                    }
                } catch (Exception e) {
                    failCount++;
                    logger.error("批量同步异常: {}", person.getPersonName(), e);
                }
            }

            result.put("success", true);
            result.put("message", String.format("批量同步完成：成功 %d 条，失败 %d 条", successCount, failCount));
            result.put("successCount", successCount);
            result.put("failCount", failCount);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量同步失败：" + e.getMessage());
            logger.error("批量同步失败", e);
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取人员列表API
     */
    @GetMapping("/api/persons")
    @ResponseBody
    public ResponseEntity<List<PersonInfo>> getPersons() {
        List<PersonInfo> persons = personRepository.findAll();
        return ResponseEntity.ok(persons);
    }

    /**
     * 删除人员
     */
    @DeleteMapping("/api/person/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deletePerson(@PathVariable String id) {
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            personRepository.deleteById(id);
            result.put("success", true);
            result.put("message", "删除成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取楼栋人员结构信息接口
     * 
     * @return 楼栋楼层人员分布信息
     */
    @GetMapping("/api/person/buildings/structure")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getBuildingPersonStructure() {
        Map<String, Object> result = new HashMap<String, Object>();
        
        try {
            logger.info("查询楼栋人员结构信息请求");
            
            List<Map<String, Object>> buildings = personService.getBuildingPersonStructure();
            
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", buildings);
            result.put("total", buildings.size());
            
            logger.info("查询楼栋人员结构成功，共{}个楼栋", buildings.size());
            
        } catch (Exception e) {
            logger.error("查询楼栋人员结构过程中发生异常", e);
            result.put("success", false);
            result.put("message", "查询楼栋人员结构异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 预览楼栋人员信息接口
     * 
     * @param requestData 请求参数 {"buildingCode":"楼栋编码","floors":[1,2,3]}
     * @return 楼栋人员信息列表
     */
    @PostMapping("/api/person/buildings/preview")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> previewBuildingPersons(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<String, Object>();
        
        try {
            logger.info("接收到预览楼栋人员请求: {}", requestData);
            
            // 解析请求参数
            String buildingCode = (String) requestData.get("buildingCode");
            List<Integer> floors = (List<Integer>) requestData.get("floors");
            
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 调用服务层查询人员信息
            List<Map<String, Object>> persons = personService.getPersonsByBuildingAndFloors(buildingCode, floors);
            
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", persons);
            result.put("total", persons.size());
            result.put("buildingCode", buildingCode);
            result.put("floors", floors);
            
            logger.info("预览楼栋人员完成: buildingCode={}, floors={}, total={}", buildingCode, floors, persons.size());
            
        } catch (Exception e) {
            logger.error("预览楼栋人员过程中发生异常", e);
            result.put("success", false);
            result.put("message", "预览楼栋人员异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 按楼栋同步人员到EGS平台接口
     * 
     * @param requestData 请求参数 {"buildingCode":"楼栋编码","floors":[1,2,3]}
     * @return 同步结果
     */
    @PostMapping("/api/person/buildings/sync")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> syncPersonsByBuilding(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<String, Object>();
        
        try {
            logger.info("接收到按楼栋同步人员请求: {}", requestData);
            
            // 解析请求参数
            String buildingCode = (String) requestData.get("buildingCode");
            List<Integer> floors = (List<Integer>) requestData.get("floors");
            
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 调用服务层进行楼栋人员同步
            Map<String, Object> syncResult = personService.syncPersonsByBuilding(buildingCode, floors);
            
            return ResponseEntity.ok(syncResult);
            
        } catch (Exception e) {
            logger.error("按楼栋同步人员过程中发生异常", e);
            result.put("success", false);
            result.put("message", "按楼栋同步人员异常: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    // ========== Excel人员导入相关API ==========

    /**
     * Excel文件上传和解析接口
     */
    @PostMapping("/api/person/import/upload")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> uploadExcelFile(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收Excel文件上传请求: {}", file.getOriginalFilename());
            
            // 验证文件
            Map<String, Object> validationResult = excelImportService.validateFile(file);
            if (!(Boolean) validationResult.get("success")) {
                return ResponseEntity.badRequest().body(validationResult);
            }
            
            // 解析Excel文件
            Map<String, Object> parseResult = excelImportService.parseExcelFile(file);
            if (!(Boolean) parseResult.get("success")) {
                return ResponseEntity.badRequest().body(parseResult);
            }
            
            @SuppressWarnings("unchecked")
            List<PersonImportResultDTO> importData = (List<PersonImportResultDTO>) parseResult.get("data");
            
            // 返回解析结果和预览数据
            result.put("success", true);
            result.put("message", "Excel文件上传和解析成功");
            result.put("fileName", file.getOriginalFilename());
            result.put("totalCount", parseResult.get("totalCount"));
            result.put("previewData", excelImportService.getPreviewData(importData, 10));
            result.put("allData", importData); // 保存完整数据供后续处理
            
            logger.info("Excel文件解析成功: 文件={}, 总数={}", file.getOriginalFilename(), parseResult.get("totalCount"));
            
        } catch (Exception e) {
            logger.error("Excel文件上传解析异常", e);
            result.put("success", false);
            result.put("message", "文件处理失败：" + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 处理人员匹配和同步状态检查接口
     */
    @PostMapping("/api/person/import/process")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> processPersonImport(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始处理人员匹配请求");
            
            // 解析请求数据
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> rawData = (List<Map<String, Object>>) requestData.get("importData");
            
            if (rawData == null || rawData.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有要处理的数据");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 转换为DTO对象
            List<PersonImportResultDTO> importData = convertToPersonImportDTOs(rawData);
            
            // 执行人员匹配
            Map<String, Object> matchResult = personMatchService.processPersonMatching(importData);
            
            if (!(Boolean) matchResult.get("success")) {
                return ResponseEntity.status(500).body(matchResult);
            }
            
            result.put("success", true);
            result.put("message", "人员匹配处理完成");
            result.put("data", matchResult.get("data"));
            result.put("statistics", matchResult.get("statistics"));
            
            logger.info("人员匹配处理完成");
            
        } catch (Exception e) {
            logger.error("人员匹配处理异常", e);
            result.put("success", false);
            result.put("message", "匹配处理失败：" + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取导入结果详情接口（支持筛选）
     */
    @GetMapping("/api/person/import/result")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getImportResult(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里应该从缓存或会话中获取导入结果数据
            // 为简化实现，这里返回示例数据
            // 实际项目中可以使用Redis或Session存储导入结果
            
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", new ArrayList<>());
            result.put("totalElements", 0);
            result.put("currentPage", page);
            result.put("pageSize", size);
            
            logger.info("获取导入结果: status={}, page={}, size={}", status, page, size);
            
        } catch (Exception e) {
            logger.error("获取导入结果异常", e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * Excel导入人员同步接口
     */
    @PostMapping("/api/person/import/sync")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> syncExcelImportedPersons(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始同步Excel导入的人员数据");
            
            // 解析请求数据
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> rawData = (List<Map<String, Object>>) requestData.get("importData");
            
            if (rawData == null || rawData.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有要同步的数据");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 转换为DTO对象
            List<PersonImportResultDTO> importData = convertToPersonImportDTOs(rawData);
            
            // 筛选出已匹配但未同步的人员
            List<PersonImportResultDTO> unsyncedPersons = importData.stream()
                    .filter(person -> person.getIsMatched() != null && person.getIsMatched() && 
                                    (person.getIsSynced() == null || !person.getIsSynced()))
                    .collect(Collectors.toList());
            
            if (unsyncedPersons.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有找到需要同步的人员");
                return ResponseEntity.ok(result);
            }
            
            // 执行Excel导入人员同步
            Map<String, Object> syncResult = personMatchService.syncExcelImportedPersons(unsyncedPersons);
            
            if (!(Boolean) syncResult.get("success")) {
                return ResponseEntity.status(500).body(syncResult);
            }
            
            result.put("success", true);
            result.put("message", "Excel导入人员同步完成");
            result.put("data", syncResult.get("data"));
            result.put("statistics", syncResult.get("statistics"));
            
            logger.info("Excel导入人员同步完成: 总数={}, 成功={}, 失败={}", 
                       syncResult.get("totalCount"), syncResult.get("successCount"), syncResult.get("failCount"));
            
        } catch (Exception e) {
            logger.error("Excel导入人员同步异常", e);
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * Excel导入单个人员同步接口
     */
    @PostMapping("/api/person/import/sync-single")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> syncSingleExcelImportedPerson(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始同步单个Excel导入的人员数据");
            
            // 解析单个人员数据
            @SuppressWarnings("unchecked")
            Map<String, Object> personData = (Map<String, Object>) requestData.get("personData");
            
            if (personData == null) {
                result.put("success", false);
                result.put("message", "没有要同步的人员数据");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 转换为DTO对象
            List<Map<String, Object>> rawDataList = new ArrayList<>();
            rawDataList.add(personData);
            List<PersonImportResultDTO> importData = convertToPersonImportDTOs(rawDataList);
            
            if (importData.isEmpty()) {
                result.put("success", false);
                result.put("message", "人员数据转换失败");
                return ResponseEntity.badRequest().body(result);
            }
            
            PersonImportResultDTO person = importData.get(0);
            
            // 检查人员是否已匹配
            if (person.getIsMatched() == null || !person.getIsMatched()) {
                result.put("success", false);
                result.put("message", "该人员未匹配，无法同步");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 检查人员是否已同步
            if (person.getIsSynced() != null && person.getIsSynced()) {
                result.put("success", false);
                result.put("message", "该人员已同步，无需重复同步");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 执行单个人员同步
            Map<String, Object> syncResult = personMatchService.syncExcelImportedPersons(importData);
            
            if (!(Boolean) syncResult.get("success")) {
                return ResponseEntity.status(500).body(syncResult);
            }
            
            result.put("success", true);
            result.put("message", "人员同步成功");
            result.put("data", syncResult.get("data"));
            result.put("statistics", syncResult.get("statistics"));
            
            logger.info("单个Excel导入人员同步完成: 姓名={}, 工号={}", person.getName(), person.getJobNumber());
            
        } catch (Exception e) {
            logger.error("单个Excel导入人员同步异常", e);
            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 转换原始数据为PersonImportResultDTO对象
     */
    private List<PersonImportResultDTO> convertToPersonImportDTOs(List<Map<String, Object>> rawData) {
        List<PersonImportResultDTO> result = new ArrayList<>();
        
        for (Map<String, Object> data : rawData) {
            PersonImportResultDTO dto = new PersonImportResultDTO();
            dto.setName((String) data.get("name"));
            dto.setJobNumber((String) data.get("jobNumber"));
            dto.setDepartmentCode((String) data.get("departmentCode"));
            dto.setRowNumber((Integer) data.get("rowNumber"));
            dto.setIsMatched((Boolean) data.get("isMatched"));
            dto.setIsSynced((Boolean) data.get("isSynced"));
            dto.setMatchStatus((String) data.get("matchStatus"));
            
            // 处理匹配来源
            String matchSourceStr = (String) data.get("matchSource");
            if (matchSourceStr != null) {
                try {
                    dto.setMatchSource(PersonImportResultDTO.MatchSource.valueOf(matchSourceStr));
                } catch (IllegalArgumentException e) {
                    dto.setMatchSource(PersonImportResultDTO.MatchSource.NONE);
                }
            }
            
            dto.setLocalPersonCode((String) data.get("localPersonCode"));
            
            // 处理匹配的人员信息（注意：这里简化处理，实际项目中可能需要重新查询数据库）
            if (dto.getMatchSource() == PersonImportResultDTO.MatchSource.LOCAL && dto.getLocalPersonCode() != null) {
                // 查询本地人员信息
                Optional<PersonInfo> localPersonOpt = personRepository.findByPersonCode(dto.getLocalPersonCode());
                if (localPersonOpt.isPresent()) {
                    dto.setLocalMatchedPerson(localPersonOpt.get());
                }
            }
            
            result.add(dto);
        }
        
        return result;
    }

} 