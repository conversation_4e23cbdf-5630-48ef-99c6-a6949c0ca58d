package fastgatedemo.demo.controller;

import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.service.CachedDormitoryStatsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 大屏展示专用API控制器（基于Redis缓存）
 * 完全基于Redis缓存实现高性能实时查询
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/api/dashboard")
@CrossOrigin(origins = "*")
public class DashboardController {

    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);

    @Autowired
    private CachedDormitoryStatsService cachedDormitoryStatsService;

    /**
     * 获取大屏统计数据（基于已分配宿舍的学生）
     * 查询所有已经分配宿舍的学生作为总人数基数
     * 然后根据缓存中的进出统计在寝人员和不在寝人员以及无记录人员
     * GET /api/dashboard/statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getDashboardStatistics() {
        
        logger.info("获取大屏统计数据（基于已分配宿舍的学生）");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 基于已分配宿舍学生获取统计数据
            Map<String, Object> statistics = cachedDormitoryStatsService.getDormitoryStatistics();
            
            // 构建大屏专用数据格式
            Map<String, Object> dashboardData = new HashMap<>();
            
            long totalPersons = ((Number) statistics.getOrDefault("totalPersons", 0)).longValue();
            long returnedCount = ((Number) statistics.getOrDefault("returnedCount", 0)).longValue();
            long notReturnedCount = ((Number) statistics.getOrDefault("notReturnedCount", 0)).longValue();
            long personsWithoutRecords = ((Number) statistics.getOrDefault("personsWithoutRecords", 0)).longValue();
            double returnRate = ((Number) statistics.getOrDefault("returnRate", 0.0)).doubleValue();
            
            dashboardData.put("totalPersons", totalPersons);
            dashboardData.put("inDormitoryPersons", returnedCount);      // 在寝人员
            dashboardData.put("outDormitoryPersons", notReturnedCount);  // 不在寝人员
            dashboardData.put("personsWithoutRecords", personsWithoutRecords); // 无记录人员
            dashboardData.put("returnRate", returnRate);
            dashboardData.put("date", getCurrentDate());
            dashboardData.put("updateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            dashboardData.put("queryMethod", statistics.get("queryMethod"));
            dashboardData.put("dataSource", "assigned_dormitory_based");
            dashboardData.put("baseDescription", "基于已分配宿舍学生统计");
            
            long duration = System.currentTimeMillis() - startTime;
            dashboardData.put("queryDuration", duration);
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", dashboardData);
            
            logger.info("大屏统计数据获取成功: 总人数(已分配宿舍)={}, 在寝={}, 外出={}, 无记录={}, 归寝率={}%, 耗时={}ms", 
                    totalPersons, returnedCount, notReturnedCount, personsWithoutRecords, returnRate, duration);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取大屏统计数据失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            response.put("dataSource", "error");
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取多楼栋统计数据（基于已分配宿舍的学生）- 优化版本
     * 支持多个楼栋代码参数，返回汇总统计信息
     * 优化逻辑：先根据选择的楼栋查出所有关联的学生人数，然后根据这些学生编码去Redis中一个个查询是否在寝室
     * GET /api/dashboard/statistics/buildings?buildingCodes=A1,A2,B1
     */
    @GetMapping("/statistics/buildings")
    public ResponseEntity<Map<String, Object>> getDashboardStatisticsByBuildings(
            @RequestParam(required = false) List<String> buildingCodes) {

        logger.info("获取多楼栋统计数据（优化版本）: buildingCodes={}", buildingCodes);

        Map<String, Object> response = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();

            Map<String, Object> statistics;

            // 如果没有指定楼栋或楼栋列表为空，返回全部统计
            if (buildingCodes == null || buildingCodes.isEmpty()) {
                logger.info("未指定楼栋，使用全部统计");
                statistics = cachedDormitoryStatsService.getDormitoryStatistics();
            } else {
                // 获取指定楼栋的统计数据（使用优化后的查询逻辑）
                logger.info("使用优化查询逻辑获取指定楼栋统计数据");
                statistics = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
            }

            // 构建大屏专用数据格式
            Map<String, Object> dashboardData = new HashMap<>();

            long totalPersons = ((Number) statistics.getOrDefault("totalPersons", 0)).longValue();
            long returnedCount = ((Number) statistics.getOrDefault("returnedCount", 0)).longValue();
            long notReturnedCount = ((Number) statistics.getOrDefault("notReturnedCount", 0)).longValue();
            long personsWithoutRecords = ((Number) statistics.getOrDefault("personsWithoutRecords", 0)).longValue();
            double returnRate = ((Number) statistics.getOrDefault("returnRate", 0.0)).doubleValue();

            dashboardData.put("totalPersons", totalPersons);
            dashboardData.put("inDormitoryPersons", returnedCount);      // 在寝人员
            dashboardData.put("outDormitoryPersons", notReturnedCount);  // 不在寝人员
            dashboardData.put("personsWithoutRecords", personsWithoutRecords); // 无记录人员
            dashboardData.put("returnRate", returnRate);
            dashboardData.put("date", getCurrentDate());
            dashboardData.put("updateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            dashboardData.put("queryMethod", statistics.get("queryMethod"));
            dashboardData.put("dataSource", buildingCodes == null || buildingCodes.isEmpty() ?
                "assigned_dormitory_based" : "buildings_filtered_optimized");
            dashboardData.put("baseDescription", buildingCodes == null || buildingCodes.isEmpty() ?
                "基于已分配宿舍学生统计" : "基于指定楼栋学生统计（优化版本）");
            dashboardData.put("selectedBuildings", buildingCodes);

            // 添加优化相关的性能信息
            if (statistics.containsKey("calculationDuration")) {
                dashboardData.put("calculationDuration", statistics.get("calculationDuration"));
            }
            if (statistics.containsKey("performanceStats")) {
                dashboardData.put("performanceStats", statistics.get("performanceStats"));
            }

            long duration = System.currentTimeMillis() - startTime;
            dashboardData.put("queryDuration", duration);

            response.put("success", true);
            response.put("message", "查询成功（优化版本）");
            response.put("data", dashboardData);

            logger.info("多楼栋统计数据获取成功（优化版本）: 楼栋={}, 总人数={}, 在寝={}, 外出={}, 无记录={}, 归寝率={}%, 接口耗时={}ms",
                    buildingCodes, totalPersons, returnedCount, notReturnedCount, personsWithoutRecords, returnRate, duration);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取多楼栋统计数据失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            response.put("dataSource", "error");
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取实时进出记录（基于Redis缓存）
     * GET /api/dashboard/records?limit=30
     */
    @GetMapping("/records")
    public ResponseEntity<Map<String, Object>> getDashboardRecords(
            @RequestParam(defaultValue = "30") int limit) {
        
        logger.info("获取大屏进出记录（基于Redis缓存）: limit={}", limit);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 从Redis缓存获取最新记录
            List<DormitoryStatusDTO> latestRecords = getLatestRecordsFromCache(limit);
            
            // 转换为大屏需要的格式
            List<Map<String, Object>> dashboardRecords = latestRecords.stream()
                    .map(this::convertToDashboardRecord)
                    .collect(Collectors.toList());
            
            long duration = System.currentTimeMillis() - startTime;
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("date", getCurrentDate());
            response.put("count", dashboardRecords.size());
            response.put("data", dashboardRecords);
            response.put("dataSource", "redis_cache");
            response.put("queryDuration", duration);
            
            logger.info("大屏进出记录获取成功: 共{}条记录, 耗时={}ms", dashboardRecords.size(), duration);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取大屏进出记录失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            response.put("dataSource", "error");
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取大屏实时统计数据（基于已分配宿舍的学生统计，仅返回总数）
     * GET /api/dashboard/realtime
     */
    @GetMapping("/realtime")
    public ResponseEntity<Map<String, Object>> getRealtimeDashboardData() {
        
        logger.info("获取大屏实时统计数据（基于已分配宿舍学生，仅统计信息）");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 仅获取基于已分配宿舍学生的统计数据，不查询详细记录
            Map<String, Object> statistics = cachedDormitoryStatsService.getDormitoryStatistics();
            
            // 构建实时数据响应
            Map<String, Object> realtimeData = new HashMap<>();
            
            // 统计信息
            long totalPersons = ((Number) statistics.getOrDefault("totalPersons", 0)).longValue();
            long returnedCount = ((Number) statistics.getOrDefault("returnedCount", 0)).longValue();
            long notReturnedCount = ((Number) statistics.getOrDefault("notReturnedCount", 0)).longValue();
            long personsWithoutRecords = ((Number) statistics.getOrDefault("personsWithoutRecords", 0)).longValue();
            double returnRate = ((Number) statistics.getOrDefault("returnRate", 0.0)).doubleValue();
            
            realtimeData.put("totalPersons", totalPersons);
            realtimeData.put("inDormitoryPersons", returnedCount);      // 在寝人员
            realtimeData.put("outDormitoryPersons", notReturnedCount);  // 不在寝人员
            realtimeData.put("personsWithoutRecords", personsWithoutRecords); // 无记录人员
            realtimeData.put("returnRate", returnRate);
            realtimeData.put("queryMethod", statistics.get("queryMethod"));
            realtimeData.put("baseDescription", "基于已分配宿舍学生统计");
            realtimeData.put("date", getCurrentDate());
            realtimeData.put("timestamp", System.currentTimeMillis());
            realtimeData.put("dataSource", "assigned_dormitory_based");
            
            long duration = System.currentTimeMillis() - startTime;
            realtimeData.put("queryDuration", duration);
            
            response.put("success", true);
            response.put("message", "获取实时统计数据成功");
            response.put("data", realtimeData);
            
            logger.info("大屏实时统计数据获取成功: 总人数(已分配宿舍)={}, 在寝={}, 外出={}, 无记录={}, 耗时={}ms", 
                    totalPersons, returnedCount, notReturnedCount, personsWithoutRecords, duration);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取大屏实时统计数据失败: {}", e.getMessage(), e);
            response.put("success", false);   
            response.put("message", "获取实时统计数据失败: " + e.getMessage());
            response.put("dataSource", "error");
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 统一的大屏数据查询接口（支持楼栋筛选和数据刷新）
     * 这是优化后的统一接口，支持以下功能：
     * 1. 无参数：查询全部统计数据
     * 2. buildingCodes参数：查询指定楼栋统计数据
     * 3. refresh=true：强制刷新缓存后查询
     * POST /api/dashboard/query
     */
    @PostMapping("/query")
    public ResponseEntity<Map<String, Object>> queryDashboardData(
            @RequestBody(required = false) Map<String, Object> requestBody) {

        // 解析请求参数
        List<String> buildingCodes = null;
        boolean forceRefresh = false;

        if (requestBody != null) {
            Object buildingCodesObj = requestBody.get("buildingCodes");
            if (buildingCodesObj instanceof List) {
                buildingCodes = (List<String>) buildingCodesObj;
            }

            Object refreshObj = requestBody.get("refresh");
            if (refreshObj instanceof Boolean) {
                forceRefresh = (Boolean) refreshObj;
            }
        }

        logger.info("统一大屏数据查询: buildingCodes={}, forceRefresh={}", buildingCodes, forceRefresh);

        Map<String, Object> response = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();

            // 如果需要强制刷新，先清除缓存
            if (forceRefresh) {
                logger.info("执行强制刷新缓存");
                cachedDormitoryStatsService.clearAllCache();
            }

            Map<String, Object> statistics;

            // 根据参数选择查询方式
            if (buildingCodes == null || buildingCodes.isEmpty()) {
                logger.info("查询全部统计数据");
                statistics = cachedDormitoryStatsService.getDormitoryStatistics();
            } else {
                logger.info("查询指定楼栋统计数据（优化版本）");
                statistics = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
            }

            // 构建统一的响应格式
            Map<String, Object> dashboardData = new HashMap<>();

            long totalPersons = ((Number) statistics.getOrDefault("totalPersons", 0)).longValue();
            long returnedCount = ((Number) statistics.getOrDefault("returnedCount", 0)).longValue();
            long notReturnedCount = ((Number) statistics.getOrDefault("notReturnedCount", 0)).longValue();
            long personsWithoutRecords = ((Number) statistics.getOrDefault("personsWithoutRecords", 0)).longValue();
            double returnRate = ((Number) statistics.getOrDefault("returnRate", 0.0)).doubleValue();

            dashboardData.put("totalPersons", totalPersons);
            dashboardData.put("inDormitoryPersons", returnedCount);
            dashboardData.put("outDormitoryPersons", notReturnedCount);
            dashboardData.put("personsWithoutRecords", personsWithoutRecords);
            dashboardData.put("returnRate", returnRate);
            dashboardData.put("date", getCurrentDate());
            dashboardData.put("updateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            dashboardData.put("queryMethod", statistics.get("queryMethod"));

            // 数据源标识
            String dataSource = forceRefresh ? "force_refreshed" :
                               (buildingCodes == null || buildingCodes.isEmpty() ? "all_buildings" : "selected_buildings");
            dashboardData.put("dataSource", dataSource);
            dashboardData.put("selectedBuildings", buildingCodes);
            dashboardData.put("wasRefreshed", forceRefresh);

            // 添加性能信息
            if (statistics.containsKey("performanceStats")) {
                dashboardData.put("performanceStats", statistics.get("performanceStats"));
            }
            if (statistics.containsKey("calculationDuration")) {
                dashboardData.put("calculationDuration", statistics.get("calculationDuration"));
            }

            long duration = System.currentTimeMillis() - startTime;
            dashboardData.put("queryDuration", duration);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", dashboardData);

            logger.info("统一大屏数据查询成功: 楼栋={}, 总人数={}, 在寝={}, 外出={}, 无记录={}, 归寝率={}%, 刷新={}, 耗时={}ms",
                    buildingCodes, totalPersons, returnedCount, notReturnedCount, personsWithoutRecords, returnRate, forceRefresh, duration);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("统一大屏数据查询失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            response.put("dataSource", "error");
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 强制刷新缓存数据（保留原接口兼容性）
     * POST /api/dashboard/refresh
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshDashboardData() {
        logger.info("收到强制刷新大屏数据请求（兼容接口）");

        // 调用统一接口，设置强制刷新
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("refresh", true);

        return queryDashboardData(requestBody);
    }

    /**
     * 大屏健康检查接口
     * GET /api/dashboard/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> dashboardHealthCheck() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String currentDate = getCurrentDate();
            Map<String, Object> todayStats = cachedDormitoryStatsService.getDormitoryStatistics();
            Map<String, Object> cacheStatus = cachedDormitoryStatsService.getCacheStatistics();
            
            // 构建健康检查响应
            Map<String, Object> healthData = new HashMap<>();
            healthData.put("status", "healthy");
            healthData.put("currentDate", currentDate);
            healthData.put("currentTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            healthData.put("todayStatistics", todayStats);
            healthData.put("cacheStatus", cacheStatus);
            healthData.put("dataSource", "redis_cache");
            healthData.put("timestamp", System.currentTimeMillis());
            
            response.put("success", true);
            response.put("message", "大屏系统运行正常");
            response.put("data", healthData);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("大屏健康检查失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "健康检查失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取缓存状态信息
     * GET /api/dashboard/cache/status
     */
    @GetMapping("/cache/status")
    public ResponseEntity<Map<String, Object>> getCacheStatus() {
        logger.info("获取缓存状态信息");

        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> cacheStatus = cachedDormitoryStatsService.getCacheStatistics();

            response.put("success", true);
            response.put("message", "获取缓存状态成功");
            response.put("data", cacheStatus);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取缓存状态失败: {}", e.getMessage(), e);

            response.put("success", false);
            response.put("message", "获取缓存状态失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 清除缓存
     * POST /api/dashboard/cache/clear
     */
    @PostMapping("/cache/clear")
    public ResponseEntity<Map<String, Object>> clearCache() {
        logger.info("收到清除缓存请求");

        Map<String, Object> response = new HashMap<>();

        try {
            // 清除Redis缓存
            cachedDormitoryStatsService.clearAllCache();
            logger.info("Redis缓存已清除");

            response.put("success", true);
            response.put("message", "缓存清除成功");
            response.put("timestamp", System.currentTimeMillis());

            logger.info("缓存清除成功");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("清除缓存失败: {}", e.getMessage(), e);

            response.put("success", false);
            response.put("message", "清除缓存失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.status(500).body(response);
        }
    }



    // ==================== 私有方法 ====================

    /**
     * 从Redis缓存获取最新通行记录
     * @param limit 记录数量限制
     * @return 最新通行记录列表
     */
    private List<DormitoryStatusDTO> getLatestRecordsFromCache(int limit) {
        try {
            logger.debug("从Redis缓存获取最新{}条通行记录", limit);
            
            List<DormitoryStatusDTO> recentRecords = new ArrayList<>();
            
            // 获取最近有通行记录的学生（在寝状态）
            Page<DormitoryStatusDTO> inDormitoryPage = cachedDormitoryStatsService.getStudentsByStatus(
                    1, PageRequest.of(0, limit/2), null);
            recentRecords.addAll(inDormitoryPage.getContent());
            
            // 获取最近有通行记录的学生（外出状态）  
            Page<DormitoryStatusDTO> outDormitoryPage = cachedDormitoryStatsService.getStudentsByStatus(
                    2, PageRequest.of(0, limit/2), null);
            recentRecords.addAll(outDormitoryPage.getContent());
            
            // 按时间排序并限制数量
            return recentRecords.stream()
                    .filter(record -> record.getLastPassTime() != null)
                    .sorted((a, b) -> b.getLastPassTime().compareTo(a.getLastPassTime()))
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            logger.error("从缓存获取最新记录失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换为大屏记录格式
     */
    private Map<String, Object> convertToDashboardRecord(DormitoryStatusDTO dto) {
        Map<String, Object> record = new HashMap<>();
        
        record.put("personCode", dto.getPersonCode());
        record.put("personName", dto.getPersonName());
        record.put("lastPassTimeStr", dto.getLastPassTimeStr());
        record.put("lastInOrOut", dto.getLastInOrOut());
        record.put("lastInOrOutDesc", dto.getLastInOrOutDesc());
        record.put("isInDormitory", dto.getIsInDormitory());
        record.put("dormitoryStatusDesc", dto.getDormitoryStatusDesc());
        record.put("lastDeviceName", dto.getLastDeviceName());
        record.put("lastAreaName", dto.getLastAreaName());
        record.put("dormitoryName", dto.getDormitoryName());
        record.put("buildingName", dto.getBuildingName());
        record.put("floor", dto.getFloor());
        record.put("roomNumber", dto.getRoomNumber());
        
        return record;
    }

    /**
     * 获取当前日期
     * @return 当前日期字符串
     */
    private String getCurrentDate() {
        return java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}