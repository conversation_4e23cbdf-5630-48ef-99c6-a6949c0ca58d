package fastgatedemo.demo.controller;

import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.service.DormitoryStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @description 寝室归宿状态控制器
 * 提供查询未归寝室人员的API接口
 * <AUTHOR>
 * @date 2025-01-25
 */
@RestController
@RequestMapping("/api/dormitory-status")
public class DormitoryStatusController {

    private static final Logger logger = LoggerFactory.getLogger(DormitoryStatusController.class);

    @Autowired
    private DormitoryStatusService dormitoryStatusService;

    /**
     * 查询指定日期未归寝室的人员列表
     * GET /api/dormitory-status/not-returned?date=2025-01-25
     */
    @GetMapping("/not-returned")
    public ResponseEntity<Map<String, Object>> getNotReturnedPersons(
            @RequestParam(required = false) String date) {
        
        // 如果未指定日期，使用当前日期
        if (date == null || date.trim().isEmpty()) {
            date = dormitoryStatusService.getCurrentDate();
        }
        
        logger.info("查询{}未归寝室人员", date);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证日期格式
            if (!dormitoryStatusService.isValidDate(date)) {
                response.put("success", false);
                response.put("message", "日期格式错误，请使用 YYYY-MM-DD 格式");
                return ResponseEntity.badRequest().body(response);
            }
            
            List<DormitoryStatusDTO> notReturnedPersons = dormitoryStatusService.getNotReturnedPersons(date);
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("date", date);
            response.put("count", notReturnedPersons.size());
            response.put("data", notReturnedPersons);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("查询未归寝室人员失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 分页查询指定日期未归寝室的人员列表
     * GET /api/dormitory-status/not-returned/page?date=2025-01-25&page=0&size=20
     */
    @GetMapping("/not-returned/page")
    public ResponseEntity<Map<String, Object>> getNotReturnedPersonsWithPage(
            @RequestParam(required = false) String date,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        if (date == null || date.trim().isEmpty()) {
            date = dormitoryStatusService.getCurrentDate();
        }
        
        logger.info("分页查询{}未归寝室人员，页码: {}, 大小: {}", date, page, size);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!dormitoryStatusService.isValidDate(date)) {
                response.put("success", false);
                response.put("message", "日期格式错误，请使用 YYYY-MM-DD 格式");
                return ResponseEntity.badRequest().body(response);
            }
            
            Pageable pageable = PageRequest.of(page, size);
            Page<DormitoryStatusDTO> pageResult = dormitoryStatusService.getNotReturnedPersonsWithPage(date, pageable);
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("date", date);
            response.put("data", pageResult.getContent());
            response.put("totalElements", pageResult.getTotalElements());
            response.put("totalPages", pageResult.getTotalPages());
            response.put("currentPage", pageResult.getNumber());
            response.put("pageSize", pageResult.getSize());
            response.put("hasNext", pageResult.hasNext());
            response.put("hasPrevious", pageResult.hasPrevious());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("分页查询未归寝室人员失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 查询指定日期已归寝室的人员列表
     * GET /api/dormitory-status/returned?date=2025-01-25
     */
    @GetMapping("/returned")
    public ResponseEntity<Map<String, Object>> getReturnedPersons(
            @RequestParam(required = false) String date) {
        
        if (date == null || date.trim().isEmpty()) {
            date = dormitoryStatusService.getCurrentDate();
        }
        
        logger.info("查询{}已归寝室人员", date);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!dormitoryStatusService.isValidDate(date)) {
                response.put("success", false);
                response.put("message", "日期格式错误，请使用 YYYY-MM-DD 格式");
                return ResponseEntity.badRequest().body(response);
            }
            
            List<DormitoryStatusDTO> returnedPersons = dormitoryStatusService.getReturnedPersons(date);
            
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("date", date);
            response.put("count", returnedPersons.size());
            response.put("data", returnedPersons);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("查询已归寝室人员失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取指定日期的寝室归宿统计信息
     * GET /api/dormitory-status/statistics?date=2025-01-25
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getDormitoryStatistics(
            @RequestParam(required = false) String date) {
        
        if (date == null || date.trim().isEmpty()) {
            date = dormitoryStatusService.getCurrentDate();
        }
        
        logger.info("获取{}寝室归宿统计信息", date);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!dormitoryStatusService.isValidDate(date)) {
                response.put("success", false);
                response.put("message", "日期格式错误，请使用 YYYY-MM-DD 格式");
                return ResponseEntity.badRequest().body(response);
            }
            
            Map<String, Object> statistics = dormitoryStatusService.getDormitoryStatistics(date);

            // 转换为前端大屏需要的格式
            Map<String, Object> dashboardData = new HashMap<>();
            dashboardData.put("totalPersons", statistics.getOrDefault("totalPersons", 0));
            dashboardData.put("inDormitoryPersons", statistics.getOrDefault("returnedCount", 0));
            dashboardData.put("outDormitoryPersons", statistics.getOrDefault("notReturnedCount", 0));
            dashboardData.put("returnRate", statistics.getOrDefault("returnRate", 0.0));
            dashboardData.put("date", date);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("date", date);
            response.put("data", dashboardData);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取寝室归宿统计信息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "统计信息获取失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 查询指定人员在指定日期的归宿状态
     * GET /api/dormitory-status/person/{personCode}?date=2025-01-25
     */
    @GetMapping("/person/{personCode}")
    public ResponseEntity<Map<String, Object>> getPersonDormitoryStatus(
            @PathVariable String personCode,
            @RequestParam(required = false) String date) {
        
        if (date == null || date.trim().isEmpty()) {
            date = dormitoryStatusService.getCurrentDate();
        }
        
        logger.info("查询人员{}在{}的归宿状态", personCode, date);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!dormitoryStatusService.isValidDate(date)) {
                response.put("success", false);
                response.put("message", "日期格式错误，请使用 YYYY-MM-DD 格式");
                return ResponseEntity.badRequest().body(response);
            }
            
            Optional<DormitoryStatusDTO> statusOpt = dormitoryStatusService.getPersonDormitoryStatus(personCode, date);
            
            if (statusOpt.isPresent()) {
                response.put("success", true);
                response.put("message", "查询成功");
                response.put("data", statusOpt.get());
            } else {
                response.put("success", false);
                response.put("message", "未找到该人员在指定日期的通行记录");
                response.put("personCode", personCode);
                response.put("date", date);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("查询人员归宿状态失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取当前日期
     * GET /api/dormitory-status/current-date
     */
    @GetMapping("/current-date")
    public ResponseEntity<Map<String, Object>> getCurrentDate() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String currentDate = dormitoryStatusService.getCurrentDate();
            
            response.put("success", true);
            response.put("currentDate", currentDate);
            response.put("message", "获取当前日期成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取当前日期失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取当前日期失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 健康检查接口
     * GET /api/dormitory-status/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();

        try {
            String currentDate = dormitoryStatusService.getCurrentDate();
            Map<String, Object> todayStats = dormitoryStatusService.getDormitoryStatistics(currentDate);
            Map<String, Object> cacheStatus = dormitoryStatusService.getCacheStatus();

            response.put("success", true);
            response.put("message", "寝室归宿查询服务运行正常");
            response.put("currentDate", currentDate);
            response.put("todayStatistics", todayStats);
            response.put("cacheStatus", cacheStatus);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("健康检查失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "服务异常: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 清除统计缓存
     * DELETE /api/dormitory-status/cache?date=2025-01-25
     */
    @DeleteMapping("/cache")
    public ResponseEntity<Map<String, Object>> clearCache(@RequestParam(required = false) String date) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (date != null && !date.trim().isEmpty()) {
                if (!dormitoryStatusService.isValidDate(date)) {
                    response.put("success", false);
                    response.put("message", "日期格式错误，请使用 YYYY-MM-DD 格式");
                    return ResponseEntity.badRequest().body(response);
                }

                dormitoryStatusService.clearStatisticsCache(date);
                response.put("message", "成功清除日期 " + date + " 的统计缓存");
            } else {
                dormitoryStatusService.clearAllStatisticsCache();
                response.put("message", "成功清除所有统计缓存");
            }

            response.put("success", true);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("清除缓存失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "清除缓存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 预热统计缓存
     * POST /api/dormitory-status/cache/warmup?days=7
     */
    @PostMapping("/cache/warmup")
    public ResponseEntity<Map<String, Object>> warmupCache(@RequestParam(defaultValue = "7") int days) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (days <= 0 || days > 30) {
                response.put("success", false);
                response.put("message", "预热天数必须在1-30之间");
                return ResponseEntity.badRequest().body(response);
            }

            long startTime = System.currentTimeMillis();
            dormitoryStatusService.warmUpRecentDaysCache(days);
            long endTime = System.currentTimeMillis();

            response.put("success", true);
            response.put("message", "成功预热最近 " + days + " 天的统计缓存");
            response.put("days", days);
            response.put("duration", (endTime - startTime) + "ms");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("预热缓存失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "预热缓存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取缓存状态
     * GET /api/dormitory-status/cache/status
     */
    @GetMapping("/cache/status")
    public ResponseEntity<Map<String, Object>> getCacheStatus() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> cacheStatus = dormitoryStatusService.getCacheStatus();

            response.put("success", true);
            response.put("message", "获取缓存状态成功");
            response.putAll(cacheStatus);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取缓存状态失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取缓存状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 分页查询所有学生的在寝状态
     * GET /api/dormitory-status/students?page=0&size=20&personName=张三&dormitoryStatus=1
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param personName 人员姓名筛选（可选）
     * @param dormitoryStatus 在寝状态筛选（可选：1=在寝，2=外出，0=无记录）
     */
    @GetMapping("/students")
    public ResponseEntity<Map<String, Object>> getAllStudentsStatus(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String personName,
            @RequestParam(required = false) Integer dormitoryStatus) {

        logger.info("分页查询所有学生状态，页码: {}, 大小: {}, 姓名筛选: {}, 状态筛选: {}",
                page, size, personName, dormitoryStatus);

        Map<String, Object> response = new HashMap<>();

        try {
            // 验证分页参数
            if (page < 0) {
                response.put("success", false);
                response.put("message", "页码不能小于0");
                return ResponseEntity.badRequest().body(response);
            }

            if (size <= 0 || size > 100) {
                response.put("success", false);
                response.put("message", "每页大小必须在1-100之间");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证状态筛选参数
            if (dormitoryStatus != null && (dormitoryStatus < 0 || dormitoryStatus > 2)) {
                response.put("success", false);
                response.put("message", "状态筛选参数错误，有效值：0=无记录，1=在寝，2=外出");
                return ResponseEntity.badRequest().body(response);
            }

            Pageable pageable = PageRequest.of(page, size);
            Page<DormitoryStatusDTO> pageResult = dormitoryStatusService.getAllStudentsStatusOptimized(
                    pageable, personName, dormitoryStatus);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", pageResult.getContent());
            response.put("totalElements", pageResult.getTotalElements());
            response.put("totalPages", pageResult.getTotalPages());
            response.put("currentPage", pageResult.getNumber());
            response.put("pageSize", pageResult.getSize());
            response.put("hasNext", pageResult.hasNext());
            response.put("hasPrevious", pageResult.hasPrevious());
            // 构建筛选条件信息（Java 8兼容）
            Map<String, Object> filtersMap = new HashMap<>();
            filtersMap.put("personName", personName != null ? personName : "");
            filtersMap.put("dormitoryStatus", dormitoryStatus != null ? dormitoryStatus : "");
            response.put("filters", filtersMap);

            logger.info("查询学生状态成功，总数: {}, 当前页: {}",
                    pageResult.getTotalElements(), pageResult.getContent().size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询学生状态失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
