package fastgatedemo.demo.controller;

import fastgatedemo.demo.model.DormitoryInfo;
import fastgatedemo.demo.model.MiddlewareDormitoryInfo;
import fastgatedemo.demo.model.UniResult;
import fastgatedemo.demo.service.DormitoryService;
import fastgatedemo.demo.service.PersonDormitorySyncService;
import fastgatedemo.demo.service.PersonDormitorySyncPreviewService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description 宿舍管理控制器
 */
@RestController
public class DormitoryController {

    private static final Logger logger = LoggerFactory.getLogger(DormitoryController.class);

    @Autowired
    private DormitoryService dormitoryService;

    @Autowired
    private PersonDormitorySyncService syncService;

    @Autowired
    private PersonDormitorySyncPreviewService syncPreviewService;

    /**
     * 新增宿舍信息接口
     * 接收JSON格式：{"res":{"Name":"区域名称","Code":"A1020","Parentcode":"area","Restype":3},"Floor":"6","Amount":"4","Label":1}
     * 
     * @param requestData 宿舍信息请求数据
     * @return 统一响应结果
     */
    @PostMapping("/fastgate/dormitory")
    public ResponseEntity<Map<String, Object>> addDormitory(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到添加宿舍请求: {}", requestData);
            
            // 解析请求数据
            Map<String, Object> resData = (Map<String, Object>) requestData.get("res");
            if (resData == null) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "缺少res参数");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 创建宿舍信息对象
            DormitoryInfo dormitoryInfo = new DormitoryInfo();
            dormitoryInfo.setName((String) resData.get("Name"));
            dormitoryInfo.setCode((String) resData.get("Code"));
            dormitoryInfo.setParentcode((String) resData.get("Parentcode"));
            
            Object restypeObj = resData.get("Restype");
            if (restypeObj != null) {
                dormitoryInfo.setRestype(Integer.valueOf(restypeObj.toString()));
            }
            
            dormitoryInfo.setFloor((String) requestData.get("Floor"));
            dormitoryInfo.setAmount((String) requestData.get("Amount"));
            
            Object labelObj = requestData.get("Label");
            if (labelObj != null) {
                dormitoryInfo.setLabel(Integer.valueOf(labelObj.toString()));
            }
            
            // 数据验证
            if (dormitoryInfo.getName() == null || dormitoryInfo.getName().trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "宿舍名称不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (dormitoryInfo.getCode() == null || dormitoryInfo.getCode().trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "宿舍编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 调用服务层添加宿舍
            UniResult uniResult = dormitoryService.addDormitory(dormitoryInfo);
            
            if (uniResult != null && uniResult.getErrCode() == 200) {
                // 成功响应，模拟速通门系统返回的数据格式
                Map<String, Object> data = new HashMap<>();
                data.put("Id", dormitoryInfo.getId() != null ? dormitoryInfo.getId() : 95); // 使用实际ID或默认值
                data.put("Code", dormitoryInfo.getCode());
                data.put("Name", dormitoryInfo.getName());
                data.put("Level", 2);
                data.put("Restype", dormitoryInfo.getRestype());
                data.put("Respath", "/area/" + dormitoryInfo.getCode() + "/");
                data.put("Parentcode", dormitoryInfo.getParentcode());
                data.put("ParentName", null);
                data.put("Belongin", null);
                data.put("Areatype", null);
                data.put("Ins", null);
                data.put("Outs", null);
                data.put("Stay", null);
                data.put("Levelorder", 6);
                data.put("Count", null);
                data.put("OnlineCount", null);
                data.put("Inorout", null);
                data.put("Version", null);
                data.put("Owned", null);
                data.put("UpdateTime", null);
                data.put("OutLandCode", null);
                data.put("DeviceId", null);
                
                result.put("data", data);
                result.put("ErrCode", 200);
                result.put("ErrMsg", "添加宿舍成功");
                
                logger.info("宿舍添加成功: {}", dormitoryInfo.getCode());
            } else {
                // 失败响应
                result.put("ErrCode", uniResult != null ? uniResult.getErrCode() : 500);
                result.put("ErrMsg", uniResult != null ? uniResult.getErrMsg() : "添加宿舍失败");
                
                logger.error("宿舍添加失败: {}, 错误: {}", dormitoryInfo.getCode(), 
                           uniResult != null ? uniResult.getErrMsg() : "未知错误");
            }
            
        } catch (Exception e) {
            logger.error("添加宿舍过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "添加宿舍过程中发生异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 查询中间库宿舍列表接口
     * 
     * @return 宿舍列表响应
     */
    @GetMapping("/dormitory/list")
    public ResponseEntity<Map<String, Object>> getDormitoryList() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("查询中间库宿舍列表");
            
            List<MiddlewareDormitoryInfo> dormitories = dormitoryService.getAllMiddlewareDormitories();
            
            result.put("ErrCode", 200);
            result.put("ErrMsg", "查询宿舍列表成功");
            result.put("data", dormitories);
            result.put("total", dormitories.size());
            
            logger.info("查询宿舍列表成功，共{}条记录", dormitories.size());
            
        } catch (Exception e) {
            logger.error("查询宿舍列表过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询宿舍列表异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 同步指定宿舍到EGS平台接口
     * 
     * @param id 宿舍ID
     * @return 同步结果响应
     */
    @PostMapping("/dormitory/sync/{id}")
    public ResponseEntity<Map<String, Object>> syncDormitoryToEGS(@PathVariable String id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到同步宿舍到EGS平台请求: id={}", id);
            
            UniResult syncResult = dormitoryService.syncDormitoryToEGS(id);
            
            if (syncResult != null) {
                result.put("ErrCode", syncResult.getErrCode());
                result.put("ErrMsg", syncResult.getErrMsg());
                result.put("data", syncResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "同步宿舍失败，返回结果为空");
            }
            
            logger.info("同步宿舍完成: id={}, result={}", id, syncResult);
            
        } catch (Exception e) {
            logger.error("同步宿舍过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "同步宿舍异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 批量同步所有宿舍到EGS平台接口
     * 
     * @return 批量同步结果响应
     */
    @PostMapping("/dormitory/sync/all")
    public ResponseEntity<Map<String, Object>> syncAllDormitoriesToEGS() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始批量同步所有宿舍到EGS平台");
            
            UniResult syncResult = dormitoryService.syncAllDormitoriesToEGS();
            
            if (syncResult != null) {
                result.put("ErrCode", syncResult.getErrCode());
                result.put("ErrMsg", syncResult.getErrMsg());
                result.put("data", syncResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "批量同步宿舍失败，返回结果为空");
            }
            
            logger.info("批量同步宿舍完成: result={}", syncResult);
            
        } catch (Exception e) {
            logger.error("批量同步宿舍过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "批量同步宿舍异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取楼栋列表接口
     * 
     * @return 楼栋列表响应
     */
    @GetMapping("/dormitory/buildings")
    public ResponseEntity<Map<String, Object>> getBuildingList() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("查询楼栋列表请求");
            
            List<Map<String, Object>> buildings = dormitoryService.getBuildingList();
            
            result.put("ErrCode", 200);
            result.put("ErrMsg", "查询成功");
            result.put("data", buildings);
            result.put("total", buildings.size());
            
            logger.info("查询楼栋列表成功，共{}个楼栋", buildings.size());
            
        } catch (Exception e) {
            logger.error("查询楼栋列表过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询楼栋列表异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 楼栋选择同步到EGS平台接口
     * 
     * @param requestData 楼栋编码列表 {"buildingCodes":["code1","code2"]}
     * @return 同步结果响应
     */
    @PostMapping("/dormitory/sync/buildings")
    public ResponseEntity<Map<String, Object>> syncBuildingsByCode(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到楼栋选择同步请求: {}", requestData);
            
            // 解析请求参数
            List<String> buildingCodes = (List<String>) requestData.get("buildingCodes");
            
            if (buildingCodes == null || buildingCodes.isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码列表不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 调用服务层进行楼栋选择同步
            UniResult syncResult = dormitoryService.syncBuildingsByCode(buildingCodes);
            
            if (syncResult != null) {
                result.put("ErrCode", syncResult.getErrCode());
                result.put("ErrMsg", syncResult.getErrMsg());
                result.put("data", syncResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "楼栋选择同步失败，返回结果为空");
            }
            
            logger.info("楼栋选择同步完成: buildingCodes={}, result={}", buildingCodes, syncResult);
            
        } catch (Exception e) {
            logger.error("楼栋选择同步过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "楼栋选择同步异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 查询房间信息接口
     * 
     * @param requestData 查询参数 {"buildnum":"C","floor":2,"label":-1}
     * @return 房间信息响应
     */
    @PostMapping("/fastgate/dormitory/member")
    public ResponseEntity<Map<String, Object>> getDormitoryMembers(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到查询房间信息请求: {}", requestData);
            
            // 解析请求参数
            String buildnum = (String) requestData.get("buildnum");
            Object floorObj = requestData.get("floor");
            
            if (buildnum == null || buildnum.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            Integer floor = null;
            if (floorObj != null) {
                floor = Integer.valueOf(floorObj.toString());
            }
            
            // label固定使用-1（EGS平台要求的默认值），不使用前端传递的值
            Integer label = -1;
            
            // 调用服务层查询房间信息
            UniResult uniResult = dormitoryService.getDormitoryMembers(buildnum, floor, label);
            
            if (uniResult != null) {
                result.put("ErrCode", uniResult.getErrCode());
                result.put("ErrMsg", uniResult.getErrMsg());
                result.put("data", uniResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "查询房间信息失败，返回结果为空");
            }
            
            logger.info("房间信息查询完成: buildnum={}, floor={}, result={}", buildnum, floor, uniResult);
            
        } catch (Exception e) {
            logger.error("查询房间信息过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询房间信息异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 批量绑定人员到宿舍接口
     * 
     * @param requestData 绑定参数 {"code":["STU006","STU005","STU010","STU009"],"roomId":1925}
     * @return 绑定结果响应
     */
    @PostMapping("/fastgate/dormitory/batchBingPersonDor")
    public ResponseEntity<Map<String, Object>> batchBindPersonDormitory(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到批量绑定人员宿舍请求: {}", requestData);
            
            // 解析请求参数
            List<String> codes = (List<String>) requestData.get("code");
            Object roomIdObj = requestData.get("roomId");
            
            if (codes == null || codes.isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "人员编码列表不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (roomIdObj == null) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "房间ID不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            String roomId = roomIdObj.toString();
            
            // 调用服务层批量绑定人员宿舍
            UniResult uniResult = dormitoryService.batchBindPersonDormitory(codes, roomId);
            
            if (uniResult != null) {
                result.put("ErrCode", uniResult.getErrCode());
                result.put("ErrMsg", uniResult.getErrMsg());
                result.put("data", uniResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "批量绑定人员宿舍失败，返回结果为空");
            }
            
            logger.info("批量绑定人员宿舍完成: codes={}, roomId={}, result={}", codes, roomId, uniResult);
            
        } catch (Exception e) {
            logger.error("批量绑定人员宿舍过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "批量绑定人员宿舍异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 查询人员宿舍关联关系接口
     * 
     * @return 人员宿舍关联关系列表
     */
    @GetMapping("/fastgate/dormitory/relations")
    public ResponseEntity<Map<String, Object>> getPersonDormitoryRelations(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("分页查询人员宿舍关联关系请求(旧URL): page={}, size={}", page, size);
            
            // 参数校验
            if (page < 0) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "页码不能小于0");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (size <= 0 || size > 100) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "每页大小必须在1-100之间");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 调用服务层进行分页查询
            Map<String, Object> pageData = dormitoryService.getPersonDormitoryRelationsPaged(page, size);
            
            result.put("ErrCode", 200);
            result.put("ErrMsg", "分页查询成功");
            result.putAll(pageData);
            
            logger.info("分页查询人员宿舍关联关系成功(旧URL): page={}, size={}, total={}",
                    page, size, pageData.get("total"));
            
        } catch (Exception e) {
            logger.error("分页查询人员宿舍关联关系异常(旧URL): {}", e.getMessage(), e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "分页查询异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 批量绑定所有人员宿舍关联到EGS平台接口
     * 
     * @return 批量绑定结果
     */
    @PostMapping("/fastgate/dormitory/batch-bind-all")
    public ResponseEntity<Map<String, Object>> batchBindAllPersonDormitory() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始批量绑定所有人员宿舍关联到EGS平台");
            
            UniResult bindResult = dormitoryService.batchBindAllPersonDormitory();
            
            if (bindResult != null) {
                result.put("ErrCode", bindResult.getErrCode());
                result.put("ErrMsg", bindResult.getErrMsg());
                result.put("data", bindResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "批量绑定失败，返回结果为空");
            }
            
            logger.info("批量绑定所有人员宿舍关联完成: result={}", bindResult);
            
        } catch (Exception e) {
            logger.error("批量绑定所有人员宿舍关联过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "批量绑定异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 分页查询宿舍列表接口
     * 
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页宿舍列表响应
     */
    @GetMapping("/dormitory/list/page")
    public ResponseEntity<Map<String, Object>> getDormitoryListByPage(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("分页查询中间库宿舍列表: page={}, size={}", page, size);
            
            // 参数校验
            if (page < 0) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "页码不能小于0");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (size <= 0 || size > 100) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "每页大小必须在1-100之间");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 调用服务层进行分页查询
            Map<String, Object> pageData = dormitoryService.getMiddlewareDormitoriesByPage(page, size);
            
            result.put("ErrCode", 200);
            result.put("ErrMsg", "分页查询宿舍列表成功");
            result.putAll(pageData);
            
            logger.info("分页查询宿舍列表成功: page={}, size={}, total={}", 
                    page, size, pageData.get("total"));
            
        } catch (Exception e) {
            logger.error("分页查询宿舍列表过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "分页查询宿舍列表异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 同步楼栋为部门到EGS平台
     * @param requestData 请求数据，包含楼栋编码列表
     * @return 同步结果
     */
    @PostMapping("/dormitory/sync/departments")
    public ResponseEntity<Map<String, Object>> syncDepartments(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到同步部门请求: {}", requestData);
            
            // 解析请求参数
            @SuppressWarnings("unchecked")
            List<String> buildingCodes = (List<String>) requestData.get("buildingCodes");
            
            // 调用服务层进行部门同步
            UniResult syncResult = dormitoryService.syncDepartments(buildingCodes);
            
            if (syncResult != null) {
                result.put("ErrCode", syncResult.getErrCode());
                result.put("ErrMsg", syncResult.getErrMsg());
                result.put("data", syncResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "部门同步失败，返回结果为空");
            }
            
            logger.info("部门同步完成: buildingCodes={}, result={}", buildingCodes, syncResult);
            
        } catch (Exception e) {
            logger.error("部门同步过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "部门同步异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 查询楼栋关联人员接口
     * 
     * @param buildingCode 楼栋编码
     * @return 楼栋关联人员列表
     */
    @GetMapping("/api/building/persons")
    public ResponseEntity<Map<String, Object>> getBuildingPersons(@RequestParam String buildingCode) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("查询楼栋{}关联人员请求", buildingCode);
            
            // 参数验证
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 查询楼栋关联人员
            List<Map<String, Object>> persons = dormitoryService.getPersonsByBuildingCode(buildingCode.trim());
            
            result.put("ErrCode", 200);
            result.put("ErrMsg", "查询成功");
            result.put("data", persons);
            result.put("total", persons.size());
            result.put("buildingCode", buildingCode.trim());
            
            logger.info("查询楼栋{}关联人员成功，共{}人", buildingCode, persons.size());

        } catch (Exception e) {
            logger.error("查询楼栋{}关联人员异常: {}", buildingCode, e.getMessage(), e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 查询楼栋楼层关联人员接口
     * 
     * @param buildingCode 楼栋编码
     * @param floor 楼层
     * @return 楼栋楼层关联人员列表
     */
    @GetMapping("/api/building/floor/persons")
    public ResponseEntity<Map<String, Object>> getBuildingFloorPersons(
            @RequestParam String buildingCode, 
            @RequestParam Integer floor) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("查询楼栋{}第{}层关联人员请求", buildingCode, floor);
            
            // 参数验证
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (floor == null || floor <= 0) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼层必须为正整数");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 查询楼栋楼层关联人员
            List<Map<String, Object>> persons = dormitoryService.getPersonsByBuildingAndFloor(buildingCode.trim(), floor);
            
            result.put("ErrCode", 200);
            result.put("ErrMsg", "查询成功");
            result.put("data", persons);
            result.put("total", persons.size());
            result.put("buildingCode", buildingCode.trim());
            result.put("floor", floor);
            
            logger.info("查询楼栋{}第{}层关联人员成功，共{}人", buildingCode, floor, persons.size());

        } catch (Exception e) {
            logger.error("查询楼栋{}第{}层关联人员异常: {}", buildingCode, floor, e.getMessage(), e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 查询楼栋楼层列表接口
     * 
     * @param buildingCode 楼栋编码
     * @return 楼栋楼层列表
     */
    @GetMapping("/api/building/floors")
    public ResponseEntity<Map<String, Object>> getBuildingFloors(@RequestParam String buildingCode) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("查询楼栋{}楼层列表请求", buildingCode);
            
            // 参数验证
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 查询楼栋楼层列表
            List<Map<String, Object>> floors = dormitoryService.getBuildingFloorList(buildingCode.trim());
            
            result.put("ErrCode", 200);
            result.put("ErrMsg", "查询成功");
            result.put("data", floors);
            result.put("total", floors.size());
            result.put("buildingCode", buildingCode.trim());
            
            logger.info("查询楼栋{}楼层列表成功，共{}层", buildingCode, floors.size());

        } catch (Exception e) {
            logger.error("查询楼栋{}楼层列表异常: {}", buildingCode, e.getMessage(), e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 查询楼栋人员统计信息接口
     * 
     * @param buildingCode 楼栋编码
     * @return 楼栋人员统计信息
     */
    @GetMapping("/api/building/statistics")
    public ResponseEntity<Map<String, Object>> getBuildingStatistics(@RequestParam String buildingCode) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("查询楼栋{}统计信息请求", buildingCode);
            
            // 参数验证
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 查询楼栋统计信息
            Map<String, Object> statistics = dormitoryService.getBuildingPersonsStatistics(buildingCode.trim());
            
            if (statistics.containsKey("error")) {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "查询统计信息失败: " + statistics.get("error"));
            } else {
                result.put("ErrCode", 200);
                result.put("ErrMsg", "查询成功");
                result.put("data", statistics);
            }
            
            logger.info("查询楼栋{}统计信息成功", buildingCode);

        } catch (Exception e) {
            logger.error("查询楼栋{}统计信息异常: {}", buildingCode, e.getMessage(), e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    // === 按楼栋绑定功能相关接口 ===

    /**
     * 查询楼栋房间统计信息接口
     * 
     * @param buildingCode 楼栋编码
     * @return 楼栋房间统计信息
     */
    @GetMapping("/dormitory/building-stats/{buildingCode}")
    public ResponseEntity<Map<String, Object>> getBuildingRoomStats(@PathVariable String buildingCode) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("查询楼栋{}房间统计信息请求", buildingCode);
            
            // 参数验证
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 查询楼栋房间统计信息
            Map<String, Object> stats = dormitoryService.getBuildingRoomStats(buildingCode.trim());
            
            if (stats.containsKey("error")) {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "查询楼栋统计信息失败: " + stats.get("error"));
            } else {
                result.put("ErrCode", 200);
                result.put("ErrMsg", "查询成功");
                result.put("data", stats);
            }
            
            logger.info("查询楼栋{}房间统计信息成功", buildingCode);

        } catch (Exception e) {
            logger.error("查询楼栋{}房间统计信息异常: {}", buildingCode, e.getMessage(), e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 查询楼层房间人员分配信息接口
     * 
     * @param buildingCode 楼栋编码
     * @param floor 楼层
     * @return 楼层房间人员分配信息
     */
    @GetMapping("/dormitory/floor-rooms")
    public ResponseEntity<Map<String, Object>> getFloorRoomPersons(
            @RequestParam String buildingCode, 
            @RequestParam Integer floor) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("查询楼栋{}第{}层房间人员分配信息请求", buildingCode, floor);
            
            // 参数验证
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (floor == null || floor <= 0) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼层必须为正整数");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 查询楼层房间人员分配信息
            List<Map<String, Object>> rooms = dormitoryService.getFloorRoomPersons(buildingCode.trim(), floor);
            
            result.put("ErrCode", 200);
            result.put("ErrMsg", "查询成功");
            result.put("data", rooms);
            result.put("total", rooms.size());
            result.put("buildingCode", buildingCode.trim());
            result.put("floor", floor);
            
            logger.info("查询楼栋{}第{}层房间人员分配信息成功，共{}间房", buildingCode, floor, rooms.size());

        } catch (Exception e) {
            logger.error("查询楼栋{}第{}层房间人员分配信息异常: {}", buildingCode, floor, e.getMessage(), e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "查询异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 按楼层绑定人员到宿舍接口
     * 
     * @param requestData 绑定参数 {"buildingCode":"xxx","floor":2}
     * @return 绑定结果
     */
    @PostMapping("/dormitory/bind-building-floor")
    public ResponseEntity<Map<String, Object>> bindBuildingFloorPersons(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到按楼层绑定人员宿舍请求: {}", requestData);
            
            // 解析请求参数
            String buildingCode = (String) requestData.get("buildingCode");
            Object floorObj = requestData.get("floor");
            
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (floorObj == null) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼层不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            Integer floor = Integer.valueOf(floorObj.toString());
            if (floor <= 0) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼层必须为正整数");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 调用服务层按楼层绑定人员宿舍
            UniResult bindResult = dormitoryService.bindBuildingFloorPersons(buildingCode.trim(), floor);
            
            if (bindResult != null) {
                result.put("ErrCode", bindResult.getErrCode());
                result.put("ErrMsg", bindResult.getErrMsg());
                result.put("data", bindResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "按楼层绑定人员宿舍失败，返回结果为空");
            }
            
            logger.info("按楼层绑定人员宿舍完成: buildingCode={}, floor={}, result={}", buildingCode, floor, bindResult);
            
        } catch (Exception e) {
            logger.error("按楼层绑定人员宿舍过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "按楼层绑定人员宿舍异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 按宿舍绑定人员接口
     * 
     * @param requestData 绑定参数 {"buildingCode":"xxx","floor":2,"roomName":"201"}
     * @return 绑定结果
     */
    @PostMapping("/dormitory/bind-room")
    public ResponseEntity<Map<String, Object>> bindRoomPersons(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到按宿舍绑定人员请求: {}", requestData);
            
            // 解析请求参数
            String buildingCode = (String) requestData.get("buildingCode");
            Object floorObj = requestData.get("floor");
            String roomName = (String) requestData.get("roomName");
            
            if (buildingCode == null || buildingCode.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼栋编码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (floorObj == null) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼层不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (roomName == null || roomName.trim().isEmpty()) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "房间名称不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            Integer floor = Integer.valueOf(floorObj.toString());
            if (floor <= 0) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "楼层必须为正整数");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 调用服务层按宿舍绑定人员
            UniResult bindResult = dormitoryService.bindRoomPersons(buildingCode.trim(), floor, roomName.trim());
            
            if (bindResult != null) {
                result.put("ErrCode", bindResult.getErrCode());
                result.put("ErrMsg", bindResult.getErrMsg());
                result.put("data", bindResult.getData());
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", "按宿舍绑定人员失败，返回结果为空");
            }
            
            logger.info("按宿舍绑定人员完成: buildingCode={}, floor={}, roomName={}, result={}", buildingCode, floor, roomName, bindResult);
            
        } catch (Exception e) {
            logger.error("按宿舍绑定人员过程中发生异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "按宿舍绑定人员异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 分页查询人员宿舍关联关系接口 (保留原有分页路径)
     * 
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页的人员宿舍关联关系列表
     */
    @GetMapping("/fastgate/dormitory/relations/page")
    public ResponseEntity<Map<String, Object>> getPersonDormitoryRelationsPaged(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Map<String, Object> result = new HashMap<>();
        try {
            logger.info("分页查询人员宿舍关联关系请求: page={}, size={}", page, size);
            // 参数校验
            if (page < 0) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "页码不能小于0");
                return ResponseEntity.badRequest().body(result);
            }
            if (size <= 0 || size > 100) {
                result.put("ErrCode", 400);
                result.put("ErrMsg", "每页大小必须在1-100之间");
                return ResponseEntity.badRequest().body(result);
            }
            // 调用服务层进行分页查询
            Map<String, Object> pageData = dormitoryService.getPersonDormitoryRelationsPaged(page, size);
            result.put("ErrCode", 200);
            result.put("ErrMsg", "分页查询成功");
            result.putAll(pageData);
            logger.info("分页查询人员宿舍关联关系成功: page={}, size={}, total={}", page, size, pageData.get("total"));
        } catch (Exception e) {
            logger.error("分页查询人员宿舍关联关系异常: page={}, size={}, error={}", page, size, e.getMessage(), e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "分页查询异常: " + e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 手动触发同步未同步的人员宿舍关联关系接口
     *
     * @return 同步结果
     */
    @PostMapping("/fastgate/dormitory/relations/sync")
    public ResponseEntity<Map<String, Object>> manualSyncUnsyncedRelations() {
        Map<String, Object> result = new HashMap<>();

        try {
            logger.info("手动触发同步未同步的人员宿舍关联关系");

            Map<String, Object> syncResult = syncService.manualSyncUnsyncedRelations();

            if ((Boolean) syncResult.get("success")) {
                result.put("ErrCode", 200);
                result.put("ErrMsg", syncResult.get("message"));
                result.put("data", syncResult);
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", syncResult.get("message"));
            }

            logger.info("手动同步完成: result={}", syncResult);

        } catch (Exception e) {
            logger.error("手动同步人员宿舍关联关系异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "手动同步异常: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 获取人员宿舍关联同步统计信息接口
     *
     * @return 统计信息
     */
    @GetMapping("/fastgate/dormitory/relations/sync/statistics")
    public ResponseEntity<Map<String, Object>> getSyncStatistics() {
        Map<String, Object> result = new HashMap<>();

        try {
            logger.info("获取人员宿舍关联同步统计信息");

            Map<String, Object> stats = syncService.getSyncStatistics();

            result.put("ErrCode", 200);
            result.put("ErrMsg", "获取统计信息成功");
            result.put("data", stats);

            logger.info("获取同步统计信息成功: stats={}", stats);

        } catch (Exception e) {
            logger.error("获取同步统计信息异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "获取统计信息异常: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 获取人员宿舍关联同步预览数据接口
     *
     * @return 预览数据
     */
    @GetMapping("/fastgate/dormitory/relations/sync/preview")
    public ResponseEntity<Map<String, Object>> getSyncPreview() {
        Map<String, Object> result = new HashMap<>();

        try {
            logger.info("获取人员宿舍关联同步预览数据");

            Map<String, Object> previewData = syncPreviewService.getSyncPreviewData();

            if ((Boolean) previewData.get("success")) {
                result.put("ErrCode", 200);
                result.put("ErrMsg", "获取预览数据成功");
                result.put("data", previewData);
            } else {
                result.put("ErrCode", 500);
                result.put("ErrMsg", previewData.get("message"));
            }

            logger.info("获取同步预览数据成功: totalCount={}, buildingCount={}",
                       previewData.get("totalCount"), previewData.get("buildingCount"));

        } catch (Exception e) {
            logger.error("获取同步预览数据异常", e);
            result.put("ErrCode", 500);
            result.put("ErrMsg", "获取预览数据异常: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }



}