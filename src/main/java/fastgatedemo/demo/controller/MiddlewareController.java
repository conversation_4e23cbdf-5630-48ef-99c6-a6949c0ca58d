package fastgatedemo.demo.controller;

import fastgatedemo.demo.model.MiddlewarePersonInfo;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.service.MiddlewareService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description 中间库数据管理控制器
 */
@Controller
@RequestMapping("/middleware")
public class MiddlewareController {

    private static final Logger logger = LoggerFactory.getLogger(MiddlewareController.class);

    @Autowired
    private MiddlewareService middlewareService;

    /**
     * 中间库人员列表页面
     */
    @GetMapping("/persons")
    public String middlewarePersonList(Model model,
                                     @RequestParam(value = "perType", required = false) Integer perType,
                                     @RequestParam(value = "department", required = false) String department,
                                     @RequestParam(value = "name", required = false) String name,
                                     @RequestParam(value = "page", defaultValue = "0") int page,
                                     @RequestParam(value = "size", defaultValue = "10") int size) {
        
        // 分页查询人员信息
        Page<MiddlewarePersonInfo> personPage = middlewareService.getPersonsPagedByCondition(perType, department, name, page, size);
        
        // 获取统计信息
        Map<String, Long> statistics = middlewareService.getPersonStatistics();
        
        // 获取同步进度
        Map<String, Object> syncProgress = middlewareService.getSyncProgress();
        
        model.addAttribute("personPage", personPage);
        model.addAttribute("persons", personPage.getContent());
        model.addAttribute("statistics", statistics);
        model.addAttribute("syncProgress", syncProgress);
        model.addAttribute("currentPage", page);
        model.addAttribute("pageSize", size);
        model.addAttribute("totalPages", personPage.getTotalPages());
        model.addAttribute("totalElements", personPage.getTotalElements());
        model.addAttribute("hasNext", personPage.hasNext());
        model.addAttribute("hasPrevious", personPage.hasPrevious());
        
        // 保持筛选条件
        if (perType != null) {
            model.addAttribute("filterType", "perType");
            model.addAttribute("filterValue", perType);
        } else if (department != null && !department.trim().isEmpty()) {
            model.addAttribute("filterType", "department");
            model.addAttribute("filterValue", department);
        } else if (name != null && !name.trim().isEmpty()) {
            model.addAttribute("filterType", "name");
            model.addAttribute("filterValue", name);
        }
        
        return "middleware-list";
    }

    /**
     * 中间库统计信息API
     */
    @GetMapping("/api/statistics")
    @ResponseBody
    public ResponseEntity<Map<String, Long>> getStatistics() {
        try {
            Map<String, Long> statistics = middlewareService.getPersonStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 检查数据库连接状态API
     */
    @GetMapping("/api/connection/check")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> checkConnection() {
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            boolean connected = middlewareService.checkDatabaseConnection();
            result.put("connected", connected);
            result.put("message", connected ? "数据库连接正常" : "数据库连接失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put("connected", false);
            result.put("message", "数据库连接异常: " + e.getMessage());
            logger.error("检查数据库连接失败", e);
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 查询中间库人员信息API
     */
    @GetMapping("/api/persons")
    @ResponseBody
    public ResponseEntity<List<MiddlewarePersonInfo>> getPersons(
            @RequestParam(value = "perType", required = false) Integer perType,
            @RequestParam(value = "department", required = false) String department,
            @RequestParam(value = "name", required = false) String name) {
        try {
            List<MiddlewarePersonInfo> persons;
            
            if (perType != null) {
                persons = middlewareService.getPersonsByType(perType);
            } else if (department != null && !department.trim().isEmpty()) {
                persons = middlewareService.getPersonsByDepartment(department.trim());
            } else if (name != null && !name.trim().isEmpty()) {
                persons = middlewareService.searchPersonsByName(name.trim());
            } else {
                persons = middlewareService.getAllActivePersons();
            }
            
            return ResponseEntity.ok(persons);
        } catch (Exception e) {
            logger.error("查询中间库人员信息失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 转换单个人员信息API
     */
    @PostMapping("/api/person/{perCode}/convert")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> convertSinglePerson(@PathVariable String perCode) {
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            PersonInfo personInfo = middlewareService.convertSinglePerson(perCode);
            result.put("success", true);
            result.put("message", "转换成功");
            result.put("personInfo", personInfo);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "转换失败: " + e.getMessage());
            logger.error("转换单个人员信息失败: {}", perCode, e);
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 批量同步中间库数据到EGS系统API
     */
    @PostMapping("/api/sync/batch")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> batchSyncToEGS(@RequestBody(required = false) Map<String, Object> request) {
        try {
            List<String> perCodes = null;
            if (request != null && request.containsKey("perCodes")) {
                perCodes = (List<String>) request.get("perCodes");
            }
            
            Map<String, Object> result = middlewareService.batchSyncToEGS(perCodes);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<String, Object>();
            result.put("success", false);
            result.put("message", "批量同步异常: " + e.getMessage());
            logger.error("批量同步API调用失败", e);
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取同步进度API
     */
    @GetMapping("/api/sync/progress")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getSyncProgress() {
        try {
            Map<String, Object> progress = middlewareService.getSyncProgress();
            return ResponseEntity.ok(progress);
        } catch (Exception e) {
            logger.error("获取同步进度失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 分页查询中间库人员信息API
     */
    @GetMapping("/api/persons/paged")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getPersonsPaged(
            @RequestParam(value = "perType", required = false) Integer perType,
            @RequestParam(value = "department", required = false) String department,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size) {
        try {
            Page<MiddlewarePersonInfo> personPage = middlewareService.getPersonsPagedByCondition(perType, department, name, page, size);
            
            Map<String, Object> result = new HashMap<String, Object>();
            result.put("content", personPage.getContent());
            result.put("totalElements", personPage.getTotalElements());
            result.put("totalPages", personPage.getTotalPages());
            result.put("currentPage", personPage.getNumber());
            result.put("size", personPage.getSize());
            result.put("hasNext", personPage.hasNext());
            result.put("hasPrevious", personPage.hasPrevious());
            result.put("first", personPage.isFirst());
            result.put("last", personPage.isLast());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("分页查询中间库人员信息失败", e);
            return ResponseEntity.status(500).build();
        }
    }
} 