package fastgatedemo.demo.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description 大屏页面控制器
 * 提供大屏首页的页面访问
 * <AUTHOR>
 * @date 2025-01-30
 */
@Controller
@RequestMapping("/dashboard")
public class DashboardPageController {
    
    /**
     * 大屏首页
     * 访问路径: /dashboard
     */
    @GetMapping("")
    public String dashboardPage() {
        return "dashboard";
    }
    
    /**
     * 大屏首页（带斜杠）
     * 访问路径: /dashboard/
     */
    @GetMapping("/")
    public String dashboardPageWithSlash() {
        return "dashboard";
    }
}
