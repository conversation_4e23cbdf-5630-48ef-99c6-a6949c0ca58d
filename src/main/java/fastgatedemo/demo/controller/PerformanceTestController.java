package fastgatedemo.demo.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fastgatedemo.demo.service.ViewMessageProcessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @description 性能测试控制器
 * 用于测试高并发场景下的系统性能
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/api/test")
public class PerformanceTestController {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceTestController.class);

    @Autowired
    private ViewMessageProcessService messageProcessService;

    private final ExecutorService testExecutor = Executors.newFixedThreadPool(50);
    private final Random random = new Random();

    /**
     * 模拟通行记录消息
     * @param count 生成数量
     * @param concurrent 并发数
     * @return 测试结果
     */
    @PostMapping("/simulate-access-records")
    public ResponseEntity<Map<String, Object>> simulateAccessRecords(
            @RequestParam(defaultValue = "100") int count,
            @RequestParam(defaultValue = "10") int concurrent) {

        if (count > 10000) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "测试数量不能超过10000条");
            return ResponseEntity.badRequest().body(error);
        }

        if (concurrent > 100) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "并发数不能超过100");
            return ResponseEntity.badRequest().body(error);
        }

        try {
            logger.info("开始性能测试: 总数={}, 并发数={}", count, concurrent);
            long startTime = System.currentTimeMillis();
            
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);
            AtomicLong totalProcessTime = new AtomicLong(0);

            // 分批并发执行
            CompletableFuture<Void>[] futures = new CompletableFuture[concurrent];
            int batchSize = count / concurrent;
            int remainder = count % concurrent;

            for (int i = 0; i < concurrent; i++) {
                int currentBatchSize = batchSize + (i < remainder ? 1 : 0);
                final int threadIndex = i;
                
                futures[i] = CompletableFuture.runAsync(() -> {
                    for (int j = 0; j < currentBatchSize; j++) {
                        try {
                            long processStart = System.currentTimeMillis();
                            String testMessage = generateTestMessage(threadIndex * 1000 + j);
                            boolean result = messageProcessService.processMessage(testMessage);
                            long processTime = System.currentTimeMillis() - processStart;
                            
                            totalProcessTime.addAndGet(processTime);
                            
                            if (result) {
                                successCount.incrementAndGet();
                            } else {
                                failCount.incrementAndGet();
                            }
                            
                        } catch (Exception e) {
                            failCount.incrementAndGet();
                            logger.error("测试消息处理失败: {}", e.getMessage());
                        }
                    }
                }, testExecutor);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures).join();

            long totalTime = System.currentTimeMillis() - startTime;
            int totalProcessed = successCount.get() + failCount.get();
            
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", count);
            result.put("concurrentThreads", concurrent);
            result.put("successCount", successCount.get());
            result.put("failCount", failCount.get());
            result.put("totalTime", totalTime);
            result.put("averageProcessTime", totalProcessed > 0 ? totalProcessTime.get() / totalProcessed : 0);
            result.put("tps", totalTime > 0 ? (totalProcessed * 1000.0 / totalTime) : 0);
            result.put("successRate", totalProcessed > 0 ? (successCount.get() * 100.0 / totalProcessed) : 0);
            result.put("timestamp", System.currentTimeMillis());

            logger.info("性能测试完成: 总数={}, 成功={}, 失败={}, 耗时={}ms, TPS={}", 
                       totalProcessed, successCount.get(), failCount.get(), totalTime, 
                       totalTime > 0 ? (totalProcessed * 1000.0 / totalTime) : 0);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("性能测试失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "性能测试失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 生成测试消息
     * @param index 索引
     * @return 测试消息JSON
     */
    private String generateTestMessage(int index) {
        JSONObject message = new JSONObject();
        JSONObject personCheck = new JSONObject();
        
        // 模拟人员信息
        personCheck.put("PersonCode", "STU" + String.format("%06d", (index % 10000) + 1));
        personCheck.put("Name", "测试学生" + ((index % 10000) + 1));
        personCheck.put("DeviceID", "DEVICE_" + String.format("%03d", (index % 10) + 1));
        personCheck.put("DeviceName", "测试设备" + ((index % 10) + 1));
        personCheck.put("AreaCode", "AREA_" + String.format("%02d", (index % 5) + 1));
        personCheck.put("AreaName", "测试区域" + ((index % 5) + 1));
        
        // 随机进出方向 (1=进入, 2=离开)
        personCheck.put("InOrOut", random.nextBoolean() ? 1 : 2);
        
        // 当前时间
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        personCheck.put("CheckTime", currentTime);
        personCheck.put("PassTime", currentTime);
        
        // 其他信息
        personCheck.put("MatchConfidence", 85 + random.nextInt(15)); // 85-99的匹配置信度
        personCheck.put("DeviceIP", "192.168.1." + (100 + (index % 50)));
        personCheck.put("Temperature", 36.0 + random.nextDouble() * 1.5); // 36-37.5度体温

        message.put("PersonCheckObjectList", new Object[]{personCheck});
        
        return message.toJSONString();
    }

    /**
     * 批量生成测试用户
     * @param startIndex 起始索引
     * @param count 用户数量
     * @return 生成结果
     */
    @PostMapping("/generate-test-users")
    public ResponseEntity<Map<String, Object>> generateTestUsers(
            @RequestParam(defaultValue = "1") int startIndex,
            @RequestParam(defaultValue = "100") int count) {

        if (count > 1000) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "生成用户数量不能超过1000");
            return ResponseEntity.badRequest().body(error);
        }

        try {
            logger.info("开始生成测试用户: startIndex={}, count={}", startIndex, count);
            
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO tbl_person (code, name, sex, status, phone, department) VALUES ");
            
            for (int i = 0; i < count; i++) {
                int userIndex = startIndex + i;
                if (i > 0) sql.append(", ");
                
                sql.append(String.format("('STU%06d', '测试学生%d', %d, 1, '138%08d', 'TEST_DEPT')", 
                          userIndex, userIndex, (userIndex % 2) + 1, userIndex));
            }
            
            sql.append(" ON CONFLICT (code) DO NOTHING");
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "测试用户生成SQL已准备");
            result.put("sql", sql.toString());
            result.put("startIndex", startIndex);
            result.put("count", count);
            result.put("endIndex", startIndex + count - 1);
            result.put("note", "请手动执行SQL到FastGate数据库的tbl_person表");
            
            logger.info("测试用户SQL生成完成: {}条用户", count);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("生成测试用户失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "生成测试用户失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 压力测试 - 持续发送消息
     * @param duration 持续时间(秒)
     * @param tps 目标TPS
     * @return 测试结果
     */
    @PostMapping("/stress-test")
    public ResponseEntity<Map<String, Object>> stressTest(
            @RequestParam(defaultValue = "60") int duration,
            @RequestParam(defaultValue = "100") int tps) {

        if (duration > 300) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "压力测试时间不能超过300秒");
            return ResponseEntity.badRequest().body(error);
        }

        if (tps > 5000) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "目标TPS不能超过5000");
            return ResponseEntity.badRequest().body(error);
        }

        try {
            logger.info("开始压力测试: 持续时间={}秒, 目标TPS={}", duration, tps);
            
            AtomicInteger totalSent = new AtomicInteger(0);
            AtomicInteger totalSuccess = new AtomicInteger(0);
            AtomicInteger totalFailed = new AtomicInteger(0);
            
            long startTime = System.currentTimeMillis();
            long endTime = startTime + duration * 1000L;
            
            CompletableFuture<Void> stressTask = CompletableFuture.runAsync(() -> {
                int intervalMs = 1000 / tps; // 每条消息的间隔时间
                int messageIndex = 0;
                
                while (System.currentTimeMillis() < endTime) {
                    try {
                        String testMessage = generateTestMessage(messageIndex++);
                        boolean result = messageProcessService.processMessage(testMessage);
                        
                        totalSent.incrementAndGet();
                        if (result) {
                            totalSuccess.incrementAndGet();
                        } else {
                            totalFailed.incrementAndGet();
                        }
                        
                        if (intervalMs > 0) {
                            Thread.sleep(intervalMs);
                        }
                        
                    } catch (Exception e) {
                        totalFailed.incrementAndGet();
                        logger.error("压力测试消息处理失败: {}", e.getMessage());
                    }
                }
            }, testExecutor);

            // 等待测试完成
            stressTask.join();
            
            long actualDuration = System.currentTimeMillis() - startTime;
            
            Map<String, Object> result = new HashMap<>();
            result.put("plannedDuration", duration);
            result.put("actualDuration", actualDuration);
            result.put("targetTPS", tps);
            result.put("totalSent", totalSent.get());
            result.put("totalSuccess", totalSuccess.get());
            result.put("totalFailed", totalFailed.get());
            result.put("actualTPS", actualDuration > 0 ? (totalSent.get() * 1000.0 / actualDuration) : 0);
            result.put("successRate", totalSent.get() > 0 ? (totalSuccess.get() * 100.0 / totalSent.get()) : 0);
            result.put("timestamp", System.currentTimeMillis());

            logger.info("压力测试完成: 发送={}, 成功={}, 失败={}, 实际TPS={}", 
                       totalSent.get(), totalSuccess.get(), totalFailed.get(),
                       actualDuration > 0 ? (totalSent.get() * 1000.0 / actualDuration) : 0);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("压力测试失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "压力测试失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 清理测试数据
     * @return 清理结果
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupTestData() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("message", "测试数据清理SQL已准备");
            result.put("sqlCommands", new String[]{
                "-- 清理Redis缓存 (需要手动执行)",
                "FLUSHDB",
                "",
                "-- 清理队列表",
                "DELETE FROM access_record_queue WHERE person_code LIKE 'STU%';",
                "",
                "-- 清理FastGate测试数据 (需要手动执行)",
                "DELETE FROM tbl_access_control_record WHERE person_code LIKE 'STU%';",
                "DELETE FROM tbl_person WHERE code LIKE 'STU%';"
            });
            result.put("note", "请根据需要手动执行相应的清理SQL");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("准备清理SQL失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", "准备清理SQL失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
}