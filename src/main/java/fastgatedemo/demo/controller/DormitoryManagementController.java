package fastgatedemo.demo.controller;

import fastgatedemo.demo.service.DormitoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description 寝室管理控制器
 * 提供寝室管理相关的API接口，包括楼栋、楼层、寝室信息查询
 */
@RestController
@RequestMapping("/api/dormitory-management")
@CrossOrigin(origins = "*")
public class DormitoryManagementController {

    private static final Logger logger = LoggerFactory.getLogger(DormitoryManagementController.class);

    @Autowired
    private DormitoryService dormitoryService;

    /**
     * 获取所有楼栋列表
     * @return 楼栋列表，包含楼栋代码、名称、寝室数量、楼层数量
     */
    @GetMapping("/buildings")
    public ResponseEntity<Map<String, Object>> getBuildings() {
        try {
            logger.info("开始获取楼栋列表");
            List<Map<String, Object>> buildings = dormitoryService.getAllBuildings();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", buildings);
            response.put("message", "获取楼栋列表成功");
            response.put("count", buildings.size());
            
            logger.info("获取楼栋列表成功，共{}个楼栋", buildings.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取楼栋列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取楼栋列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取指定楼栋的楼层列表
     * @param buildingCode 楼栋代码
     * @return 楼层列表，包含楼层号、楼层名称、寝室数量
     */
    @GetMapping("/buildings/{buildingCode}/floors")
    public ResponseEntity<Map<String, Object>> getBuildingFloors(@PathVariable String buildingCode) {
        try {
            logger.info("开始获取楼栋{}的楼层列表", buildingCode);
            List<Map<String, Object>> floors = dormitoryService.getBuildingFloors(buildingCode);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", floors);
            response.put("message", "获取楼层列表成功");
            response.put("buildingCode", buildingCode);
            response.put("count", floors.size());
            
            logger.info("获取楼栋{}的楼层列表成功，共{}个楼层", buildingCode, floors.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取楼栋{}的楼层列表失败", buildingCode, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取楼层列表失败: " + e.getMessage());
            response.put("buildingCode", buildingCode);
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取指定楼栋楼层的寝室列表（含人员信息）
     * @param buildingCode 楼栋代码
     * @param floor 楼层号
     * @return 寝室列表，包含寝室信息和人员详情
     */
    @GetMapping("/buildings/{buildingCode}/floors/{floor}/dormitories")
    public ResponseEntity<Map<String, Object>> getFloorDormitories(
            @PathVariable String buildingCode,
            @PathVariable Integer floor) {
        try {
            logger.info("开始获取楼栋{}第{}层的寝室列表", buildingCode, floor);
            List<Map<String, Object>> dormitories = dormitoryService.getFloorDormitoriesWithPersons(buildingCode, floor);
            
            // 统计信息
            int totalDormitories = dormitories.size();
            int totalBeds = dormitories.stream()
                    .mapToInt(d -> (Integer) d.get("bedCount"))
                    .sum();
            int occupiedBeds = dormitories.stream()
                    .mapToInt(d -> (Integer) d.get("currentPersonCount"))
                    .sum();
            double occupancyRate = totalBeds > 0 ? (double) occupiedBeds / totalBeds * 100 : 0;
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", dormitories);
            response.put("message", "获取寝室列表成功");
            response.put("buildingCode", buildingCode);
            response.put("floor", floor);
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalDormitories", totalDormitories);
            statistics.put("totalBeds", totalBeds);
            statistics.put("occupiedBeds", occupiedBeds);
            statistics.put("availableBeds", totalBeds - occupiedBeds);
            statistics.put("occupancyRate", Math.round(occupancyRate * 100.0) / 100.0);
            response.put("statistics", statistics);
            
            logger.info("获取楼栋{}第{}层寝室列表成功，共{}间寝室，入住率{:.1f}%", 
                    buildingCode, floor, totalDormitories, occupancyRate);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取楼栋{}第{}层寝室列表失败", buildingCode, floor, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取寝室列表失败: " + e.getMessage());
            response.put("buildingCode", buildingCode);
            response.put("floor", floor);
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取寝室管理统计信息
     * @return 全局统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getManagementStatistics() {
        try {
            logger.info("开始获取寝室管理统计信息");
            Map<String, Object> statistics = dormitoryService.getDormitoryManagementStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "获取统计信息成功");
            
            logger.info("获取寝室管理统计信息成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取寝室管理统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
} 