package fastgatedemo.demo.controller;

import fastgatedemo.demo.config.FastGateConf;
import fastgatedemo.demo.config.UnifiedMockConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @description 配置测试控制器
 * 用于验证配置Bean是否正确注入和工作
 */
@RestController
@RequestMapping("/api/config")
public class ConfigTestController {

    @Autowired
    private FastGateConf fastGateConf;

    @Autowired
    private UnifiedMockConfig unifiedMockConfig;

    /**
     * 测试FastGate配置
     */
    @GetMapping("/fastgate")
    public Map<String, Object> testFastGateConfig() {
        Map<String, Object> result = new HashMap<>();
        result.put("url", fastGateConf.getUrl());
        result.put("user", fastGateConf.getUser());
        result.put("picPath", fastGateConf.getPicPath());
        result.put("personUrl", fastGateConf.getPersonUrl());
        result.put("groupBindUrl", fastGateConf.getGroupBindUrl());
        return result;
    }

    /**
     * 获取配置状态信息
     */
    @GetMapping("/status")
    public Map<String, Object> getConfigStatus() {
        Map<String, Object> result = new HashMap<>();
        result.put("fastGateConfigOK", fastGateConf != null);
        result.put("unifiedMockConfigOK", unifiedMockConfig != null);
        result.put("message", "配置文件架构优化完成，所有配置Bean正常注入");
        return result;
    }

    /**
     * 诊断Profile与数据源配置一致性
     */
    @GetMapping("/diagnose")
    public Map<String, Object> diagnoseConfig() {
        Map<String, Object> result = new HashMap<>();
        
        // 这里需要注入Environment和DataSource来检查实际配置
        result.put("message", "Profile与数据源配置诊断");
        result.put("recommendation", "请检查外部配置文件是否正确使用Profile机制");
        result.put("solution", "移除外部配置文件中的直接数据源配置，使用Profile特定配置段");
        
        return result;
    }
} 