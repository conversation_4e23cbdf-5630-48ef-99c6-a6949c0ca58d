/**
 * Copyright (C),  2011-2018, ZheJiang Uniview Technologies Co., Ltd. All rights reserved.
 * <http://www.uniview.com/>
 * <p>
 * FileName : BaseConstant
 * Author   : zW4705
 * Date     : 2018/9/19 15:00
 * DESCRIPTION: 速通门平台请求接口地址
 * <p>
 * History:
 * DATE        NAME        DESC
 */
package fastgatedemo.demo.constant;

/**
 * @description: 速通门平台请求接口地址
 */
public class BaseConstant {

    /**
     * 用户登录请求接口
     */
    public static final String LOGIN_URL = "/user/login";

    /**
     * 查询权限组接口
     */
    public static final String QUERY_GROUPS = "/groups?sort=desc&conditionParam=";

    /**
     * 查询门禁权限组下设备接口,url后需加具体权限组 id
     */
    public static final String QUERY_GROUP_DEVICES = "/group/";

    /**
     * 为人员绑定默认门禁权限组
     */
    public static final String URL_PERSON_GROUP = "/personGroup/";

    /**
     * 人员接口
     */
    public static final String URL_PERSON = "/person";

    /**
     * 根据personCode查询人员信息
     */
    public static final String URL_PERSON_CODE = "/personCode/";

    /**
     * 分页查询人员接口
     */
    public static final String URL_PERSONS_QUERY = "/persons";

    /**
     * 宿舍接口
     */
    public static final String URL_DORMITORY = "/dormitory";

    /**
     * 出入记录查询接口
     */
    public static final String QUERY_ACCESS_RECORD = "/accessRecords?conditionParam=";

    /**
     * 查询时间模板接口
     */
    public static final String QUERY_TEMPLATES = "/templates?conditionParam=";

    /**
     * 请求通用格式
     */
    public static final String CONTENT_TYPE_JSON = "application/json";

    /**
     * utf-8
     */
    public static final String CHARSET_UTF8 = "UTF-8";

    /**
     * CONTENT_TYPE
     */
    public static final String CONTENT_TYPE = "Content-type";

    /**
     * CONTENT_TYPE_FORMDATA
     */
    public static final String CONTENT_TYPE_FORMDATA = "multipart/form-data";
    /**
     * CONTENT_TYPE_TEXT_PLAIN
     */
    public static final String CONTENT_TYPE_TEXT_PLAIN = "text/plain";
    /**
     * 员工
     */
    public static final Integer PERSON_TYPE_6 = 6;
    /**
     * 默认门禁权限组
     */
    public static final Integer DEFAULT_GROUP = 0;
    /**
     * SUFFIX_JPG
     */
    public static final String SUFFIX_JPG = ".jpg";
    /**
     * MIME_TYPE_IMAGE_JPEG
     */
    public static final String MIME_TYPE_IMAGE_JPEG = "image/jpeg";
    /**
     * PART_NAME_FILE
     */
    public static final String PART_NAME_FILE = "file";

}
