package fastgatedemo.demo.config;

import fastgatedemo.demo.websocket.DashboardWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * @description WebSocket配置类
 * 配置大屏实时数据推送的WebSocket端点
 * <AUTHOR>
 * @date 2025-01-30
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private DashboardWebSocketHandler dashboardWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册大屏WebSocket端点
        registry.addHandler(dashboardWebSocketHandler, "/ws/dashboard/records")
                .setAllowedOrigins("*") // 允许跨域，生产环境建议配置具体域名
                .withSockJS(); // 启用SockJS支持，提高兼容性

        // 也注册不带SockJS的原生WebSocket端点
        registry.addHandler(dashboardWebSocketHandler, "/ws/dashboard/records/native")
                .setAllowedOrigins("*");
    }
}
