package fastgatedemo.demo.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description 统一的模拟数据配置管理类 - 简化版
 * <AUTHOR>
 * @date 2025-01-29
 */
@Component
public class UnifiedMockConfig {

    @Value("${app.mock.enabled:false}")
    private boolean globalMockEnabled;

    @Value("${app.mock.mode:development}")
    private String mockMode;

    // ==================== 全局开关方法 ====================

    /**
     * 判断是否启用模拟数据
     * @return true-启用模拟数据，false-使用真实接口
     */
    public boolean isMockEnabled() {
        return globalMockEnabled;
    }

    /**
     * 获取模拟数据模式
     * @return 模拟数据模式
     */
    public String getMockMode() {
        return mockMode;
    }

    /**
     * 是否启用开发模式
     * @return true-开发模式，false-其他模式
     */
    public boolean isDevelopmentMode() {
        return "development".equals(mockMode);
    }

    // ==================== 简化的配置方法 ====================

    /**
     * 是否启用FastGate模拟
     * @return true-启用，false-禁用
     */
    public boolean isFastgateMockEnabled() {
        return globalMockEnabled;
    }

    /**
     * 是否启用数据库模拟
     * @return true-启用，false-禁用
     */
    public boolean isDatabaseMockEnabled() {
        return globalMockEnabled;
    }

    /**
     * 是否启用文件模拟
     * @return true-启用，false-禁用
     */
    public boolean isFileMockEnabled() {
        return globalMockEnabled;
    }

    /**
     * 获取FastGate响应延迟（毫秒）
     * @return 响应延迟
     */
    public int getFastgateResponseDelay() {
        return 200;
    }

    /**
     * 获取FastGate成功率
     * @return 成功率
     */
    public double getFastgateSuccessRate() {
        return 0.98;
    }

    /**
     * 获取模拟人员数量
     * @return 人员数量
     */
    public int getPersonCount() {
        return 100;
    }

    /**
     * 获取学生比例
     * @return 学生比例
     */
    public double getStudentRatio() {
        return 0.8;
    }

    /**
     * 获取教师比例
     * @return 教师比例
     */
    public double getTeacherRatio() {
        return 0.2;
    }

    /**
     * 获取宿舍楼数量
     * @return 宿舍楼数量
     */
    public int getBuildingCount() {
        return 5;
    }

    /**
     * 获取楼层数量
     * @return 楼层数量
     */
    public int getFloorCount() {
        return 3;
    }

    /**
     * 获取每层房间数
     * @return 每层房间数
     */
    public int getRoomPerFloor() {
        return 10;
    }

    /**
     * 获取每房间床位数
     * @return 每房间床位数
     */
    public int getBedPerRoom() {
        return 4;
    }

    /**
     * 获取日通行记录数量
     * @return 日通行记录数量
     */
    public int getDailyEntryRecordCount() {
        return 200;
    }

    /**
     * 获取通行记录成功率
     * @return 成功率
     */
    public double getEntryRecordSuccessRate() {
        return 0.92;
    }

    /**
     * 是否启用人脸识别模拟
     * @return true-启用，false-禁用
     */
    public boolean isFaceRecognitionEnabled() {
        return true;
    }

    /**
     * 获取人脸识别准确率
     * @return 准确率
     */
    public double getFaceRecognitionAccuracy() {
        return 0.88;
    }

    /**
     * 获取人脸识别响应时间
     * @return 响应时间（毫秒）
     */
    public int getFaceRecognitionResponseTime() {
        return 150;
    }

    /**
     * 是否启用网络错误模拟
     * @return true-启用，false-禁用
     */
    public boolean isNetworkErrorEnabled() {
        return false;
    }

    /**
     * 获取网络错误率
     * @return 错误率
     */
    public double getNetworkErrorRate() {
        return 0.05;
    }

    /**
     * 是否启用数据库错误模拟
     * @return true-启用，false-禁用
     */
    public boolean isDatabaseErrorEnabled() {
        return false;
    }

    /**
     * 获取数据库错误率
     * @return 错误率
     */
    public double getDatabaseErrorRate() {
        return 0.02;
    }

    /**
     * 获取随机延迟时间
     * @return 延迟时间（毫秒）
     */
    public int getRandomDelay() {
        int min = 100;
        int max = 500;
        return min + (int) (Math.random() * (max - min));
    }

    /**
     * 获取成功率
     * @return 成功率
     */
    public double getSuccessRate() {
        return 0.95;
    }

    /**
     * 是否启用缓存
     * @return true-启用，false-禁用
     */
    public boolean isCacheEnabled() {
        return true;
    }

    /**
     * 获取缓存TTL
     * @return TTL（秒）
     */
    public int getCacheTtl() {
        return 300;
    }

    /**
     * 是否启用性能监控
     * @return true-启用，false-禁用
     */
    public boolean isPerformanceEnabled() {
        return true;
    }

    /**
     * 获取慢查询阈值
     * @return 阈值（毫秒）
     */
    public int getSlowQueryThreshold() {
        return 1000;
    }

    // ==================== 其他工具方法 ====================

    /**
     * 根据成功率判断是否成功
     * @param successRate 成功率
     * @return true-成功，false-失败
     */
    public boolean isSuccess(double successRate) {
        return Math.random() < successRate;
    }

    /**
     * 根据默认成功率判断是否成功
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return isSuccess(getSuccessRate());
    }

    /**
     * 模拟网络延迟
     * @param baseDelay 基础延迟
     */
    public void simulateNetworkDelay(int baseDelay) {
        try {
            Thread.sleep(baseDelay + (int) (Math.random() * 100));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 模拟随机延迟
     */
    public void simulateRandomDelay() {
        simulateNetworkDelay(getRandomDelay());
    }
}