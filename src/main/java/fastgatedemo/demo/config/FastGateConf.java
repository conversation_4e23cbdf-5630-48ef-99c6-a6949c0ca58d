package fastgatedemo.demo.config;

import fastgatedemo.demo.constant.BaseConstant;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 *@description 速通门平台配置信息获取
 * 从YAML配置文件中读取FastGate和文件存储相关配置
 */
@ConfigurationProperties(prefix = "fastgate")
public class FastGateConf {

    private String passwd;
    private String user;
    private String url;
    
    // 人员图片路径配置（使用嵌套配置）
    private PersonConfig person = new PersonConfig();
    
    public static class PersonConfig {
        private String picPath;
        
        public String getPicPath() {
            return picPath;
        }
        
        public void setPicPath(String picPath) {
            this.picPath = picPath;
        }
    }
    /**
     * 人员信息接口url
     */
    private String personUrl;
    /**
     * 人员与权限组绑定url
     */
    private String groupBindUrl;

    /**
     * 密码进行MD5加密
     * @return MD5加密后的密码
     */
    public String getPasswd() {
        return DigestUtils.md5Hex(passwd);
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPicPath() {
        return person.getPicPath();
    }

    public void setPicPath(String picPath) {
        this.person.setPicPath(picPath);
    }
    
    public PersonConfig getPerson() {
        return person;
    }
    
    public void setPerson(PersonConfig person) {
        this.person = person;
    }

    public String getPersonUrl() {
        return url + BaseConstant.URL_PERSON;
    }

    public void setPersonUrl(String personUrl) {
        this.personUrl = personUrl;
    }

    public String getGroupBindUrl() {
        return url + BaseConstant.URL_PERSON_GROUP;
    }

    public void setGroupBindUrl(String groupBindUrl) {
        this.groupBindUrl = groupBindUrl;
    }
}
