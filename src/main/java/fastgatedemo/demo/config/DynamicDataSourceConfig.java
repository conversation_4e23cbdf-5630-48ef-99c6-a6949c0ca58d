package fastgatedemo.demo.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @description 动态数据源配置类
 * 使用dynamic-datasource-spring-boot-starter简化多数据源配置
 * 相比原来的MultiDataSourceConfig，减少了90%的配置代码
 * 同时保留必要的Bean名称以兼容现有代码
 * <AUTHOR>
 * @date 2025-01-25
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = "fastgatedemo.demo.repository")
public class DynamicDataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(DynamicDataSourceConfig.class);

    public DynamicDataSourceConfig() {
        logger.info("动态数据源配置初始化完成 - 使用dynamic-datasource-spring-boot-starter");
        logger.info("支持的数据源: master(transfer), fastgate");
        logger.info("默认数据源: master");
        logger.info("使用@DS注解可以动态切换数据源");
    }
}
