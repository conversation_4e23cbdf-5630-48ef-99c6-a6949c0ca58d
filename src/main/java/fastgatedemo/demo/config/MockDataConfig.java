package fastgatedemo.demo.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * @description 模拟数据配置类
 * <AUTHOR>
 * @date 2025-01-25
 */
@Component
@ConfigurationProperties(prefix = "app.mock")
public class MockDataConfig {

    // ==================== 全局模拟开关 ====================
    @Value("${app.mock.enabled:false}")
    private boolean enabled;

    @Value("${app.mock.mode:development}")
    private String mode;

    // ==================== 接口模拟配置 ====================
    @Value("${mock.fastgate.enabled:false}")
    private boolean fastgateEnabled;

    @Value("${mock.fastgate.response.delay:200}")
    private int fastgateResponseDelay;

    @Value("${mock.fastgate.success.rate:0.98}")
    private double fastgateSuccessRate;

    @Value("${mock.database.enabled:false}")
    private boolean databaseEnabled;

    @Value("${mock.database.response.delay:50}")
    private int databaseResponseDelay;

    @Value("${mock.file.enabled:false}")
    private boolean fileEnabled;

    @Value("${mock.file.response.delay:100}")
    private int fileResponseDelay;

    @Value("${mock.file.upload.success.rate:0.95}")
    private double fileUploadSuccessRate;

    // ==================== 模拟数据内容配置 ====================
    @Value("${mock.person.count:100}")
    private int personCount;

    @Value("${mock.person.student.ratio:0.8}")
    private double studentRatio;

    @Value("${mock.person.teacher.ratio:0.2}")
    private double teacherRatio;

    @Value("${mock.dormitory.building.count:5}")
    private int buildingCount;

    @Value("${mock.dormitory.floor.count:3}")
    private int floorCount;

    @Value("${mock.dormitory.room.per.floor:10}")
    private int roomPerFloor;

    @Value("${mock.dormitory.bed.per.room:4}")
    private int bedPerRoom;

    @Value("${mock.entry.record.daily.count:200}")
    private int entryRecordDailyCount;

    @Value("${mock.entry.record.success.rate:0.92}")
    private double entryRecordSuccessRate;

    // ==================== 人脸识别模拟配置 ====================
    @Value("${mock.face.recognition.enabled:true}")
    private boolean faceRecognitionEnabled;

    @Value("${mock.face.recognition.accuracy:0.88}")
    private double faceRecognitionAccuracy;

    @Value("${mock.face.recognition.response.time:150}")
    private int faceRecognitionResponseTime;

    // ==================== 错误模拟配置 ====================
    @Value("${mock.error.network.enabled:false}")
    private boolean networkErrorEnabled;

    @Value("${mock.error.network.rate:0.05}")
    private double networkErrorRate;

    @Value("${mock.error.database.enabled:false}")
    private boolean databaseErrorEnabled;

    @Value("${mock.error.database.rate:0.02}")
    private double databaseErrorRate;

    @Value("${mock.error.business.enabled:false}")
    private boolean businessErrorEnabled;

    @Value("${mock.error.business.rate:0.03}")
    private double businessErrorRate;

    // ==================== 性能配置 ====================
    @Value("${mock.response.delay.min:100}")
    private int responseDelayMin;

    @Value("${mock.response.delay.max:500}")
    private int responseDelayMax;

    @Value("${mock.success.rate:0.95}")
    private double successRate;

    @Value("${mock.cache.enabled:true}")
    private boolean cacheEnabled;

    @Value("${mock.cache.ttl:300}")
    private int cacheTtl;

    // ==================== 工具方法 ====================
    
    /**
     * 判断是否启用模拟数据
     * @return true-启用模拟数据，false-使用真实接口
     */
    public boolean isMockEnabled() {
        return enabled;
    }

    /**
     * 判断是否为开发模式
     * @return true-开发模式，false-生产模式
     */
    public boolean isDevelopmentMode() {
        return "development".equalsIgnoreCase(mode);
    }

    /**
     * 判断FastGate接口是否启用模拟
     * @return true-使用模拟数据，false-调用真实接口
     */
    public boolean shouldMockFastgate() {
        return enabled && fastgateEnabled;
    }

    /**
     * 判断数据库操作是否启用模拟
     * @return true-使用模拟数据，false-真实数据库操作
     */
    public boolean shouldMockDatabase() {
        return enabled && databaseEnabled;
    }

    /**
     * 判断文件操作是否启用模拟
     * @return true-使用模拟操作，false-真实文件操作
     */
    public boolean shouldMockFile() {
        return enabled && fileEnabled;
    }

    /**
     * 获取随机响应延迟时间
     * @return 延迟时间（毫秒）
     */
    public int getRandomResponseDelay() {
        if (responseDelayMin >= responseDelayMax) {
            return responseDelayMin;
        }
        return responseDelayMin + (int) (Math.random() * (responseDelayMax - responseDelayMin));
    }

    /**
     * 根据成功率判断操作是否成功
     * @param successRate 成功率（0.0-1.0）
     * @return true-成功，false-失败
     */
    public boolean isOperationSuccess(double successRate) {
        return Math.random() < successRate;
    }

    /**
     * 根据默认成功率判断操作是否成功
     * @return true-成功，false-失败
     */
    public boolean isOperationSuccess() {
        return isOperationSuccess(successRate);
    }

    // ==================== Getter and Setter 方法 ====================

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public boolean isFastgateEnabled() {
        return fastgateEnabled;
    }

    public void setFastgateEnabled(boolean fastgateEnabled) {
        this.fastgateEnabled = fastgateEnabled;
    }

    public int getFastgateResponseDelay() {
        return fastgateResponseDelay;
    }

    public void setFastgateResponseDelay(int fastgateResponseDelay) {
        this.fastgateResponseDelay = fastgateResponseDelay;
    }

    public double getFastgateSuccessRate() {
        return fastgateSuccessRate;
    }

    public void setFastgateSuccessRate(double fastgateSuccessRate) {
        this.fastgateSuccessRate = fastgateSuccessRate;
    }

    public boolean isDatabaseEnabled() {
        return databaseEnabled;
    }

    public void setDatabaseEnabled(boolean databaseEnabled) {
        this.databaseEnabled = databaseEnabled;
    }

    public int getDatabaseResponseDelay() {
        return databaseResponseDelay;
    }

    public void setDatabaseResponseDelay(int databaseResponseDelay) {
        this.databaseResponseDelay = databaseResponseDelay;
    }

    public boolean isFileEnabled() {
        return fileEnabled;
    }

    public void setFileEnabled(boolean fileEnabled) {
        this.fileEnabled = fileEnabled;
    }

    public int getFileResponseDelay() {
        return fileResponseDelay;
    }

    public void setFileResponseDelay(int fileResponseDelay) {
        this.fileResponseDelay = fileResponseDelay;
    }

    public double getFileUploadSuccessRate() {
        return fileUploadSuccessRate;
    }

    public void setFileUploadSuccessRate(double fileUploadSuccessRate) {
        this.fileUploadSuccessRate = fileUploadSuccessRate;
    }

    public int getPersonCount() {
        return personCount;
    }

    public void setPersonCount(int personCount) {
        this.personCount = personCount;
    }

    public double getStudentRatio() {
        return studentRatio;
    }

    public void setStudentRatio(double studentRatio) {
        this.studentRatio = studentRatio;
    }

    public double getTeacherRatio() {
        return teacherRatio;
    }

    public void setTeacherRatio(double teacherRatio) {
        this.teacherRatio = teacherRatio;
    }

    public int getBuildingCount() {
        return buildingCount;
    }

    public void setBuildingCount(int buildingCount) {
        this.buildingCount = buildingCount;
    }

    public int getFloorCount() {
        return floorCount;
    }

    public void setFloorCount(int floorCount) {
        this.floorCount = floorCount;
    }

    public int getRoomPerFloor() {
        return roomPerFloor;
    }

    public void setRoomPerFloor(int roomPerFloor) {
        this.roomPerFloor = roomPerFloor;
    }

    public int getBedPerRoom() {
        return bedPerRoom;
    }

    public void setBedPerRoom(int bedPerRoom) {
        this.bedPerRoom = bedPerRoom;
    }

    public int getEntryRecordDailyCount() {
        return entryRecordDailyCount;
    }

    public void setEntryRecordDailyCount(int entryRecordDailyCount) {
        this.entryRecordDailyCount = entryRecordDailyCount;
    }

    public double getEntryRecordSuccessRate() {
        return entryRecordSuccessRate;
    }

    public void setEntryRecordSuccessRate(double entryRecordSuccessRate) {
        this.entryRecordSuccessRate = entryRecordSuccessRate;
    }

    public boolean isFaceRecognitionEnabled() {
        return faceRecognitionEnabled;
    }

    public void setFaceRecognitionEnabled(boolean faceRecognitionEnabled) {
        this.faceRecognitionEnabled = faceRecognitionEnabled;
    }

    public double getFaceRecognitionAccuracy() {
        return faceRecognitionAccuracy;
    }

    public void setFaceRecognitionAccuracy(double faceRecognitionAccuracy) {
        this.faceRecognitionAccuracy = faceRecognitionAccuracy;
    }

    public int getFaceRecognitionResponseTime() {
        return faceRecognitionResponseTime;
    }

    public void setFaceRecognitionResponseTime(int faceRecognitionResponseTime) {
        this.faceRecognitionResponseTime = faceRecognitionResponseTime;
    }

    public boolean isNetworkErrorEnabled() {
        return networkErrorEnabled;
    }

    public void setNetworkErrorEnabled(boolean networkErrorEnabled) {
        this.networkErrorEnabled = networkErrorEnabled;
    }

    public double getNetworkErrorRate() {
        return networkErrorRate;
    }

    public void setNetworkErrorRate(double networkErrorRate) {
        this.networkErrorRate = networkErrorRate;
    }

    public boolean isDatabaseErrorEnabled() {
        return databaseErrorEnabled;
    }

    public void setDatabaseErrorEnabled(boolean databaseErrorEnabled) {
        this.databaseErrorEnabled = databaseErrorEnabled;
    }

    public double getDatabaseErrorRate() {
        return databaseErrorRate;
    }

    public void setDatabaseErrorRate(double databaseErrorRate) {
        this.databaseErrorRate = databaseErrorRate;
    }

    public boolean isBusinessErrorEnabled() {
        return businessErrorEnabled;
    }

    public void setBusinessErrorEnabled(boolean businessErrorEnabled) {
        this.businessErrorEnabled = businessErrorEnabled;
    }

    public double getBusinessErrorRate() {
        return businessErrorRate;
    }

    public void setBusinessErrorRate(double businessErrorRate) {
        this.businessErrorRate = businessErrorRate;
    }

    public int getResponseDelayMin() {
        return responseDelayMin;
    }

    public void setResponseDelayMin(int responseDelayMin) {
        this.responseDelayMin = responseDelayMin;
    }

    public int getResponseDelayMax() {
        return responseDelayMax;
    }

    public void setResponseDelayMax(int responseDelayMax) {
        this.responseDelayMax = responseDelayMax;
    }

    public double getSuccessRate() {
        return successRate;
    }

    public void setSuccessRate(double successRate) {
        this.successRate = successRate;
    }

    public boolean isCacheEnabled() {
        return cacheEnabled;
    }

    public void setCacheEnabled(boolean cacheEnabled) {
        this.cacheEnabled = cacheEnabled;
    }

    public int getCacheTtl() {
        return cacheTtl;
    }

    public void setCacheTtl(int cacheTtl) {
        this.cacheTtl = cacheTtl;
    }
} 