package fastgatedemo.demo.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import fastgatedemo.demo.config.FastGateConf;
import fastgatedemo.demo.constant.BaseConstant;
import fastgatedemo.demo.model.DepartPerson;
import fastgatedemo.demo.model.MiddlewareFacePhoto;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.model.UniResult;
import fastgatedemo.demo.repository.MiddlewareFacePhotoRepository;
import fastgatedemo.demo.repository.PersonRepository;
import fastgatedemo.demo.util.ImageUtils;
import fastgatedemo.demo.util.UniHttpUtil;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.io.IOException;
import java.util.Optional;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * @description 速通门人员信息处理
 */
@Service
public class PersonService {

    /**
     * 日志文件
     */
    private static Logger logger = LoggerFactory.getLogger(PersonService.class);

    @Resource
    FastGateConf fastGateConf;

    @Autowired
    private MiddlewareFacePhotoRepository middlewareFacePhotoRepository;

    @Autowired
    private PersonRepository personRepository;

    @PersistenceContext
    private EntityManager entityManager;



    /**
     * 添加人员信息（带智能认证机制）
     *
     * @param personInfo
     * @return 是否成功添加
     */
    public boolean addPerson(PersonInfo personInfo) {
        try {
            // 检查并补充人脸照片数据
            ensurePersonPicture(personInfo);
            
            // 第一次尝试插入人员信息
            UniResult uniResult = callInsertPersonAPI(personInfo);

            // 检查是否为认证失败（ErrCode=1010表示登录状态已失效）
            if (isAuthenticationFailure(uniResult)) {
                logger.warn("EGS认证可能失败，尝试重新登录后重试...personCode:{}", personInfo.getPersonCode());

                // 重新登录EGS平台
                if (reLogin()) {
                    logger.info("重新登录成功，重试人员添加API调用: personCode={}", personInfo.getPersonCode());
                    // 重试API调用
                    uniResult = callInsertPersonAPI(personInfo);
                } else {
                    logger.error("重新登录失败，人员添加失败: personCode={}", personInfo.getPersonCode());
                    return false;
                }
            }

            // 检查插入结果
            if (null != uniResult && HttpStatus.SC_OK == uniResult.getErrCode()) {
                logger.debug("insert person success,personCode:{},personName:{}", personInfo.getPersonCode(), personInfo.getPersonName());
                
                // 为人员信息绑定默认权限组
                uniResult = callBindDefaultGroupAPI(personInfo);
                
                // 绑定权限组如果失败也尝试重新登录重试
                if (isAuthenticationFailure(uniResult)) {
                    logger.warn("权限组绑定认证失败，重试: personCode={}", personInfo.getPersonCode());
                    if (reLogin()) {
                        uniResult = callBindDefaultGroupAPI(personInfo);
                    }
                }
                
                if (HttpStatus.SC_OK == uniResult.getErrCode()) {
                    logger.debug("person bind default group success: personCode={}, uniResult={}", personInfo.getPersonCode(), uniResult);
                    return true; // 人员添加和权限组绑定都成功
                } else {
                    logger.error("person bind default group failed: personCode={}, uniResult={}", personInfo.getPersonCode(), uniResult);
                    return false; // 权限组绑定失败
                }
            } else {
                logger.error("insert person failed，personCode:{},personName:{},{}", personInfo.getPersonCode(), personInfo.getPersonName(), uniResult);
                return false; // 人员添加失败
            }
        } catch (Exception e) {
            logger.error("add person information exception ,personCode:{},{}", personInfo.getPersonCode(), e.getMessage(), e);
            return false; // 异常情况
        }
    }

    /**
     * 删除人员信息（带智能认证机制）
     *
     * @param personInfo
     */
    public void deletePerson(PersonInfo personInfo) {
        try {
            // 第一次尝试删除人员信息
            UniResult uniResult = callDeletePersonAPI(personInfo);

            // 检查是否为认证失败
            if (isAuthenticationFailure(uniResult)) {
                logger.warn("EGS认证可能失败，尝试重新登录后重试...personCode:{}", personInfo.getPersonCode());

                // 重新登录EGS平台
                if (reLogin()) {
                    logger.info("重新登录成功，重试人员删除API调用: personCode={}", personInfo.getPersonCode());
                    // 重试API调用
                    uniResult = callDeletePersonAPI(personInfo);
                } else {
                    logger.error("重新登录失败，人员删除失败: personCode={}", personInfo.getPersonCode());
                    return;
                }
            }

            if (null != uniResult && HttpStatus.SC_OK == uniResult.getErrCode()) {
                logger.debug("delete person success, personCode:{},personName:{}", personInfo.getPersonCode(), personInfo.getPersonName());
            } else {
                logger.error("delete person failed, personCode:{},personName:{},{}", personInfo.getPersonCode(), personInfo.getPersonName(), uniResult);
            }
        } catch (Exception e) {
            logger.error("delete person information exception,personCode:{},{}", personInfo.getPersonCode(), e.getMessage(), e);
        }
    }

    /**
     * 更新人员信息（带智能认证机制）
     *
     * @param personInfo
     */
    public boolean updatePerson(PersonInfo personInfo) {
        try {
            // 第一次尝试查询人员信息
            UniResult uniResult = callQueryPersonAPI(personInfo.getPersonCode());

            // 检查是否为认证失败
            if (isAuthenticationFailure(uniResult)) {
                logger.warn("EGS认证可能失败，尝试重新登录后重试...personCode:{}", personInfo.getPersonCode());

                // 重新登录EGS平台
                if (reLogin()) {
                    logger.info("重新登录成功，重试人员查询API调用: personCode={}", personInfo.getPersonCode());
                    // 重试API调用
                    uniResult = callQueryPersonAPI(personInfo.getPersonCode());
                } else {
                    logger.error("重新登录失败，人员更新失败: personCode={}", personInfo.getPersonCode());
                    return false;
                }
            }

            if (null == uniResult) {
                //当前数据库中没有对应人员信息, 直接新增
                boolean addResult = addPerson(personInfo);
                return addResult;
            }

            DepartPerson departPerson = JSONObject.parseObject(uniResult.getData().toString(), DepartPerson.class);
            
            // 第一次尝试更新人员信息
            uniResult = callUpdatePersonAPI(departPerson, personInfo);

            // 检查是否为认证失败
            if (isAuthenticationFailure(uniResult)) {
                logger.warn("人员更新认证失败，重试: personCode={}", personInfo.getPersonCode());
                if (reLogin()) {
                    uniResult = callUpdatePersonAPI(departPerson, personInfo);
                } else {
                    logger.error("重新登录失败，人员更新失败: personCode={}", personInfo.getPersonCode());
                    return false;
                }
            }

            if (HttpStatus.SC_OK == uniResult.getErrCode()) {
                logger.debug("update person info success,personCode:{}", personInfo.getPersonCode());
                return true;
            } else {
                logger.error("update person info failed,personCode:{},{}", departPerson.getCode(), uniResult);
                return false;
            }
        } catch (Exception e) {
            logger.error("update person info exception,personCode:{},{}", personInfo.getPersonCode(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 调用插入人员API
     *
     * @param personInfo 人员信息
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callInsertPersonAPI(PersonInfo personInfo) throws IOException {
        String url = fastGateConf.getPersonUrl();
        String picPath = fastGateConf.getPicPath();
        
        logger.info("调用插入人员API - URL: {}, 人员编码: {}, 人员姓名: {}, 图片路径: {}, 照片数据: {}字节", 
                   url, personInfo.getPersonCode(), personInfo.getPersonName(), picPath,
                   personInfo.getPersonPicture() != null ? personInfo.getPersonPicture().length : 0);
        
        UniResult result = UniHttpUtil.insertPerson(url, personInfo, picPath);
        
        logger.info("插入人员API调用完成 - 人员编码: {}, 返回码: {}, 返回消息: {}", 
                   personInfo.getPersonCode(), 
                   result != null ? result.getErrCode() : "null",
                   result != null ? result.getErrMsg() : "null");
        
        return result;
    }

    /**
     * 调用绑定默认权限组API
     *
     * @param personInfo 人员信息
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callBindDefaultGroupAPI(PersonInfo personInfo) throws IOException {
        String url = fastGateConf.getGroupBindUrl();
        
        logger.info("调用绑定权限组API - URL: {}, 人员编码: {}, 人员姓名: {}", 
                   url, personInfo.getPersonCode(), personInfo.getPersonName());
        
        UniResult result = UniHttpUtil.bindDefaultGroup(url, personInfo);
        
        logger.info("绑定权限组API调用完成 - 人员编码: {}, 返回码: {}, 返回消息: {}", 
                   personInfo.getPersonCode(), 
                   result != null ? result.getErrCode() : "null",
                   result != null ? result.getErrMsg() : "null");
        
        return result;
    }

    /**
     * 调用删除人员API
     *
     * @param personInfo 人员信息
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callDeletePersonAPI(PersonInfo personInfo) throws IOException {
        String url = fastGateConf.getPersonUrl();
        JSONArray delCodeArray = new JSONArray();
        delCodeArray.add(personInfo.getPersonCode());
        String requestBody = delCodeArray.toString();
        
        logger.info("调用删除人员API - URL: {}, 人员编码: {}, 请求体: {}", 
                   url, personInfo.getPersonCode(), requestBody);
        
        UniResult result = UniHttpUtil.sendHttpDeleteJson(url, requestBody);
        
        logger.info("删除人员API调用完成 - 人员编码: {}, 返回码: {}, 返回消息: {}", 
                   personInfo.getPersonCode(), 
                   result != null ? result.getErrCode() : "null",
                   result != null ? result.getErrMsg() : "null");
        
        return result;
    }

    /**
     * 调用查询人员API
     *
     * @param personCode 人员编码
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callQueryPersonAPI(String personCode) throws IOException {
        String queryPersonUrl = fastGateConf.getUrl() + BaseConstant.URL_PERSON_CODE + personCode;
        
        logger.info("调用查询人员API - URL: {}, 人员编码: {}", queryPersonUrl, personCode);
        
        UniResult result = UniHttpUtil.sendHttpGetJson(queryPersonUrl, null);
        
        logger.info("查询人员API调用完成 - 人员编码: {}, 返回码: {}, 返回消息: {}", 
                   personCode, 
                   result != null ? result.getErrCode() : "null",
                   result != null ? result.getErrMsg() : "null");
        
        return result;
    }

    /**
     * 调用更新人员API
     *
     * @param departPerson 部门人员信息
     * @param personInfo 人员信息
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callUpdatePersonAPI(DepartPerson departPerson, PersonInfo personInfo) throws IOException {
        String url = fastGateConf.getPersonUrl();
        
        logger.info("调用更新人员API（含人脸照片） - URL: {}, 人员编码: {}, 人员姓名: {}, 部门编码: {}, 照片数据: {}字节", 
                   url, personInfo.getPersonCode(), personInfo.getPersonName(), personInfo.getDepartmentCode(),
                   personInfo.getPersonPicture() != null ? personInfo.getPersonPicture().length : 0);
        
        // 打印详细的EGS平台调用参数
        logger.info("======== EGS更新人员API调用参数详情 ========");
        logger.info("请求URL: {}", url);
        logger.info("HTTP方法: PUT");
        logger.info("请求参数:");
        logger.info("  Seqid(唯一主键): {}", departPerson.getSeqid());
        logger.info("  Name(人员姓名): {}", departPerson.getName());
        logger.info("  Code(人员编号): {}", departPerson.getCode());
        logger.info("  Depart(部门编码): {}", !StringUtils.isEmpty(departPerson.getDepartCode()) ? departPerson.getDepartCode() : "iccsid");
        logger.info("  Telephone(联系电话): {}", departPerson.getTelephone());
        logger.info("  PerType(人员类型): {}", departPerson.getPerType() != null ? departPerson.getPerType() : -1);
        logger.info("  Sex(人员性别): {}", departPerson.getSex() != null ? departPerson.getSex() : 1);
        logger.info("  Cardtype(证件类型): {}", !StringUtils.isEmpty(departPerson.getCardtype()) ? departPerson.getCardtype() : "0");
        logger.info("  Idcard(身份证号): {}", departPerson.getIdcard());
        logger.info("  AreaCode(区域编码): {}", departPerson.getAreacode());
        logger.info("  ImageList(图片列表): \"\"(空字符串)");
        logger.info("  file(人脸照片): {}", personInfo.getPersonPicture() != null && personInfo.getPersonPicture().length > 0 
                   ? "有照片文件(" + personInfo.getPersonPicture().length + "字节)" : "无照片文件");
        logger.info("========================================");
        
        // 输出原始JSON数据
        logger.info("======== 原始JSON数据 ========");
        logger.info("DepartPerson JSON: {}", JSONObject.toJSONString(departPerson));
        logger.info("==============================");
        
        UniResult result = UniHttpUtil.updatePerson(url, departPerson, personInfo);
        
        logger.info("更新人员API调用完成 - 人员编码: {}, 返回码: {}, 返回消息: {}", 
                   personInfo.getPersonCode(), 
                   result != null ? result.getErrCode() : "null",
                   result != null ? result.getErrMsg() : "null");
        
        return result;
    }

    /**
     * 检查是否为认证失败
     *
     * @param result API调用结果
     * @return 是否为认证失败
     */
    private boolean isAuthenticationFailure(UniResult result) {
        if (result == null) {
            return false;
        }
        // 根据EGS错误码表：1010=登录状态已失效，404=资源不存在，401=认证相关
        // 30404=资源不存在（部门编码问题），不是认证失败
        return result.getErrCode() == 1010 || result.getErrCode() == 404 || result.getErrCode() == 401;
    }

    /**
     * 检查并补充人员照片数据
     * 如果PersonInfo没有照片，则从face_photo表查询并设置
     *
     * @param personInfo 人员信息
     */
    private void ensurePersonPicture(PersonInfo personInfo) {
        // 如果已经有照片数据，则无需处理
        if (personInfo.getPersonPicture() != null && personInfo.getPersonPicture().length > 0) {
            logger.debug("人员 {} 已有照片数据，跳过照片查询", personInfo.getPersonName());
            return;
        }

        try {
            // 根据人员编码查询face_photo表
            Optional<MiddlewareFacePhoto> facePhotoOpt = 
                middlewareFacePhotoRepository.findByPerCode(personInfo.getPersonCode());
            
            if (facePhotoOpt.isPresent()) {
                MiddlewareFacePhoto facePhoto = facePhotoOpt.get();
                if (facePhoto.getPhotoData() != null && !facePhoto.getPhotoData().trim().isEmpty()) {
                    // 处理十六进制格式的照片数据（如 \xffd8ffe0...）
                    byte[] photoBytes = convertHexStringToBytes(facePhoto.getPhotoData());
                    
                    if (photoBytes != null && photoBytes.length > 0) {
                        personInfo.setPersonPicture(photoBytes);
                        logger.debug("从数据库为人员 {} 设置照片数据 ({}字节)", 
                                   personInfo.getPersonName(), photoBytes.length);
                        return;
                    } else {
                        logger.warn("人员 {} 的照片数据转换失败，将使用默认照片", personInfo.getPersonName());
                    }
                }
            }



        } catch (Exception e) {
            logger.warn("为人员 {} 获取照片数据失败，将不包含照片: 错误详情: {}", 
                       personInfo.getPersonName(), e.getMessage());
            // 照片获取失败不影响同步，设置为null继续处理
            personInfo.setPersonPicture(null);
        }
    }

    /**
     * 将十六进制字符串转换为字节数组
     * 支持PostgreSQL BYTEA格式：\xffd8ffe0...
     * @param hexString 十六进制字符串
     * @return 转换后的字节数组，转换失败时返回null
     */
    private byte[] convertHexStringToBytes(String hexString) {
        if (hexString == null || hexString.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 移除PostgreSQL BYTEA前缀 \x
            String cleanHex = hexString.trim();
            if (cleanHex.startsWith("\\x")) {
                cleanHex = cleanHex.substring(2);
            }
            
            // 检查是否为有效的十六进制字符串
            if (cleanHex.length() % 2 != 0) {
                logger.warn("无效的十六进制字符串长度: {}", cleanHex.length());
                return null;
            }
            
            // 转换十六进制字符串为字节数组
            int len = cleanHex.length();
            byte[] data = new byte[len / 2];
            for (int i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(cleanHex.charAt(i), 16) << 4)
                                    + Character.digit(cleanHex.charAt(i + 1), 16));
            }
            
            logger.debug("十六进制转换成功，输入长度: {}, 输出字节数: {}", cleanHex.length(), data.length);
            return data;
            
        } catch (Exception e) {
            logger.error("十六进制字符串转换失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 重新登录EGS平台
     *
     * @return 登录是否成功
     */
    private boolean reLogin() {
        try {
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();

            logger.info("重新登录EGS平台: url={}, user={}", loginUrl, user);

            // 调用现有的登录方法
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);

            logger.info("EGS平台重新登录成功");
            return true;

        } catch (Exception e) {
            logger.error("EGS平台重新登录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取楼栋人员结构信息
     * @return 楼栋楼层人员分布信息
     */
    public List<Map<String, Object>> getBuildingPersonStructure() {
        try {
            String sql = "SELECT " +
                    "d.building_code, " +
                    "d.building_name, " +
                    "d.floor, " +
                    "COUNT(DISTINCT d.dormitory_code) as dormitory_count, " +
                    "COUNT(pdr.person_code) as person_count " +
                    "FROM dormitory_info d " +
                    "LEFT JOIN person_dormitory_relation pdr ON d.dormitory_code = pdr.dormitory_code AND pdr.status = 1 " +
                    "WHERE d.status = 1 " +
                    "GROUP BY d.building_code, d.building_name, d.floor " +
                    "ORDER BY d.building_name, d.floor";

            Query query = entityManager.createNativeQuery(sql);
            List<Object[]> resultList = query.getResultList();
            
            // 按楼栋分组组织数据
            Map<String, Map<String, Object>> buildingMap = new LinkedHashMap<>();
            
            for (Object[] row : resultList) {
                String buildingCode = (String) row[0];
                String buildingName = (String) row[1];
                Integer floor = row[2] != null ? ((Number) row[2]).intValue() : null;
                Integer dormitoryCount = ((Number) row[3]).intValue();
                Integer personCount = ((Number) row[4]).intValue();
                
                // 获取或创建楼栋信息
                Map<String, Object> building = buildingMap.computeIfAbsent(buildingCode, k -> {
                    Map<String, Object> b = new HashMap<>();
                    b.put("buildingCode", buildingCode);
                    b.put("buildingName", buildingName);
                    b.put("totalPersons", 0);
                    b.put("totalDormitories", 0);
                    b.put("floors", new ArrayList<Map<String, Object>>());
                    return b;
                });
                
                // 更新楼栋统计
                building.put("totalPersons", (Integer) building.get("totalPersons") + personCount);
                building.put("totalDormitories", (Integer) building.get("totalDormitories") + dormitoryCount);
                
                // 添加楼层信息
                Map<String, Object> floorInfo = new HashMap<>();
                floorInfo.put("floor", floor);
                floorInfo.put("floorName", floor != null ? floor + "层" : "未分层");
                floorInfo.put("dormitoryCount", dormitoryCount);
                floorInfo.put("personCount", personCount);
                
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> floors = (List<Map<String, Object>>) building.get("floors");
                floors.add(floorInfo);
            }
            
            List<Map<String, Object>> result = new ArrayList<>(buildingMap.values());
            logger.info("查询楼栋人员结构成功，共{}个楼栋", result.size());
            return result;
            
        } catch (Exception e) {
            logger.error("查询楼栋人员结构异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据楼栋和楼层查询人员信息
     * @param buildingCode 楼栋编码
     * @param floors 楼层列表，为空时查询整个楼栋
     * @return 人员信息列表
     */
    public List<Map<String, Object>> getPersonsByBuildingAndFloors(String buildingCode, List<Integer> floors) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT DISTINCT ")
                    .append("pi.person_name, ")
                    .append("pi.person_code, ")
                    .append("pi.gender, ")
                    .append("pi.person_type, ")
                    .append("pi.department_code, ")
                    .append("pi.sync_flag, ")
                    .append("d.building_name, ")
                    .append("d.floor, ")
                    .append("d.room_name, ")
                    .append("pdr.bed_no ")
                    .append("FROM person_info pi ")
                    .append("INNER JOIN person_dormitory_relation pdr ON pi.person_code = pdr.person_code AND pdr.status = 1 ")
                    .append("INNER JOIN dormitory_info d ON pdr.dormitory_code = d.dormitory_code AND d.status = 1 ")
                    .append("WHERE d.building_code = :buildingCode ");

            // 如果指定了楼层，添加楼层过滤条件
            if (floors != null && !floors.isEmpty()) {
                sql.append("AND d.floor IN :floors ");
            }
            
            sql.append("ORDER BY d.floor, d.room_name, pdr.bed_no");

            Query query = entityManager.createNativeQuery(sql.toString());
            query.setParameter("buildingCode", buildingCode);
            if (floors != null && !floors.isEmpty()) {
                query.setParameter("floors", floors);
            }

            List<Object[]> resultList = query.getResultList();
            List<Map<String, Object>> persons = new ArrayList<>();
            
            for (Object[] row : resultList) {
                Map<String, Object> person = new HashMap<>();
                person.put("personName", row[0]);
                person.put("personCode", row[1]);
                person.put("gender", row[2] != null ? ((Number) row[2]).intValue() : null);
                person.put("personType", row[3]);
                person.put("departmentCode", row[4]);
                person.put("syncFlag", row[5] != null ? ((Number) row[5]).intValue() : 0);
                person.put("buildingName", row[6]);
                person.put("floor", row[7] != null ? ((Number) row[7]).intValue() : null);
                person.put("roomName", row[8]);
                person.put("bedNo", row[9]);
                persons.add(person);
            }
            
            logger.info("查询楼栋{}人员成功，共{}条记录", buildingCode, persons.size());
            return persons;
            
        } catch (Exception e) {
            logger.error("查询楼栋{}人员异常: {}", buildingCode, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 按楼栋同步人员到EGS平台
     * @param buildingCode 楼栋编码
     * @param floors 楼层列表，为空时同步整个楼栋
     * @return 同步结果
     */
    public Map<String, Object> syncPersonsByBuilding(String buildingCode, List<Integer> floors) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询楼栋名称
            String buildingNameSql = "SELECT building_name FROM dormitory_info WHERE building_code = :buildingCode LIMIT 1";
            Query buildingQuery = entityManager.createNativeQuery(buildingNameSql);
            buildingQuery.setParameter("buildingCode", buildingCode);
            List<String> buildingNames = buildingQuery.getResultList();
            String buildingName = buildingNames.isEmpty() ? buildingCode : buildingNames.get(0);
            
            // 查询需要同步的人员
            List<Map<String, Object>> personsToSync = getPersonsByBuildingAndFloors(buildingCode, floors);
            
            if (personsToSync.isEmpty()) {
                result.put("success", false);
                result.put("message", "该楼栋/楼层下没有找到需要同步的人员");
                result.put("totalCount", 0);
                result.put("successCount", 0);
                result.put("failCount", 0);
                return result;
            }

            logger.info("开始按楼栋同步人员: 楼栋[{}], 楼层{}, 人员数量: {}", 
                       buildingName, floors != null ? floors : "全部", personsToSync.size());

            // 先进行EGS平台登录验证
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);
            logger.info("EGS系统登录验证完成");

            // 统计变量
            int successCount = 0;
            int failCount = 0;
            List<String> failedPersons = new ArrayList<>();
            
            // 遍历人员进行同步
            for (Map<String, Object> personData : personsToSync) {
                try {
                    String personCode = (String) personData.get("personCode");
                    String personName = (String) personData.get("personName");
                    
                    // 查询完整的人员信息
                    Optional<PersonInfo> personOpt = personRepository.findByPersonCode(personCode);
                    if (!personOpt.isPresent()) {
                        logger.warn("未找到人员信息: {}", personCode);
                        failCount++;
                        failedPersons.add(personName + "(" + personCode + ") - 未找到人员信息");
                        continue;
                    }
                    
                    PersonInfo personInfo = personOpt.get();
                    
                    // 设置areaCode和departmentCode为楼栋编码（关键修改）
                    personInfo.setAreaCode(buildingCode);
                    personInfo.setDepartmentCode(buildingCode);
                    
                    // 调用同步方法
                    boolean syncSuccess = addPerson(personInfo);
                    
                    if (syncSuccess) {
                        // 更新同步标志
                        personInfo.setSyncFlag(1);
                        personRepository.save(personInfo);
                        successCount++;
                        logger.debug("楼栋人员同步成功: {} ({})", personName, personCode);
                    } else {
                        failCount++;
                        failedPersons.add(personName + "(" + personCode + ") - EGS平台返回错误");
                        logger.warn("楼栋人员同步失败: {} ({}) - addPerson返回false", personName, personCode);
                    }
                    
                    // 添加延时避免频繁请求
                    Thread.sleep(100);
                    
                } catch (Exception e) {
                    failCount++;
                    String personName = (String) personData.get("personName");
                    String personCode = (String) personData.get("personCode");
                    failedPersons.add(personName + "(" + personCode + ") - " + e.getMessage());
                    logger.error("楼栋人员同步异常: {} ({})", personName, personCode, e);
                }
            }
            
            // 构造返回结果
            result.put("success", true);
            result.put("buildingName", buildingName);
            result.put("buildingCode", buildingCode);
            result.put("floors", floors);
            result.put("totalCount", personsToSync.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            
            String message = String.format("楼栋[%s]人员同步完成 - 总计: %d人, 成功: %d人, 失败: %d人", 
                                          buildingName, personsToSync.size(), successCount, failCount);
            
            if (failCount > 0) {
                message += "\n失败详情: " + String.join("; ", failedPersons);
                result.put("failedPersons", failedPersons);
            }
            
            result.put("message", message);
            
            logger.info("楼栋人员同步完成: {}", message);
            return result;
            
        } catch (Exception e) {
            logger.error("楼栋人员同步异常: buildingCode={}, floors={}, error={}", 
                        buildingCode, floors, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "楼栋人员同步异常: " + e.getMessage());
            result.put("totalCount", 0);
            result.put("successCount", 0);
            result.put("failCount", 0);
            return result;
        }
    }
}
