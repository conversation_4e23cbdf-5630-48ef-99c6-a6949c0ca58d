package fastgatedemo.demo.service;

import fastgatedemo.demo.model.ScheduledTaskInfo;
import fastgatedemo.demo.model.TaskExecutionLog;
import fastgatedemo.demo.repository.ScheduledTaskInfoRepository;
import fastgatedemo.demo.repository.TaskExecutionLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @description 定时任务管理服务
 */
@Service
public class ScheduledTaskManagementService {

    private static final Logger logger = LoggerFactory.getLogger(ScheduledTaskManagementService.class);

    @Autowired
    private ScheduledTaskInfoRepository taskInfoRepository;

    @Autowired
    private TaskExecutionLogRepository executionLogRepository;

    private String serverIp;

    @PostConstruct
    public void init() {
        try {
            serverIp = InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            serverIp = "unknown";
        }
        
        // 初始化已知的定时任务信息
        initializeKnownTasks();
    }

    /**
     * 初始化已知的定时任务信息
     */
    private void initializeKnownTasks() {
        try {
            // 人员宿舍关联同步任务
            registerTaskIfNotExists(
                "PersonDormitorySyncTask",
                "人员宿舍关联定时同步任务",
                "fastgatedemo.demo.service.PersonDormitorySyncService",
                "syncUnsyncedPersonDormitoryRelations",
                null,
                300000L, // 5分钟
                null
            );

            // 人脸照片同步检查任务
            registerTaskIfNotExists(
                "FacePhotoSyncCheckTask",
                "人脸照片同步状态检查任务",
                "fastgatedemo.demo.service.FacePhotoSyncCheckService",
                "checkFacePhotoSyncIssues",
                null,
                3600000L, // 1小时
                null
            );

            // EGS平台提取失败人脸记录查询任务
            registerTaskIfNotExists(
                "EGSFailedFaceExtractionTask",
                "EGS平台提取失败人脸记录查询任务",
                "fastgatedemo.demo.service.EGSFailedFaceExtractionService",
                "scheduledQueryFailedFaceExtraction",
                null,
                300000L, // 5分钟
                null
            );

            // 每日出入记录成功人数统计任务
            registerTaskIfNotExists(
                "DailyAccessRecordStatsTask",
                "每日出入记录成功人数统计任务",
                "fastgatedemo.demo.service.DailyAccessRecordStatsService",
                "scheduledQueryDailyAccessStats",
                "0 0 20 * * ?", // 每天晚上8点执行
                null,
                null
            );

            logger.info("定时任务信息初始化完成");
        } catch (Exception e) {
            logger.error("定时任务信息初始化失败", e);
        }
    }

    /**
     * 注册任务（如果不存在）
     */
    private void registerTaskIfNotExists(String taskName, String description, String taskClass, 
                                       String taskMethod, String cronExpression, Long fixedRate, Long fixedDelay) {
        Optional<ScheduledTaskInfo> existingTask = taskInfoRepository.findByTaskName(taskName);
        if (!existingTask.isPresent()) {
            ScheduledTaskInfo taskInfo = new ScheduledTaskInfo();
            taskInfo.setTaskName(taskName);
            taskInfo.setTaskDescription(description);
            taskInfo.setTaskClass(taskClass);
            taskInfo.setTaskMethod(taskMethod);
            taskInfo.setCronExpression(cronExpression);
            taskInfo.setFixedRate(fixedRate);
            taskInfo.setFixedDelay(fixedDelay);
            taskInfo.setEnabled(true);
            
            taskInfoRepository.save(taskInfo);
            logger.info("注册新的定时任务: {}", taskName);
        }
    }

    /**
     * 记录任务开始执行
     */
    public TaskExecutionLog recordTaskStart(String taskName) {
        TaskExecutionLog log = new TaskExecutionLog(taskName);
        log.setServerIp(serverIp);
        log.setThreadName(Thread.currentThread().getName());
        
        return executionLogRepository.save(log);
    }

    /**
     * 记录任务执行成功
     */
    @Transactional
    public void recordTaskSuccess(TaskExecutionLog log, String message, Integer processedRecords, 
                                 Integer successRecords, Integer failedRecords) {
        log.markSuccess(message);
        log.setProcessedRecords(processedRecords);
        log.setSuccessRecords(successRecords);
        log.setFailedRecords(failedRecords);
        
        executionLogRepository.save(log);
        
        // 更新任务信息统计
        updateTaskStatistics(log.getTaskName(), "SUCCESS", log.getExecutionDuration(), null);
    }

    /**
     * 记录任务执行失败
     */
    @Transactional
    public void recordTaskFailure(TaskExecutionLog log, String errorMessage) {
        log.markFailure(errorMessage);
        
        executionLogRepository.save(log);
        
        // 更新任务信息统计
        updateTaskStatistics(log.getTaskName(), "FAILED", log.getExecutionDuration(), errorMessage);
    }

    /**
     * 更新任务统计信息
     */
    private void updateTaskStatistics(String taskName, String status, Long duration, String errorMessage) {
        try {
            taskInfoRepository.updateTaskExecutionInfo(
                taskName, 
                LocalDateTime.now(), 
                status, 
                duration, 
                errorMessage
            );
        } catch (Exception e) {
            logger.error("更新任务统计信息失败: taskName={}", taskName, e);
        }
    }

    /**
     * 获取所有任务信息
     */
    public Page<ScheduledTaskInfo> getAllTasks(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("updateTime").descending());
        return taskInfoRepository.findAll(pageable);
    }

    /**
     * 获取任务执行日志
     */
    public Page<TaskExecutionLog> getTaskExecutionLogs(String taskName, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        if (taskName != null && !taskName.trim().isEmpty()) {
            return executionLogRepository.findByTaskNameOrderByExecutionStartTimeDesc(taskName, pageable);
        } else {
            return executionLogRepository.findAll(PageRequest.of(page, size, Sort.by("executionStartTime").descending()));
        }
    }

    /**
     * 启用/禁用任务
     */
    @Transactional
    public boolean toggleTaskEnabled(String taskName) {
        Optional<ScheduledTaskInfo> taskOpt = taskInfoRepository.findByTaskName(taskName);
        if (taskOpt.isPresent()) {
            ScheduledTaskInfo task = taskOpt.get();
            boolean newEnabled = !task.getEnabled();
            taskInfoRepository.updateTaskEnabled(taskName, newEnabled);
            logger.info("任务 {} 状态已更改为: {}", taskName, newEnabled ? "启用" : "禁用");
            return true;
        }
        return false;
    }

    /**
     * 重置任务统计信息
     */
    @Transactional
    public boolean resetTaskStatistics(String taskName) {
        int updated = taskInfoRepository.resetTaskStatistics(taskName);
        if (updated > 0) {
            logger.info("任务 {} 统计信息已重置", taskName);
            return true;
        }
        return false;
    }

    /**
     * 获取任务监控仪表板数据
     */
    public Map<String, Object> getTaskDashboardData() {
        Map<String, Object> dashboard = new HashMap<>();
        
        try {
            // 基础统计
            Long totalTasks = taskInfoRepository.countAllTasks();
            Long enabledTasks = taskInfoRepository.countEnabledTasks();
            Long runningTasks = taskInfoRepository.countRunningTasks();
            
            dashboard.put("totalTasks", totalTasks);
            dashboard.put("enabledTasks", enabledTasks);
            dashboard.put("disabledTasks", totalTasks - enabledTasks);
            dashboard.put("runningTasks", runningTasks);
            
            // 最近24小时执行统计
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime yesterday = now.minusDays(1);
            
            List<Object[]> recentStats = executionLogRepository.getTaskExecutionStatistics(yesterday, now);
            List<Map<String, Object>> taskStats = new ArrayList<>();
            
            for (Object[] stat : recentStats) {
                Map<String, Object> taskStat = new HashMap<>();
                taskStat.put("taskName", stat[0]);
                taskStat.put("totalExecutions", stat[1]);
                taskStat.put("successExecutions", stat[2]);
                taskStat.put("failureExecutions", stat[3]);
                taskStat.put("avgDuration", stat[4]);
                taskStats.add(taskStat);
            }
            
            dashboard.put("recentTaskStats", taskStats);
            
            // 异常任务
            List<ScheduledTaskInfo> failedTasks = taskInfoRepository.findTasksWithHighFailureCount(5L);
            dashboard.put("highFailureTasks", failedTasks);
            
            // 长时间未执行的任务
            LocalDateTime oneHourAgo = now.minusHours(1);
            List<ScheduledTaskInfo> staleTask = taskInfoRepository.findTasksNotExecutedSince(oneHourAgo);
            dashboard.put("staleTasks", staleTask);
            
            // 正在运行的任务
            List<TaskExecutionLog> runningTaskLogs = executionLogRepository.findByExecutionStatusOrderByExecutionStartTimeDesc("RUNNING");
            dashboard.put("currentlyRunningTasks", runningTaskLogs);

            // 超时任务
            LocalDateTime timeoutThreshold = now.minusMinutes(30);
            List<TaskExecutionLog> timeoutTasks = executionLogRepository.findByExecutionStatusAndExecutionStartTimeBefore("RUNNING", timeoutThreshold);
            dashboard.put("timeoutTasks", timeoutTasks);
            
        } catch (Exception e) {
            logger.error("获取任务监控仪表板数据失败", e);
            dashboard.put("error", e.getMessage());
        }
        
        return dashboard;
    }

    /**
     * 清理历史执行日志
     */
    @Transactional
    public int cleanupExecutionLogs(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        int deletedCount = executionLogRepository.deleteLogsBefore(cutoffTime);
        logger.info("清理了 {} 条历史执行日志（保留最近 {} 天）", deletedCount, daysToKeep);
        return deletedCount;
    }

    /**
     * 获取任务详细信息
     */
    public Optional<ScheduledTaskInfo> getTaskInfo(String taskName) {
        return taskInfoRepository.findByTaskName(taskName);
    }

    /**
     * 获取任务最近执行记录
     */
    public List<TaskExecutionLog> getRecentExecutionLogs(String taskName, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return executionLogRepository.findRecentExecutionLogs(taskName, pageable);
    }
}
