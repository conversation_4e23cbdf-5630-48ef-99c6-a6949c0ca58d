package fastgatedemo.demo.service;

import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.websocket.DashboardWebSocketHandlerBackup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description 大屏WebSocket推送服务
 * 负责向前端推送实时进出记录
 * <AUTHOR>
 * @date 2025-01-30
 */
@Service
public class DashboardWebSocketService {

    private static final Logger logger = LoggerFactory.getLogger(DashboardWebSocketService.class);

    @Autowired
    private DashboardWebSocketHandlerBackup webSocketHandler;

    @Autowired
    private CachedDormitoryStatsService cachedDormitoryStatsService;

    @Autowired
    private StudentStatusCacheService statusCacheService;

    /**
     * 异步推送新的进出记录
     * 当有新的进出记录时调用此方法
     */
    @Async
    public void pushNewRecord(DormitoryStatusDTO record) {
        try {
            logger.info("准备推送新的进出记录: personCode={}, personName={}, action={}", 
                    record.getPersonCode(), record.getPersonName(), record.getLastInOrOutDesc());
            
            webSocketHandler.pushNewRecord(record);
            
        } catch (Exception e) {
            logger.error("推送新进出记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 推送批量记录（用于客户端初始化）
     */
    @Async
    public void pushRecentRecords(int limit) {
        try {
            logger.info("推送最近{}条进出记录", limit);
            
            // 获取最近的进出记录
            List<DormitoryStatusDTO> recentRecords = getRecentRecords(limit);
            
            if (!recentRecords.isEmpty()) {
                webSocketHandler.pushBatchRecords(recentRecords);
                logger.info("成功推送{}条最近记录", recentRecords.size());
            } else {
                logger.info("没有最近记录可推送");
            }
            
        } catch (Exception e) {
            logger.error("推送最近记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取最近的进出记录
     * @param limit 记录数量限制
     * @return 最近的进出记录列表
     */
    private List<DormitoryStatusDTO> getRecentRecords(int limit) {
        try {
            // 从缓存服务获取最近的进出记录
            // 这里简化实现，实际可以根据需要从不同数据源获取
            logger.debug("获取最近{}条进出记录", limit);

            // 由于当前缓存服务主要存储状态而非历史记录，
            // 这里返回空列表，实际使用中可以扩展为从数据库获取最近记录
            return new ArrayList<>();

        } catch (Exception e) {
            logger.error("获取最近记录失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 定时发送心跳消息（每30秒）
     */
    @Scheduled(fixedRate = 30000)
    public void sendHeartbeat() {
        try {
            int connectionCount = webSocketHandler.getConnectionCount();
            if (connectionCount > 0) {
                logger.debug("发送WebSocket心跳消息，当前连接数: {}", connectionCount);
                webSocketHandler.sendHeartbeat();
            }
        } catch (Exception e) {
            logger.error("发送心跳消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取WebSocket连接状态
     */
    public int getConnectionCount() {
        return webSocketHandler.getConnectionCount();
    }

    /**
     * 模拟推送测试记录（用于测试）
     */
    public void pushTestRecord() {
        try {
            DormitoryStatusDTO testRecord = new DormitoryStatusDTO();
            testRecord.setPersonCode("TEST001");
            testRecord.setPersonName("测试用户");
            testRecord.setLastInOrOut(1);
            testRecord.setLastInOrOutDesc("进入寝室");
            testRecord.setLastPassTimeStr(java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")));
            testRecord.setDormitoryName("测试寝室");
            testRecord.setBuildingName("测试楼栋");
            testRecord.setLastDeviceName("测试设备");
            
            pushNewRecord(testRecord);
            logger.info("推送测试记录完成");
            
        } catch (Exception e) {
            logger.error("推送测试记录失败: {}", e.getMessage(), e);
        }
    }
}
