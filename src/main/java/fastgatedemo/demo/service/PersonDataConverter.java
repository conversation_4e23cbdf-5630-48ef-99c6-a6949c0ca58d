package fastgatedemo.demo.service;

import fastgatedemo.demo.model.MiddlewareFacePhoto;
import fastgatedemo.demo.model.MiddlewarePersonInfo;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.util.ImageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * @description 人员数据转换服务
 * 负责将中间库数据转换为EGS PersonInfo格式的转换
 */
@Service
public class PersonDataConverter {

    private static final Logger logger = LoggerFactory.getLogger(PersonDataConverter.class);
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 将中间库人员信息转换为EGS PersonInfo格式
     * @param middlewarePersonInfo 中间库人员信息
     * @param middlewareFacePhoto 中间库人脸照片（可选）
     * @return EGS PersonInfo对象
     */
    public PersonInfo convertToPersonInfo(MiddlewarePersonInfo middlewarePersonInfo, 
                                         MiddlewareFacePhoto middlewareFacePhoto) {
        if (middlewarePersonInfo == null) {
            throw new IllegalArgumentException("中间库人员信息不能为空");
        }

        PersonInfo personInfo = new PersonInfo();

        // 设置主键ID，使用UUID
        personInfo.setSeqid(UUID.randomUUID().toString());

        // 必填字段转换
        personInfo.setPersonName(middlewarePersonInfo.getAccName());
        personInfo.setPersonCode(convertPersonCode(middlewarePersonInfo));
        
        // 性别转换：中间库(1男2女0未知) → EGS(1男0女)
        personInfo.setGender(convertGender(middlewarePersonInfo.getGender()));
        
        // 状态转换：保持一致(1启用0禁用)
        personInfo.setStatus(middlewarePersonInfo.getStatus());
        
        // 人员类型转换：中间库(3学生5教职工) → EGS("6"等字符串)
        personInfo.setPersonType(convertPersonType(middlewarePersonInfo.getPerType()));

        // 可选字段转换
        personInfo.setIdcard(middlewarePersonInfo.getIdCard());
        personInfo.setTelephone(middlewarePersonInfo.getTelephone());
        

        // 卡相关信息
        personInfo.setCardnum(middlewarePersonInfo.getIcCard());
        personInfo.setCardtype("1"); // 默认卡类型

        // 时间字段
        personInfo.setUpdateTime(
            middlewarePersonInfo.getUpdateTime() != null 
                ? middlewarePersonInfo.getUpdateTime().format(DATE_FORMATTER)
                : LocalDateTime.now().format(DATE_FORMATTER)
        );

        // 同步标志：默认未同步
        personInfo.setSyncFlag(0);

        // 查询并设置区域编码(宿舍编码)
        String areaCode = queryPersonDormitoryCode(middlewarePersonInfo.getPerCode());
        personInfo.setAreaCode(areaCode);

        // 处理人脸照片：优先使用数据库中的照片，失败时使用默认照片
        try {
            if (middlewareFacePhoto != null && 
                middlewareFacePhoto.getPhotoData() != null && 
                !middlewareFacePhoto.getPhotoData().trim().isEmpty()) {
                
                // 处理十六进制格式的照片数据（如 \xffd8ffe0...）
                byte[] photoBytes = convertHexStringToBytes(middlewareFacePhoto.getPhotoData());
                
                if (photoBytes != null && photoBytes.length > 0) {
                    personInfo.setPersonPicture(photoBytes);
                    logger.debug("成功从数据库加载照片: {} ({}字节)", 
                               middlewarePersonInfo.getAccName(), photoBytes.length);
                } else {
                    logger.warn("照片数据转换失败，使用默认照片: {}", middlewarePersonInfo.getAccName());
                    personInfo.setPersonPicture(ImageUtils.getImageBytesByFilePath("E:\\jianyou\\DataTrans\\doc\\lisi.png"));
                }
            } else {
                // 使用默认照片
                personInfo.setPersonPicture(ImageUtils.getImageBytesByFilePath("E:\\jianyou\\DataTrans\\doc\\lisi.png"));
                logger.debug("未找到照片数据，使用默认照片: {}", middlewarePersonInfo.getAccName());
            }
        } catch (Exception e) {
            logger.warn("照片处理失败，尝试使用默认照片: {} - 错误详情: {}", 
                       middlewarePersonInfo.getAccName(), e.getMessage());
            try {
                // 尝试使用默认照片作为后备
                personInfo.setPersonPicture(ImageUtils.getImageBytesByFilePath("E:\\jianyou\\DataTrans\\doc\\lisi.png"));
                logger.debug("已为人员 {} 设置默认照片", middlewarePersonInfo.getAccName());
            } catch (Exception e2) {
                logger.warn("加载默认照片也失败，继续处理: {} - {}", middlewarePersonInfo.getAccName(), e2.getMessage());
                // 照片加载失败不影响人员同步
                personInfo.setPersonPicture(null);
            }
        }

        logger.debug("转换人员信息完成: {} -> {}", 
                    middlewarePersonInfo.getAccName(), personInfo.getPersonName());

        return personInfo;
    }

    /**
     * 转换人员编码
     * 优先使用per_code，如果为空则使用acc_num
     */
    private String convertPersonCode(MiddlewarePersonInfo middlewarePersonInfo) {
        if (StringUtils.hasText(middlewarePersonInfo.getPerCode())) {
            return middlewarePersonInfo.getPerCode();
        }
        if (StringUtils.hasText(middlewarePersonInfo.getAccNum())) {
            return middlewarePersonInfo.getAccNum();
        }
        // 如果都为空，生成一个临时编码
        return "TEMP_" + System.currentTimeMillis();
    }

    /**
     * 转换性别
     * 中间库：1-男，2-女，0-未知
     * EGS系统：1-男，0-女
     */
    private Integer convertGender(Integer middlewareGender) {
        if (middlewareGender == null) {
            return 1; // 默认男性
        }
        switch (middlewareGender) {
            case 1: return 1; // 男
            case 2: return 0; // 女
            case 0: 
            default: return 1; // 未知默认为男
        }
    }

    /**
     * 转换人员类型
     * 中间库：3-学生，5-教职工
     * EGS系统：使用字符串，"6"为通用类型
     */
    private String convertPersonType(Integer middlewarePerType) {
        if (middlewarePerType == null) {
            return "6"; // 默认类型
        }
        switch (middlewarePerType) {
            case 3: return "3"; // 学生
            case 5: return "5"; // 教职工
            default: return "6"; // 其他类型
        }
    }

    /**
     * 验证转换后的PersonInfo是否符合EGS系统要求
     */
    public boolean validatePersonInfo(PersonInfo personInfo) {
        if (personInfo == null) {
            return false;
        }

        // 检查必填字段
        if (!StringUtils.hasText(personInfo.getPersonName()) ||
            !StringUtils.hasText(personInfo.getPersonCode()) ||
            personInfo.getGender() == null ||
            personInfo.getStatus() == null) {
            return false;
        }

        return true;
    }

    /**
     * 批量转换人员信息
     * @param middlewarePersonInfos 中间库人员信息列表
     * @param middlewareFacePhotos 中间库人脸照片列表
     * @return 转换后的PersonInfo列表
     */
    public java.util.List<PersonInfo> batchConvert(
            java.util.List<MiddlewarePersonInfo> middlewarePersonInfos,
            java.util.Map<String, MiddlewareFacePhoto> middlewareFacePhotos) {
        
        java.util.List<PersonInfo> result = new java.util.ArrayList<>();
        
        for (MiddlewarePersonInfo middlewarePersonInfo : middlewarePersonInfos) {
            try {
                MiddlewareFacePhoto facePhoto = null;
                if (middlewareFacePhotos != null) {
                    facePhoto = middlewareFacePhotos.get(middlewarePersonInfo.getPerCode());
                }
                
                PersonInfo personInfo = convertToPersonInfo(middlewarePersonInfo, facePhoto);
                if (validatePersonInfo(personInfo)) {
                    result.add(personInfo);
                } else {
                    logger.warn("人员信息验证失败，跳过: {}", middlewarePersonInfo.getAccName());
                }
            } catch (Exception e) {
                logger.error("转换人员信息时发生异常: {}", middlewarePersonInfo.getAccName(), e);
            }
        }
        
        logger.info("批量转换完成：输入{}条，输出{}条", middlewarePersonInfos.size(), result.size());
        return result;
    }

    /**
     * 将十六进制字符串转换为字节数组
     * 支持PostgreSQL BYTEA格式：\xffd8ffe0...
     * @param hexString 十六进制字符串
     * @return 转换后的字节数组，转换失败时返回null
     */
    private byte[] convertHexStringToBytes(String hexString) {
        if (hexString == null || hexString.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 移除PostgreSQL BYTEA前缀 \x
            String cleanHex = hexString.trim();
            if (cleanHex.startsWith("\\x")) {
                cleanHex = cleanHex.substring(2);
            }
            
            // 检查是否为有效的十六进制字符串
            if (cleanHex.length() % 2 != 0) {
                logger.warn("无效的十六进制字符串长度: {}", cleanHex.length());
                return null;
            }
            
            // 转换十六进制字符串为字节数组
            int len = cleanHex.length();
            byte[] data = new byte[len / 2];
            for (int i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(cleanHex.charAt(i), 16) << 4)
                                    + Character.digit(cleanHex.charAt(i + 1), 16));
            }
            
            logger.debug("十六进制转换成功，输入长度: {}, 输出字节数: {}", cleanHex.length(), data.length);
            return data;
            
        } catch (Exception e) {
            logger.error("十六进制字符串转换失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 查询人员对应的宿舍编码
     * @param personCode 人员编码
     * @return 宿舍编码，如果未分配宿舍则返回null
     */
    private String queryPersonDormitoryCode(String personCode) {
        try {
            String sql = "SELECT dormitory_code FROM person_dormitory_relation WHERE person_code = :personCode AND status = 1";
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("personCode", personCode);
            
            @SuppressWarnings("unchecked")
            List<String> results = query.getResultList();
            
            if (!results.isEmpty()) {
                String dormitoryCode = results.get(0);
                logger.debug("查询到人员{}的宿舍编码: {}", personCode, dormitoryCode);
                return dormitoryCode;
            } else {
                logger.debug("人员{}未分配宿舍", personCode);
                return null;
            }
        } catch (Exception e) {
            logger.error("查询人员{}宿舍编码失败: {}", personCode, e.getMessage(), e);
            return null;
        }
    }
} 