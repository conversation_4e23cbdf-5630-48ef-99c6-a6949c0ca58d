package fastgatedemo.demo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @description 数据库压力监控服务
 * 实时监控数据库连接、TPS、响应时间等指标
 * 提供自动限流和告警机制
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class DatabasePressureMonitorService {

    private static final Logger logger = LoggerFactory.getLogger(DatabasePressureMonitorService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 配置参数
    @Value("${db.monitor.enabled:true}")
    private boolean monitorEnabled = true;

    @Value("${db.monitor.max.connections:50}")
    private int maxConnections = 50;

    @Value("${db.monitor.max.tps:2000}")
    private int maxTPS = 2000;

    @Value("${db.monitor.max.response.time:5000}")
    private long maxResponseTime = 5000; // 5秒

    @Value("${db.monitor.check.interval.seconds:30}")
    private int checkIntervalSeconds = 30;

    // 监控指标
    private final AtomicLong totalQueries = new AtomicLong(0);
    private final AtomicLong totalSlowQueries = new AtomicLong(0);
    private final AtomicInteger currentTPS = new AtomicInteger(0);
    private final AtomicLong currentResponseTime = new AtomicLong(0);
    private final AtomicInteger currentConnections = new AtomicInteger(0);
    private volatile boolean isUnderPressure = false;
    private volatile LocalDateTime lastPressureTime;
    private volatile LocalDateTime lastResetTime = LocalDateTime.now();

    // 限流控制
    private final AtomicInteger queryCount = new AtomicInteger(0);
    private final AtomicLong windowStartTime = new AtomicLong(System.currentTimeMillis());
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    @PostConstruct
    public void init() {
        if (monitorEnabled) {
            startMonitoring();
            logger.info("数据库压力监控服务已启动");
        }
    }

    /**
     * 开始监控
     */
    private void startMonitoring() {
        // 定期检查数据库状态
        scheduler.scheduleAtFixedRate(this::checkDatabaseStatus, 10, checkIntervalSeconds, TimeUnit.SECONDS);
        
        // 定期重置TPS计数器
        scheduler.scheduleAtFixedRate(this::resetTPSCounter, 60, 60, TimeUnit.SECONDS);
        
        logger.info("数据库监控定时任务已启动，检查间隔: {}秒", checkIntervalSeconds);
    }

    /**
     * 检查是否允许执行查询（限流检查）
     * @return 是否允许执行
     */
    public boolean allowQuery() {
        if (!monitorEnabled) {
            return true;
        }

        // 检查当前压力状态
        if (isUnderPressure) {
            logger.debug("数据库处于高压力状态，拒绝查询");
            return false;
        }

        // 检查TPS限制
        long currentWindow = System.currentTimeMillis() / 1000; // 秒级窗口
        long windowStart = windowStartTime.get() / 1000;
        
        if (currentWindow > windowStart) {
            // 新的时间窗口，重置计数
            queryCount.set(0);
            windowStartTime.set(System.currentTimeMillis());
        }
        
        int currentCount = queryCount.incrementAndGet();
        if (currentCount > maxTPS) {
            logger.warn("TPS超过限制({})，当前: {}", maxTPS, currentCount);
            return false;
        }

        return true;
    }

    /**
     * 记录查询执行
     * @param executionTime 执行时间（毫秒）
     * @param isSlowQuery 是否为慢查询
     */
    public void recordQuery(long executionTime, boolean isSlowQuery) {
        if (!monitorEnabled) {
            return;
        }

        totalQueries.incrementAndGet();
        currentTPS.incrementAndGet();
        currentResponseTime.set(executionTime);

        if (isSlowQuery || executionTime > maxResponseTime) {
            totalSlowQueries.incrementAndGet();
            logger.warn("检测到慢查询，执行时间: {}ms", executionTime);
        }
    }

    /**
     * 检查数据库状态
     */
    private void checkDatabaseStatus() {
        try {
            long startTime = System.currentTimeMillis();
            
            // 检查数据库连接
            checkDatabaseConnection();
            
            // 检查活跃连接数
            checkActiveConnections();
            
            // 评估压力状态
            evaluatePressureStatus();
            
            long duration = System.currentTimeMillis() - startTime;
            logger.debug("数据库状态检查完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            logger.error("数据库状态检查失败: {}", e.getMessage(), e);
            isUnderPressure = true;
            lastPressureTime = LocalDateTime.now();
        }
    }

    /**
     * 检查数据库连接
     */
    private void checkDatabaseConnection() {
        try {
            long startTime = System.currentTimeMillis();
            Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
            long responseTime = System.currentTimeMillis() - startTime;
            
            currentResponseTime.set(responseTime);
            
            if (result == null || result != 1) {
                throw new RuntimeException("数据库连接测试失败");
            }
            
            logger.debug("数据库连接正常，响应时间: {}ms", responseTime);
            
        } catch (Exception e) {
            logger.error("数据库连接检查失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 检查活跃连接数
     */
    private void checkActiveConnections() {
        try {
            // PostgreSQL查询活跃连接数
            String sql = "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'";
            Integer activeConnections = jdbcTemplate.queryForObject(sql, Integer.class);
            
            if (activeConnections != null) {
                currentConnections.set(activeConnections);
                logger.debug("当前活跃连接数: {}", activeConnections);
                
                if (activeConnections > maxConnections) {
                    logger.warn("活跃连接数超过限制: 当前={}, 限制={}", activeConnections, maxConnections);
                }
            }
            
        } catch (Exception e) {
            logger.error("检查活跃连接数失败: {}", e.getMessage());
        }
    }

    /**
     * 评估压力状态
     */
    private void evaluatePressureStatus() {
        boolean wasUnderPressure = isUnderPressure;
        
        // 评估条件
        boolean highConnections = currentConnections.get() > maxConnections * 0.8;
        boolean highTPS = currentTPS.get() > maxTPS * 0.8;
        boolean slowResponse = currentResponseTime.get() > maxResponseTime;
        boolean tooManySlowQueries = totalSlowQueries.get() > totalQueries.get() * 0.1; // 慢查询超过10%
        
        // 压力判断
        isUnderPressure = highConnections || highTPS || slowResponse || tooManySlowQueries;
        
        if (isUnderPressure && !wasUnderPressure) {
            lastPressureTime = LocalDateTime.now();
            logger.warn("数据库进入高压力状态 - 连接数: {}, TPS: {}, 响应时间: {}ms, 慢查询率: {}%",
                       currentConnections.get(), currentTPS.get(), currentResponseTime.get(),
                       totalQueries.get() > 0 ? (totalSlowQueries.get() * 100 / totalQueries.get()) : 0);
        } else if (!isUnderPressure && wasUnderPressure) {
            logger.info("数据库压力恢复正常");
        }
    }

    /**
     * 重置TPS计数器
     */
    private void resetTPSCounter() {
        int lastTPS = currentTPS.getAndSet(0);
        lastResetTime = LocalDateTime.now();
        
        logger.debug("TPS计数器重置，上一分钟TPS: {}", lastTPS);
    }

    /**
     * 获取监控统计信息
     * @return 监控统计
     */
    public Map<String, Object> getMonitoringStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("enabled", monitorEnabled);
        stats.put("isUnderPressure", isUnderPressure);
        stats.put("totalQueries", totalQueries.get());
        stats.put("totalSlowQueries", totalSlowQueries.get());
        stats.put("currentTPS", currentTPS.get());
        stats.put("currentResponseTime", currentResponseTime.get());
        stats.put("currentConnections", currentConnections.get());
        stats.put("maxConnections", maxConnections);
        stats.put("maxTPS", maxTPS);
        stats.put("maxResponseTime", maxResponseTime);
        
        if (totalQueries.get() > 0) {
            double slowQueryRate = (double) totalSlowQueries.get() / totalQueries.get() * 100;
            stats.put("slowQueryRate", Math.round(slowQueryRate * 100.0) / 100.0);
        } else {
            stats.put("slowQueryRate", 0.0);
        }
        
        stats.put("lastPressureTime", lastPressureTime);
        stats.put("lastResetTime", lastResetTime);
        stats.put("timestamp", LocalDateTime.now());
        
        return stats;
    }

    /**
     * 获取数据库详细信息
     * @return 数据库详细信息
     */
    public Map<String, Object> getDatabaseDetails() {
        Map<String, Object> details = new HashMap<>();
        
        try {
            // PostgreSQL版本信息
            String version = jdbcTemplate.queryForObject("SELECT version()", String.class);
            details.put("version", version);
            
            // 数据库大小
            String sizeSql = "SELECT pg_size_pretty(pg_database_size(current_database()))";
            String dbSize = jdbcTemplate.queryForObject(sizeSql, String.class);
            details.put("databaseSize", dbSize);
            
            // 连接统计
            String connSql = "SELECT count(*) as total, " +
                           "count(CASE WHEN state = 'active' THEN 1 END) as active, " +
                           "count(CASE WHEN state = 'idle' THEN 1 END) as idle " +
                           "FROM pg_stat_activity";
            
            Map<String, Object> connStats = jdbcTemplate.queryForMap(connSql);
            details.put("connections", connStats);
            
            // 锁信息
            String lockSql = "SELECT count(*) FROM pg_locks WHERE granted = false";
            Integer waitingLocks = jdbcTemplate.queryForObject(lockSql, Integer.class);
            details.put("waitingLocks", waitingLocks);
            
        } catch (Exception e) {
            logger.error("获取数据库详细信息失败: {}", e.getMessage());
            details.put("error", e.getMessage());
        }
        
        return details;
    }

    /**
     * 动态调整监控参数
     * @param maxConnections 最大连接数
     * @param maxTPS 最大TPS
     * @param maxResponseTime 最大响应时间
     */
    public void updateThresholds(Integer maxConnections, Integer maxTPS, Long maxResponseTime) {
        if (maxConnections != null && maxConnections > 0) {
            this.maxConnections = maxConnections;
            logger.info("最大连接数阈值调整为: {}", maxConnections);
        }
        
        if (maxTPS != null && maxTPS > 0) {
            this.maxTPS = maxTPS;
            logger.info("最大TPS阈值调整为: {}", maxTPS);
        }
        
        if (maxResponseTime != null && maxResponseTime > 0) {
            this.maxResponseTime = maxResponseTime;
            logger.info("最大响应时间阈值调整为: {}ms", maxResponseTime);
        }
    }

    /**
     * 启用/禁用监控
     * @param enabled 是否启用
     */
    public void setMonitorEnabled(boolean enabled) {
        boolean wasEnabled = this.monitorEnabled;
        this.monitorEnabled = enabled;
        
        if (enabled && !wasEnabled) {
            startMonitoring();
            logger.info("数据库压力监控已启用");
        } else if (!enabled && wasEnabled) {
            logger.info("数据库压力监控已禁用");
        }
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalQueries.set(0);
        totalSlowQueries.set(0);
        currentTPS.set(0);
        queryCount.set(0);
        windowStartTime.set(System.currentTimeMillis());
        lastResetTime = LocalDateTime.now();
        
        logger.info("监控统计信息已重置");
    }

    /**
     * 检查当前是否处于压力状态
     * @return 是否处于压力状态
     */
    public boolean isUnderPressure() {
        return isUnderPressure;
    }

    /**
     * 获取当前TPS
     * @return 当前TPS
     */
    public int getCurrentTPS() {
        return currentTPS.get();
    }

    /**
     * 获取当前响应时间
     * @return 当前响应时间（毫秒）
     */
    public long getCurrentResponseTime() {
        return currentResponseTime.get();
    }

    /**
     * 关闭监控服务
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                logger.info("数据库压力监控服务已关闭");
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}