package fastgatedemo.demo.service;

import fastgatedemo.demo.dto.PersonImportResultDTO;
import fastgatedemo.demo.model.MiddlewarePersonInfo;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.repository.MiddlewarePersonRepository;
import fastgatedemo.demo.repository.PersonRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.time.LocalDateTime;

/**
 * @description 人员匹配服务
 * 负责Excel导入数据与中间库人员信息的匹配，以及本地同步状态的检查
 */
@Service
public class PersonMatchService {

    private static final Logger logger = LoggerFactory.getLogger(PersonMatchService.class);

    @Autowired
    private MiddlewarePersonRepository middlewarePersonRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private PersonService personService;

    /**
     * 批量处理人员匹配和同步状态检查
     */
    public Map<String, Object> processPersonMatching(List<PersonImportResultDTO> importData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始处理人员匹配，共{}条记录", importData.size());

            // 提取所有工号，进行批量查询优化
            List<String> jobNumbers = importData.stream()
                    .map(PersonImportResultDTO::getJobNumber)
                    .filter(jobNum -> !StringUtils.isEmpty(jobNum))
                    .distinct()
                    .collect(Collectors.toList());

            // 直接查询本地人员信息（跳过中间库查询）
            Map<String, PersonInfo> localPersonMap = batchQueryLocalPersonsByJobNumber(jobNumbers);
            
            // 空的中间库数据（不再使用）
            Map<String, MiddlewarePersonInfo> middlewarePersonMap = new HashMap<>();
            Map<String, PersonInfo> localSyncStatusMap = new HashMap<>();

            // 处理每条导入数据的匹配
            int matchedCount = 0;
            int syncedCount = 0;
            int unmatchedCount = 0;

            for (PersonImportResultDTO importPerson : importData) {
                processPersonMatch(importPerson, middlewarePersonMap, localPersonMap, localSyncStatusMap);
                
                // 统计结果
                if (importPerson.getIsMatched()) {
                    matchedCount++;
                    if (importPerson.getIsSynced()) {
                        syncedCount++;
                    }
                } else {
                    unmatchedCount++;
                }
            }

            // 构建结果
            result.put("success", true);
            result.put("message", "人员匹配处理完成");
            result.put("data", importData);
            result.put("statistics", buildStatistics(importData.size(), matchedCount, syncedCount, unmatchedCount));

            logger.info("人员匹配处理完成：总数={}, 匹配成功={}, 已同步={}, 未匹配={}", 
                       importData.size(), matchedCount, syncedCount, unmatchedCount);

        } catch (Exception e) {
            logger.error("人员匹配处理异常", e);
            result.put("success", false);
            result.put("message", "人员匹配处理失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 批量查询中间库人员信息
     */
    private Map<String, MiddlewarePersonInfo> batchQueryMiddlewarePersons(List<String> jobNumbers) {
        Map<String, MiddlewarePersonInfo> resultMap = new HashMap<>();
        
        if (jobNumbers.isEmpty()) {
            return resultMap;
        }

        try {
            // 分批查询，避免IN子句过长
            int batchSize = 500;
            for (int i = 0; i < jobNumbers.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, jobNumbers.size());
                List<String> batchJobNumbers = jobNumbers.subList(i, endIndex);
                
                List<MiddlewarePersonInfo> batchResults = middlewarePersonRepository.findByAccNumIn(batchJobNumbers);
                
                for (MiddlewarePersonInfo person : batchResults) {
                    resultMap.put(person.getAccNum(), person);
                }
            }

            logger.debug("批量查询中间库：查询{}个工号，匹配{}条记录", jobNumbers.size(), resultMap.size());

        } catch (Exception e) {
            logger.error("批量查询中间库人员信息异常", e);
        }

        return resultMap;
    }

    /**
     * 批量查询本地人员信息（按工号查询，用于Excel导入匹配）
     */
    private Map<String, PersonInfo> batchQueryLocalPersonsByJobNumber(List<String> jobNumbers) {
        Map<String, PersonInfo> resultMap = new HashMap<>();
        
        if (jobNumbers.isEmpty()) {
            return resultMap;
        }

        try {
            // 分批查询，避免IN子句过长
            int batchSize = 500;
            for (int i = 0; i < jobNumbers.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, jobNumbers.size());
                List<String> batchJobNumbers = jobNumbers.subList(i, endIndex);
                
                List<PersonInfo> batchResults = personRepository.findByJobNumberIn(batchJobNumbers);
                
                for (PersonInfo person : batchResults) {
                    resultMap.put(person.getPersonCode(), person);
                }
            }

            logger.debug("批量查询本地人员（按工号）：查询{}个工号，匹配{}条记录", jobNumbers.size(), resultMap.size());

        } catch (Exception e) {
            logger.error("批量查询本地人员信息异常", e);
        }

        return resultMap;
    }

    /**
     * 批量查询本地人员同步状态
     */
    private Map<String, PersonInfo> batchQueryLocalPersons(Set<String> personCodes) {
        Map<String, PersonInfo> resultMap = new HashMap<>();
        
        if (personCodes.isEmpty()) {
            return resultMap;
        }

        try {
            List<String> codeList = new ArrayList<>(personCodes);
            
            // 分批查询
            int batchSize = 500;
            for (int i = 0; i < codeList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, codeList.size());
                List<String> batchCodes = codeList.subList(i, endIndex);
                
                List<PersonInfo> batchResults = personRepository.findByPersonCodeIn(batchCodes);
                
                for (PersonInfo person : batchResults) {
                    resultMap.put(person.getPersonCode(), person);
                }
            }

            logger.debug("批量查询本地人员：查询{}个编码，找到{}条记录", personCodes.size(), resultMap.size());

        } catch (Exception e) {
            logger.error("批量查询本地人员信息异常", e);
        }

        return resultMap;
    }

    /**
     * 处理单个人员的匹配（仅使用本地库）
     */
    private void processPersonMatch(PersonImportResultDTO importPerson, 
                                   Map<String, MiddlewarePersonInfo> middlewarePersonMap,
                                   Map<String, PersonInfo> localPersonMap,
                                   Map<String, PersonInfo> localSyncStatusMap) {
        
        String jobNumber = importPerson.getJobNumber();
        
        // 直接查找本地库匹配（跳过中间库）
        PersonInfo localPerson = localPersonMap.get(jobNumber);
        
        if (localPerson != null) {
            // 本地库匹配成功
            importPerson.setLocalMatchSuccess(localPerson);
            
            // 验证姓名是否一致
            validateNameConsistencyLocal(importPerson, localPerson);
            
            // 检查本地库的同步状态（根据sync_flag字段）
            if (localPerson.getSyncFlag() != null && localPerson.getSyncFlag() == 1) {
                importPerson.setSyncStatus(true);
            } else {
                importPerson.setSyncStatus(false);
            }
            
        } else {
            // 未匹配到任何数据
            importPerson.setMatchFailed("工号不存在于本地库");
        }
    }

    /**
     * 验证姓名一致性
     */
    private void validateNameConsistency(PersonImportResultDTO importPerson, MiddlewarePersonInfo middlewarePerson) {
        String excelName = importPerson.getName();
        String middlewareName = middlewarePerson.getAccName();
        
        if (!StringUtils.isEmpty(excelName) && !StringUtils.isEmpty(middlewareName)) {
            if (!excelName.trim().equals(middlewareName.trim())) {
                String currentStatus = importPerson.getMatchStatus();
                importPerson.setMatchStatus(currentStatus + " (姓名不一致: Excel=" + excelName + ", 中间库=" + middlewareName + ")");
                logger.warn("姓名不一致：工号={}, Excel姓名={}, 中间库姓名={}", 
                           importPerson.getJobNumber(), excelName, middlewareName);
            }
        }
    }

    /**
     * 验证姓名一致性（本地库）
     */
    private void validateNameConsistencyLocal(PersonImportResultDTO importPerson, PersonInfo localPerson) {
        String excelName = importPerson.getName();
        String localName = localPerson.getPersonName();
        
        if (!StringUtils.isEmpty(excelName) && !StringUtils.isEmpty(localName)) {
            // 简单的姓名一致性检查
            if (!excelName.trim().equals(localName.trim())) {
                String currentStatus = importPerson.getMatchStatus();
                importPerson.setMatchStatus(currentStatus + "（姓名不一致：Excel=" + excelName + ", 本地=" + localName + "）");
                logger.warn("工号{}姓名不一致：Excel={}, 本地={}", importPerson.getJobNumber(), excelName, localName);
            }
        }
    }

    /**
     * 构建统计信息
     */
    private Map<String, Object> buildStatistics(int totalCount, int matchedCount, int syncedCount, int unmatchedCount) {
        Map<String, Object> statistics = new HashMap<>();
        
        statistics.put("totalCount", totalCount);
        statistics.put("matchedCount", matchedCount);
        statistics.put("syncedCount", syncedCount);
        statistics.put("unmatchedCount", unmatchedCount);
        statistics.put("unsyncedCount", matchedCount - syncedCount);
        
        // 计算百分比
        if (totalCount > 0) {
            statistics.put("matchedRate", String.format("%.1f%%", (double) matchedCount / totalCount * 100));
            statistics.put("syncedRate", String.format("%.1f%%", (double) syncedCount / totalCount * 100));
            statistics.put("unmatchedRate", String.format("%.1f%%", (double) unmatchedCount / totalCount * 100));
        } else {
            statistics.put("matchedRate", "0.0%");
            statistics.put("syncedRate", "0.0%");
            statistics.put("unmatchedRate", "0.0%");
        }
        
        return statistics;
    }

    /**
     * 按状态筛选结果
     */
    public List<PersonImportResultDTO> filterByStatus(List<PersonImportResultDTO> data, String status) {
        if (StringUtils.isEmpty(status) || "all".equals(status)) {
            return data;
        }
        
        return data.stream().filter(person -> {
            switch (status) {
                case "matched":
                    return person.getIsMatched() != null && person.getIsMatched();
                case "unmatched":
                    return person.getIsMatched() == null || !person.getIsMatched();
                case "synced":
                    return person.getIsSynced() != null && person.getIsSynced();
                case "unsynced":
                    return person.getIsMatched() != null && person.getIsMatched() && 
                           (person.getIsSynced() == null || !person.getIsSynced());
                default:
                    return true;
            }
        }).collect(Collectors.toList());
    }

    /**
     * 获取可同步的人员列表（匹配成功但未同步的人员）
     */
    public List<PersonImportResultDTO> getSyncablePerson(List<PersonImportResultDTO> data) {
        return data.stream()
                .filter(person -> person.getIsMatched() != null && person.getIsMatched() && 
                                (person.getIsSynced() == null || !person.getIsSynced()))
                .collect(Collectors.toList());
    }

    /**
     * 同步Excel导入的人员数据到EGS平台
     */
    public Map<String, Object> syncExcelImportedPersons(List<PersonImportResultDTO> unsyncedPersons) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始同步Excel导入人员，共{}条记录", unsyncedPersons.size());

            // 统计变量
            int successCount = 0;
            int failCount = 0;
            List<String> failedPersons = new ArrayList<>();
            List<PersonImportResultDTO> syncedPersonList = new ArrayList<>();
            
            // 遍历每个人员进行同步
            for (PersonImportResultDTO importPerson : unsyncedPersons) {
                try {
                    boolean syncSuccess = false;
                    
                    // 现在只处理本地库匹配的人员（跳过中间库）
                    if (importPerson.getMatchSource() == PersonImportResultDTO.MatchSource.LOCAL) {
                        // 本地库匹配：直接更新并同步
                        syncSuccess = syncLocalMatchedPerson(importPerson);
                    } else {
                        // 不再处理中间库匹配，跳过
                        logger.warn("跳过非本地库匹配的人员同步: {} ({})", importPerson.getName(), importPerson.getJobNumber());
                        syncSuccess = false;
                    }
                    
                    if (syncSuccess) {
                        successCount++;
                        importPerson.setSyncStatus(true);
                        syncedPersonList.add(importPerson);
                        logger.debug("Excel人员同步成功: {} ({})", importPerson.getName(), importPerson.getJobNumber());
                    } else {
                        failCount++;
                        String failReason = "EGS平台同步失败";
                        failedPersons.add(importPerson.getName() + "(" + importPerson.getJobNumber() + ") - " + failReason);
                        logger.warn("Excel人员同步失败: {} ({}) - {}", importPerson.getName(), importPerson.getJobNumber(), failReason);
                    }
                    
                    // 添加延时避免频繁请求
                    Thread.sleep(100);
                    
                } catch (Exception e) {
                    failCount++;
                    String failReason = e.getMessage();
                    failedPersons.add(importPerson.getName() + "(" + importPerson.getJobNumber() + ") - " + failReason);
                    logger.error("Excel人员同步异常: {} ({})", importPerson.getName(), importPerson.getJobNumber(), e);
                }
            }
            
            // 构建返回结果
            result.put("success", true);
            result.put("totalCount", unsyncedPersons.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("data", syncedPersonList);
            
            String message = String.format("Excel人员同步完成 - 总计: %d人, 成功: %d人, 失败: %d人", 
                                          unsyncedPersons.size(), successCount, failCount);
            
            if (failCount > 0) {
                message += "\n失败详情: " + String.join("; ", failedPersons);
                result.put("failedPersons", failedPersons);
            }
            
            result.put("message", message);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", unsyncedPersons.size());
            statistics.put("successCount", successCount);
            statistics.put("failCount", failCount);
            result.put("statistics", statistics);
            
            logger.info("Excel人员同步完成: {}", message);
            
        } catch (Exception e) {
            logger.error("Excel人员同步异常", e);
            result.put("success", false);
            result.put("message", "Excel人员同步异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 同步本地库匹配的人员
     */
    private boolean syncLocalMatchedPerson(PersonImportResultDTO importPerson) {
        try {
            PersonInfo localPerson = importPerson.getLocalMatchedPerson();
            if (localPerson == null) {
                logger.error("本地库匹配人员信息为空: {}", importPerson.getJobNumber());
                return false;
            }
            
            // 更新区域编码和部门编码
            localPerson.setAreaCode("staff");
            localPerson.setDepartmentCode(importPerson.getDepartmentCode());
            
            // 调用EGS平台同步
            boolean syncSuccess = personService.addPerson(localPerson);
            
            if (syncSuccess) {
                // 更新本地数据库同步状态
                localPerson.setSyncFlag(1);
                localPerson.setUpdateTime(LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                personRepository.save(localPerson);
                
                logger.info("本地库人员同步成功: {} -> EGS平台", importPerson.getJobNumber());
                return true;
            } else {
                logger.warn("本地库人员EGS平台同步失败: {}", importPerson.getJobNumber());
                return false;
            }
            
        } catch (Exception e) {
            logger.error("本地库人员同步异常: {}", importPerson.getJobNumber(), e);
            return false;
        }
    }
    
    /**
     * 同步中间库匹配的人员
     */
    private boolean syncMiddlewareMatchedPerson(PersonImportResultDTO importPerson) {
        try {
            MiddlewarePersonInfo middlewarePerson = importPerson.getMatchedPerson();
            if (middlewarePerson == null) {
                logger.error("中间库匹配人员信息为空: {}", importPerson.getJobNumber());
                return false;
            }
            
            // 检查本地是否已存在该人员记录
            Optional<PersonInfo> existingPersonOpt = personRepository.findByPersonCode(middlewarePerson.getPerCode());
            PersonInfo localPerson;
            
            if (existingPersonOpt.isPresent()) {
                // 更新现有记录
                localPerson = existingPersonOpt.get();
                logger.debug("更新现有本地人员记录: {}", middlewarePerson.getPerCode());
            } else {
                // 创建新的本地记录
                localPerson = new PersonInfo();
                localPerson.setPersonCode(middlewarePerson.getPerCode());
                logger.debug("创建新的本地人员记录: {}", middlewarePerson.getPerCode());
            }
            
            // 从中间库数据填充本地记录
            localPerson.setPersonName(middlewarePerson.getAccName());
            localPerson.setGender(middlewarePerson.getGender());
            // 将Integer类型的perType转换为String类型的personType
            if (middlewarePerson.getPerType() != null) {
                localPerson.setPersonType(middlewarePerson.getPerType().toString());
            }
            localPerson.setAreaCode("staff"); // 统一设置为staff
            localPerson.setDepartmentCode(importPerson.getDepartmentCode()); // 使用Excel中的部门编码
            localPerson.setIdcard(middlewarePerson.getIdCard()); // 注意是setIdcard不是setIdCard
            localPerson.setTelephone(middlewarePerson.getTelephone());
            // PersonInfo没有email字段，跳过
            localPerson.setStatus(1); // 启用状态
            localPerson.setSyncFlag(0); // 先设置为未同步，同步成功后再更新
            
            String currentTime = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // PersonInfo没有createTime字段，只设置updateTime
            localPerson.setUpdateTime(currentTime);
            
            // 保存到本地数据库
            personRepository.save(localPerson);
            
            // 调用EGS平台同步
            boolean syncSuccess = personService.addPerson(localPerson);
            
            if (syncSuccess) {
                // 更新同步状态
                localPerson.setSyncFlag(1);
                personRepository.save(localPerson);
                
                logger.info("中间库人员同步成功: {} -> 本地库 -> EGS平台", middlewarePerson.getPerCode());
                return true;
            } else {
                logger.warn("中间库人员EGS平台同步失败: {}", middlewarePerson.getPerCode());
                return false;
            }
            
        } catch (Exception e) {
            logger.error("中间库人员同步异常: {}", importPerson.getJobNumber(), e);
            return false;
        }
    }
} 