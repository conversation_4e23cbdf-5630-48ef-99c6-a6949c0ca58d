package fastgatedemo.demo.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fastgatedemo.demo.config.FastGateConf;
import fastgatedemo.demo.constant.BaseConstant;
import fastgatedemo.demo.dto.EGSFailedFaceExtractionDto;
import fastgatedemo.demo.model.ScheduledTaskInfo;
import fastgatedemo.demo.model.TaskExecutionLog;
import fastgatedemo.demo.model.UniResult;
import fastgatedemo.demo.repository.MiddlewareFacePhotoRepository;
import fastgatedemo.demo.repository.ScheduledTaskInfoRepository;
import fastgatedemo.demo.util.UniHttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * @description EGS平台提取失败人脸记录查询服务
 */
@Service
public class EGSFailedFaceExtractionService {

    private static final Logger logger = LoggerFactory.getLogger(EGSFailedFaceExtractionService.class);

    @Autowired
    private FastGateConf fastGateConf;

    @Autowired
    private ScheduledTaskManagementService taskManagementService;

    @Autowired
    private ScheduledTaskInfoRepository taskInfoRepository;

    @Autowired
    private MiddlewareFacePhotoRepository middlewareFacePhotoRepository;

    // 缓存最近查询结果
    private List<EGSFailedFaceExtractionDto> lastQueryResults = new ArrayList<>();
    private String lastQueryTime = "";
    private Integer lastTotalCount = 0;

    /**
     * 检查任务是否启用
     * @param taskName 任务名称
     * @return 是否启用
     */
    private boolean isTaskEnabled(String taskName) {
        try {
            Optional<ScheduledTaskInfo> taskOpt = taskInfoRepository.findByTaskName(taskName);
            if (taskOpt.isPresent()) {
                Boolean enabled = taskOpt.get().getEnabled();
                return enabled != null && enabled;
            }
            // 如果任务不存在，默认为启用（向后兼容）
            logger.warn("任务 {} 在数据库中不存在，默认为启用状态", taskName);
            return true;
        } catch (Exception e) {
            logger.error("检查任务启用状态失败: {}", taskName, e);
            // 出现异常时默认为启用（避免因为数据库问题导致任务完全停止）
            return true;
        }
    }

    /**
     * 定时查询EGS平台提取失败的人脸记录
     * 每3分钟执行一次
     */
    @Scheduled(fixedRate = 180000) // 3分钟 = 180000毫秒
    public void scheduledQueryFailedFaceExtraction() {
        String taskName = "EGSFailedFaceExtractionTask";

        // 首先检查任务是否启用
        if (!isTaskEnabled(taskName)) {
            logger.info("任务 {} 已禁用，跳过执行", taskName);
            return;
        }

        TaskExecutionLog executionLog = taskManagementService.recordTaskStart(taskName);
        logger.info("开始定时查询EGS平台提取失败的人脸记录");

        try {
            QueryResult queryResult = performQuery(executionLog);
            
            // 更新缓存结果
            this.lastQueryResults = queryResult.getResults();
            this.lastTotalCount = queryResult.getTotalCount();
            this.lastQueryTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            logger.info("定时查询完成：总计{}条提取失败记录", queryResult.getTotalCount());
            
        } catch (Exception e) {
            logger.error("定时查询异常", e);
            taskManagementService.recordTaskFailure(executionLog, "定时查询异常: " + e.getMessage());
        }
    }

    /**
     * 手动触发查询EGS平台提取失败的人脸记录
     * @return 查询结果
     */
    public Map<String, Object> manualQueryFailedFaceExtraction() {
        Map<String, Object> result = new HashMap<>();
        String taskName = "ManualEGSFailedFaceExtractionTask";
        TaskExecutionLog executionLog = taskManagementService.recordTaskStart(taskName);

        logger.info("手动触发查询EGS平台提取失败的人脸记录");

        try {
            QueryResult queryResult = performQuery(executionLog);
            
            // 更新缓存结果
            this.lastQueryResults = queryResult.getResults();
            this.lastTotalCount = queryResult.getTotalCount();
            this.lastQueryTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            result.put("success", true);
            result.put("message", "手动查询完成");
            result.put("totalCount", queryResult.getTotalCount());
            result.put("results", queryResult.getResults());
            result.put("queryTime", this.lastQueryTime);

            return result;

        } catch (Exception e) {
            logger.error("手动查询异常", e);
            taskManagementService.recordTaskFailure(executionLog, "手动查询异常: " + e.getMessage());

            result.put("success", false);
            result.put("message", "手动查询失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取最近查询结果（用于前端展示）
     * @return 查询结果数据
     */
    public Map<String, Object> getLastQueryResults() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("totalCount", this.lastTotalCount);
        result.put("results", this.lastQueryResults);
        result.put("queryTime", this.lastQueryTime);
        result.put("message", "获取缓存结果成功");
        return result;
    }

    /**
     * 执行查询操作的核心方法
     * @param executionLog 执行日志
     * @return 查询结果
     * @throws Exception
     */
    private QueryResult performQuery(TaskExecutionLog executionLog) throws Exception {
        List<EGSFailedFaceExtractionDto> allResults = new ArrayList<>();
        int totalCount = 0;
        int currentPage = 1;
        int pageSize = 50; // 每页查询50条记录
        boolean hasMoreData = true;

        try {
            // 首先进行登录验证，获取Cookie
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);
            logger.info("EGS系统登录验证完成");

            while (hasMoreData) {
                // 构建查询参数
                JSONObject conditionParam = new JSONObject();
                conditionParam.put("departCode", ""); // 空表示查询所有部门
                conditionParam.put("pageSize", String.valueOf(pageSize));
                conditionParam.put("pageNum", String.valueOf(currentPage));
                conditionParam.put("byAddColumnJSONStr", "{}");
                conditionParam.put("keyword", ""); // 空表示不进行关键字过滤
                conditionParam.put("featureNum", "2"); // 2表示提取失败

                // 构建完整URL
                String queryUrl = fastGateConf.getUrl() + BaseConstant.URL_PERSONS_QUERY + 
                                 "?conditionParam=" + URLEncoder.encode(conditionParam.toJSONString(), "UTF-8");

                logger.info("查询EGS平台第{}页提取失败记录: {}", currentPage, queryUrl);

                // 调用EGS平台API
                UniResult apiResult = callEGSPersonsQueryAPI(queryUrl);

                if (apiResult != null && apiResult.getErrCode() == 200) {
                    // 解析返回数据
                    JSONObject data = (JSONObject) apiResult.getData();
                    if (data != null) {
                        totalCount = data.getIntValue("total");
                        int currentPageSize = data.getIntValue("size");
                        JSONArray personList = data.getJSONArray("list");

                        if (personList != null && !personList.isEmpty()) {
                            // 转换数据为DTO对象
                            List<EGSFailedFaceExtractionDto> pageResults = convertToDto(personList);
                            allResults.addAll(pageResults);
                            logger.info("第{}页查询成功，获取{}条记录", currentPage, pageResults.size());
                            
                            // 打印当前页人员编码
                            logPersonCodesForPage(pageResults, currentPage);
                        }

                        // 判断是否还有更多数据
                        hasMoreData = currentPageSize == pageSize && allResults.size() < totalCount;
                        currentPage++;
                    } else {
                        logger.warn("EGS返回数据为空");
                        hasMoreData = false;
                    }
                } else {
                    logger.error("查询EGS平台失败: {}", apiResult != null ? apiResult.getErrMsg() : "未知错误");
                    hasMoreData = false;
                }

                // 添加短暂延时，避免频繁请求
                Thread.sleep(100);
            }

            // 记录任务执行成功
            taskManagementService.recordTaskSuccess(executionLog, 
                String.format("查询完成，共获取%d条提取失败记录", allResults.size()),
                totalCount, allResults.size(), 0);

            // 详细打印提取失败人员的编码和基本信息
            logFailedExtractionPersons(allResults);
            
            // 根据提取失败的人员编码更新face_photo表的同步标识为0
            updateFacePhotoSyncFlags(allResults);

            return new QueryResult(allResults, totalCount);

        } catch (Exception e) {
            logger.error("查询过程中发生异常", e);
            throw e;
        }
    }

    /**
     * 调用EGS平台人员查询API
     * @param queryUrl 查询URL
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callEGSPersonsQueryAPI(String queryUrl) throws IOException {
        try {
            UniResult result = UniHttpUtil.sendHttpGetJson(queryUrl, null);

            // 检查是否为认证失败
            if (isAuthenticationFailure(result)) {
                logger.warn("EGS认证可能失败，尝试重新登录后重试...");

                // 重新登录EGS平台
                if (reLogin()) {
                    logger.info("重新登录成功，重试查询API调用");
                    // 重试API调用
                    result = UniHttpUtil.sendHttpGetJson(queryUrl, null);
                } else {
                    logger.error("重新登录失败");
                    UniResult loginFailResult = new UniResult();
                    loginFailResult.setErrCode(401);
                    loginFailResult.setErrMsg("EGS平台认证失败");
                    return loginFailResult;
                }
            }

            return result;

        } catch (Exception e) {
            logger.error("EGS平台连接失败: {}", e.getMessage());
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("连接EGS平台失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查是否为认证失败
     * @param result API调用结果
     * @return 是否为认证失败
     */
    private boolean isAuthenticationFailure(UniResult result) {
        if (result == null) {
            return false;
        }
        // 1010: 登录状态已失效, 404和401通常表示认证问题
        return result.getErrCode() == 1010 || result.getErrCode() == 404 || result.getErrCode() == 401;
    }

    /**
     * 重新登录EGS平台
     * @return 登录是否成功
     */
    private boolean reLogin() {
        try {
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();

            logger.info("重新登录EGS平台: url={}, user={}", loginUrl, user);

            // 调用现有的登录方法
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);

            logger.info("EGS平台重新登录成功");
            return true;

        } catch (Exception e) {
            logger.error("EGS平台重新登录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将EGS平台返回的JSON数据转换为DTO对象
     * @param personList EGS平台返回的人员列表
     * @return DTO对象列表
     */
    private List<EGSFailedFaceExtractionDto> convertToDto(JSONArray personList) {
        List<EGSFailedFaceExtractionDto> results = new ArrayList<>();
        
        for (int i = 0; i < personList.size(); i++) {
            try {
                JSONObject person = personList.getJSONObject(i);
                EGSFailedFaceExtractionDto dto = new EGSFailedFaceExtractionDto();
                
                // 映射字段
                dto.setPersonCode(person.getString("Code"));
                dto.setPersonName(person.getString("Name"));
                dto.setDepartmentCode(person.getString("DepartCode"));
                dto.setDepartmentName(person.getString("Department"));
                dto.setFeatureStatus(person.getInteger("Feature"));
                dto.setSex(person.getInteger("Sex"));
                dto.setTelephone(person.getString("Telephone"));
                dto.setIdCard(person.getString("IdCard"));
                dto.setIcCard(person.getString("IcCard"));
                dto.setPicNum(person.getInteger("PicNum"));
                dto.setStatus(person.getInteger("Status"));
                dto.setUpdateTime(person.getLong("UpdateTime"));
                
                results.add(dto);
                
            } catch (Exception e) {
                logger.warn("转换人员数据失败，跳过该条记录: {}", e.getMessage());
            }
        }
        
        return results;
    }

    /**
     * 详细打印提取失败人员的编码和基本信息
     * @param results 查询结果列表
     */
    private void logFailedExtractionPersons(List<EGSFailedFaceExtractionDto> results) {
        if (results == null || results.isEmpty()) {
            logger.info("=== EGS平台人脸提取失败人员统计 ===");
            logger.info("未发现提取失败的人员记录");
            logger.info("===============================");
            return;
        }

        logger.info("=== EGS平台人脸提取失败人员统计 ===");
        logger.info("总计提取失败人员数量: {}", results.size());
        
        // 按部门分组统计
        Map<String, List<EGSFailedFaceExtractionDto>> departmentGroups = results.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                person -> person.getDepartmentName() != null ? person.getDepartmentName() : "未知部门"
            ));
        
        logger.info("涉及部门数量: {}", departmentGroups.size());
        
        // 打印每个部门的详细信息
        for (Map.Entry<String, List<EGSFailedFaceExtractionDto>> entry : departmentGroups.entrySet()) {
            String deptName = entry.getKey();
            List<EGSFailedFaceExtractionDto> deptPersons = entry.getValue();
            
            logger.info("--- 部门: {} ({}人) ---", deptName, deptPersons.size());
            
            // 提取该部门所有人员编码
            List<String> personCodes = deptPersons.stream()
                .map(EGSFailedFaceExtractionDto::getPersonCode)
                .filter(code -> code != null && !code.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
            
            if (!personCodes.isEmpty()) {
                logger.info("人员编码列表: {}", String.join(", ", personCodes));
            }
            
            // 打印详细人员信息（前5个）
            int showCount = Math.min(5, deptPersons.size());
            for (int i = 0; i < showCount; i++) {
                EGSFailedFaceExtractionDto person = deptPersons.get(i);
                logger.info("  {}. 编码:{} 姓名:{} 性别:{} 照片数:{} 状态:{}", 
                    (i + 1),
                    person.getPersonCode(),
                    person.getPersonName(),
                    person.getSexDescription(),
                    person.getPicNum(),
                    person.getFeatureStatusDescription()
                );
            }
            
            if (deptPersons.size() > 5) {
                logger.info("  ... 还有{}人（详情请查看API返回结果）", deptPersons.size() - 5);
            }
        }
        
        // 提取所有人员编码并打印
        List<String> allPersonCodes = results.stream()
            .map(EGSFailedFaceExtractionDto::getPersonCode)
            .filter(code -> code != null && !code.trim().isEmpty())
            .distinct()
            .sorted()
            .collect(java.util.stream.Collectors.toList());
        
        logger.info("=== 所有提取失败人员编码汇总 ===");
        logger.info("人员编码总数: {}", allPersonCodes.size());
        
        // 分批打印编码（每行最多10个）
        for (int i = 0; i < allPersonCodes.size(); i += 10) {
            int endIndex = Math.min(i + 10, allPersonCodes.size());
            List<String> batch = allPersonCodes.subList(i, endIndex);
            logger.info("编码({}-{}): {}", i + 1, endIndex, String.join(", ", batch));
        }
        
        logger.info("===============================");
    }

    /**
     * 打印当前页人员编码信息
     * @param pageResults 当前页结果
     * @param currentPage 当前页号
     */
    private void logPersonCodesForPage(List<EGSFailedFaceExtractionDto> pageResults, int currentPage) {
        if (pageResults == null || pageResults.isEmpty()) {
            return;
        }
        
        List<String> personCodes = pageResults.stream()
            .map(person -> String.format("%s(%s)", 
                person.getPersonCode(), 
                person.getPersonName() != null ? person.getPersonName() : "未知"))
            .collect(java.util.stream.Collectors.toList());
        
        logger.info("第{}页人员编码: {}", currentPage, String.join(", ", personCodes));
    }

    /**
     * 根据EGS平台提取失败的人员编码更新face_photo表的同步标识为0
     * @param results EGS查询结果列表
     */
    private void updateFacePhotoSyncFlags(List<EGSFailedFaceExtractionDto> results) {
        if (results == null || results.isEmpty()) {
            logger.info("无提取失败人员，跳过face_photo表同步标识更新");
            return;
        }

        try {
            // 提取所有人员编码
            List<String> personCodes = results.stream()
                .map(EGSFailedFaceExtractionDto::getPersonCode)
                .filter(code -> code != null && !code.trim().isEmpty())
                .distinct()
                .collect(java.util.stream.Collectors.toList());

            if (personCodes.isEmpty()) {
                logger.warn("提取到的人员编码列表为空，跳过face_photo表同步标识更新");
                return;
            }

            logger.info("开始更新face_photo表同步标识，涉及人员编码数量: {}", personCodes.size());

            // 首先查询哪些人员编码在face_photo表中存在记录
            List<String> existingPersonCodes = middlewareFacePhotoRepository.findExistingPerCodes(personCodes);
            
            if (existingPersonCodes.isEmpty()) {
                logger.info("face_photo表中未找到任何匹配的人员记录，无需更新同步标识");
                return;
            }

            logger.info("face_photo表中找到{}条匹配记录，开始批量更新同步标识为0", existingPersonCodes.size());
            logger.info("存在记录的人员编码: {}", String.join(", ", existingPersonCodes));

            // 分批更新，避免单次更新数据量过大
            int batchSize = 50;
            int totalUpdated = 0;
            
            for (int i = 0; i < existingPersonCodes.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, existingPersonCodes.size());
                List<String> batch = existingPersonCodes.subList(i, endIndex);
                
                int updatedCount = middlewareFacePhotoRepository.updateSyncFlagToZeroByPerCodes(batch);
                totalUpdated += updatedCount;
                
                logger.info("批次{}: 更新了{}条face_photo记录的同步标识为0, 涉及人员编码: {}", 
                    (i / batchSize + 1), updatedCount, String.join(", ", batch));
            }

            logger.info("=== face_photo表同步标识更新完成 ===");
            logger.info("总计更新记录数: {}", totalUpdated);
            logger.info("更新的人员编码: {}", String.join(", ", existingPersonCodes));
            
            // 找出不存在记录的人员编码
            List<String> notFoundPersonCodes = personCodes.stream()
                .filter(code -> !existingPersonCodes.contains(code))
                .collect(java.util.stream.Collectors.toList());
            
            if (!notFoundPersonCodes.isEmpty()) {
                logger.info("以下人员编码在face_photo表中未找到记录: {}", String.join(", ", notFoundPersonCodes));
            }
            
            logger.info("=====================================");

        } catch (Exception e) {
            logger.error("更新face_photo表同步标识过程中发生异常", e);
        }
    }

    /**
     * 手动更新指定人员编码的face_photo同步标识为0
     * @param personCodes 人员编码列表
     * @return 更新结果
     */
    public Map<String, Object> manualUpdateFacePhotoSyncFlags(List<String> personCodes) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (personCodes == null || personCodes.isEmpty()) {
                result.put("success", false);
                result.put("message", "人员编码列表不能为空");
                return result;
            }

            logger.info("手动更新face_photo表同步标识，人员编码: {}", String.join(", ", personCodes));

            // 查询存在的人员编码
            List<String> existingPersonCodes = middlewareFacePhotoRepository.findExistingPerCodes(personCodes);
            
            if (existingPersonCodes.isEmpty()) {
                result.put("success", false);
                result.put("message", "face_photo表中未找到任何匹配的人员记录");
                result.put("requestedCodes", personCodes);
                result.put("existingCodes", existingPersonCodes);
                return result;
            }

            // 批量更新
            int updatedCount = middlewareFacePhotoRepository.updateSyncFlagToZeroByPerCodes(existingPersonCodes);
            
            // 找出不存在的编码
            List<String> notFoundCodes = personCodes.stream()
                .filter(code -> !existingPersonCodes.contains(code))
                .collect(java.util.stream.Collectors.toList());

            result.put("success", true);
            result.put("message", "face_photo表同步标识更新完成");
            result.put("totalUpdated", updatedCount);
            result.put("requestedCodes", personCodes);
            result.put("existingCodes", existingPersonCodes);
            result.put("notFoundCodes", notFoundCodes);
            
            logger.info("手动更新完成，更新记录数: {}, 存在的编码: {}, 未找到的编码: {}", 
                updatedCount, String.join(", ", existingPersonCodes), 
                notFoundCodes.isEmpty() ? "无" : String.join(", ", notFoundCodes));

        } catch (Exception e) {
            logger.error("手动更新face_photo表同步标识失败", e);
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 查询结果内部类
     */
    private static class QueryResult {
        private List<EGSFailedFaceExtractionDto> results;
        private int totalCount;

        public QueryResult(List<EGSFailedFaceExtractionDto> results, int totalCount) {
            this.results = results;
            this.totalCount = totalCount;
        }

        public List<EGSFailedFaceExtractionDto> getResults() {
            return results;
        }

        public int getTotalCount() {
            return totalCount;
        }
    }
} 