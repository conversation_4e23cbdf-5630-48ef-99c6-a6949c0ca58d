package fastgatedemo.demo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @description 寝室归宿统计缓存服务
 * 用于缓存统计查询结果，提高查询性能
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class DormitoryStatisticsCache {

    private static final Logger logger = LoggerFactory.getLogger(DormitoryStatisticsCache.class);

    /**
     * 缓存过期时间（分钟）
     * 当天数据缓存5分钟，历史数据缓存30分钟
     */
    private static final int CURRENT_DAY_CACHE_MINUTES = 5;
    private static final int HISTORY_CACHE_MINUTES = 30;

    /**
     * 统计结果缓存
     * Key: 日期字符串, Value: 缓存项
     */
    private final Map<String, CacheItem> statisticsCache = new ConcurrentHashMap<>();

    /**
     * 缓存项内部类
     */
    private static class CacheItem {
        private final Map<String, Object> data;
        private final LocalDateTime cacheTime;
        private final int expireMinutes;

        public CacheItem(Map<String, Object> data, int expireMinutes) {
            this.data = data;
            this.cacheTime = LocalDateTime.now();
            this.expireMinutes = expireMinutes;
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(cacheTime.plusMinutes(expireMinutes));
        }

        public Map<String, Object> getData() {
            return data;
        }

        public LocalDateTime getCacheTime() {
            return cacheTime;
        }
    }

    /**
     * 获取缓存的统计数据
     * @param date 查询日期
     * @return 缓存的统计数据，如果不存在或已过期则返回null
     */
    public Map<String, Object> getCachedStatistics(String date) {
        CacheItem item = statisticsCache.get(date);
        
        if (item == null) {
            logger.debug("缓存未命中: {}", date);
            return null;
        }
        
        if (item.isExpired()) {
            logger.debug("缓存已过期: {}, 缓存时间: {}", date, item.getCacheTime());
            statisticsCache.remove(date);
            return null;
        }
        
        logger.debug("缓存命中: {}, 缓存时间: {}", date, item.getCacheTime());
        return item.getData();
    }

    /**
     * 缓存统计数据
     * @param date 查询日期
     * @param statistics 统计数据
     */
    public void cacheStatistics(String date, Map<String, Object> statistics) {
        if (date == null || statistics == null) {
            return;
        }
        
        // 判断是否为当天数据
        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        int expireMinutes = today.equals(date) ? CURRENT_DAY_CACHE_MINUTES : HISTORY_CACHE_MINUTES;
        
        CacheItem item = new CacheItem(statistics, expireMinutes);
        statisticsCache.put(date, item);
        
        logger.debug("缓存统计数据: {}, 过期时间: {}分钟", date, expireMinutes);
        
        // 清理过期缓存
        cleanExpiredCache();
    }

    /**
     * 清除指定日期的缓存
     * @param date 日期
     */
    public void evictCache(String date) {
        if (statisticsCache.remove(date) != null) {
            logger.info("清除缓存: {}", date);
        }
    }

    /**
     * 清除所有缓存
     */
    public void evictAllCache() {
        int size = statisticsCache.size();
        statisticsCache.clear();
        logger.info("清除所有缓存，共{}项", size);
    }

    /**
     * 获取缓存状态信息
     * @return 缓存状态
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new ConcurrentHashMap<>();
        status.put("totalCacheItems", statisticsCache.size());
        status.put("cacheKeys", statisticsCache.keySet());
        
        // 统计过期项数量
        long expiredCount = statisticsCache.values().stream()
                .mapToLong(item -> item.isExpired() ? 1 : 0)
                .sum();
        status.put("expiredItems", expiredCount);
        
        return status;
    }

    /**
     * 清理过期的缓存项
     */
    private void cleanExpiredCache() {
        try {
            int beforeSize = statisticsCache.size();
            
            // 移除过期的缓存项
            statisticsCache.entrySet().removeIf(entry -> {
                boolean expired = entry.getValue().isExpired();
                if (expired) {
                    logger.debug("清理过期缓存: {}", entry.getKey());
                }
                return expired;
            });
            
            int afterSize = statisticsCache.size();
            if (beforeSize > afterSize) {
                logger.debug("清理过期缓存完成，清理前: {}, 清理后: {}", beforeSize, afterSize);
            }
            
        } catch (Exception e) {
            logger.warn("清理过期缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 预热缓存 - 为常用日期预加载统计数据
     * @param dates 需要预热的日期列表
     * @param statisticsLoader 统计数据加载器
     */
    public void warmUpCache(java.util.List<String> dates, java.util.function.Function<String, Map<String, Object>> statisticsLoader) {
        if (dates == null || dates.isEmpty() || statisticsLoader == null) {
            return;
        }
        
        logger.info("开始预热缓存，日期数量: {}", dates.size());
        
        for (String date : dates) {
            try {
                if (getCachedStatistics(date) == null) {
                    Map<String, Object> statistics = statisticsLoader.apply(date);
                    if (statistics != null) {
                        cacheStatistics(date, statistics);
                        logger.debug("预热缓存成功: {}", date);
                    }
                }
            } catch (Exception e) {
                logger.warn("预热缓存失败: {}, 错误: {}", date, e.getMessage());
            }
        }
        
        logger.info("缓存预热完成");
    }

    /**
     * 检查缓存健康状态
     * @return 健康状态信息
     */
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new ConcurrentHashMap<>();
        
        try {
            int totalItems = statisticsCache.size();
            long expiredItems = statisticsCache.values().stream()
                    .mapToLong(item -> item.isExpired() ? 1 : 0)
                    .sum();
            
            health.put("status", "healthy");
            health.put("totalItems", totalItems);
            health.put("expiredItems", expiredItems);
            health.put("activeItems", totalItems - expiredItems);
            health.put("cacheHitRate", calculateHitRate());
            
        } catch (Exception e) {
            health.put("status", "error");
            health.put("error", e.getMessage());
        }
        
        return health;
    }

    /**
     * 计算缓存命中率（简化实现）
     * @return 命中率百分比
     */
    private double calculateHitRate() {
        // 这里可以实现更复杂的命中率统计
        // 简化实现，返回活跃缓存项的比例
        int totalItems = statisticsCache.size();
        if (totalItems == 0) return 0.0;
        
        long activeItems = statisticsCache.values().stream()
                .mapToLong(item -> item.isExpired() ? 0 : 1)
                .sum();
        
        return (double) activeItems / totalItems * 100;
    }
}
