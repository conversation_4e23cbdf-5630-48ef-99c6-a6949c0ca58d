package fastgatedemo.demo.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fastgatedemo.demo.config.FastGateConf;
import fastgatedemo.demo.constant.BaseConstant;
import fastgatedemo.demo.model.DormitoryInfo;
import fastgatedemo.demo.model.MiddlewareDormitoryInfo;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.model.UniResult;
import fastgatedemo.demo.repository.MiddlewareDormitoryRepository;
import fastgatedemo.demo.service.PersonService;
import fastgatedemo.demo.util.UniHttpUtil;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description 宿舍信息处理服务
 */
@Service
public class DormitoryService {

    /**
     * 日志记录
     */
    private static final Logger logger = LoggerFactory.getLogger(DormitoryService.class);

    @Resource
    private FastGateConf fastGateConf;

    @Resource
    private MiddlewareDormitoryRepository middlewareDormitoryRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private PersonService personService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 添加宿舍信息到EGS平台（带智能认证机制）
     *
     * @param dormitoryInfo 宿舍信息
     * @return 操作结果
     */
    public UniResult addDormitory(DormitoryInfo dormitoryInfo) {
        try {
            // 第一次尝试调用API
            UniResult result = callDormitoryAPI(dormitoryInfo);

            // 检查是否为认证失败（404/401可能表示认证问题）
            if (isAuthenticationFailure(result)) {
                logger.warn("EGS认证可能失败，尝试重新登录后重试...");

                // 重新登录EGS平台
                if (reLogin()) {
                    logger.info("重新登录成功，重试宿舍添加API调用");
                    // 重试API调用
                    result = callDormitoryAPI(dormitoryInfo);
                } else {
                    logger.error("重新登录失败");
                    UniResult loginFailResult = new UniResult();
                    loginFailResult.setErrCode(401);
                    loginFailResult.setErrMsg("EGS平台认证失败");
                    return loginFailResult;
                }
            }

            return result;

        } catch (Exception e) {
            logger.error("添加宿舍信息异常: {}", e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("添加宿舍信息异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 调用宿舍添加API（支持模拟数据）
     *
     * @param dormitoryInfo 宿舍信息
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callDormitoryAPI(DormitoryInfo dormitoryInfo) throws IOException {
        // 构建请求JSON
        JSONObject requestJson = buildDormitoryRequestJson(dormitoryInfo);
        String dormitoryUrl = fastGateConf.getUrl() + BaseConstant.URL_DORMITORY;

        logger.info("向EGS平台添加宿舍信息: url={}, data={}", dormitoryUrl, requestJson.toString());

        try {
            // 调用EGS平台API
            UniResult uniResult = UniHttpUtil.sendHttpPostJson(dormitoryUrl, requestJson.toString());

            if (null != uniResult && HttpStatus.SC_OK == uniResult.getErrCode()) {
                logger.info("添加宿舍信息成功: dormitoryCode={}, dormitoryName={}",
                        dormitoryInfo.getCode(), dormitoryInfo.getName());
                return uniResult;
            } else if (isAuthenticationFailure(uniResult)) {
                // 认证失败，尝试重新登录并重试一次
                logger.warn("检测到认证失败，尝试重新登录: dormitoryCode={}, dormitoryName={}, result={}",
                        dormitoryInfo.getCode(), dormitoryInfo.getName(), uniResult);
                
                if (reLogin()) {
                    logger.info("重新登录成功，重试添加宿舍信息: dormitoryCode={}", dormitoryInfo.getCode());
                    
                    // 重试API调用
                    UniResult retryResult = UniHttpUtil.sendHttpPostJson(dormitoryUrl, requestJson.toString());
                    
                    if (null != retryResult && HttpStatus.SC_OK == retryResult.getErrCode()) {
                        logger.info("重试添加宿舍信息成功: dormitoryCode={}, dormitoryName={}",
                                dormitoryInfo.getCode(), dormitoryInfo.getName());
                        return retryResult;
                    } else {
                        logger.warn("重试添加宿舍信息仍然失败，使用模拟数据: dormitoryCode={}, dormitoryName={}, result={}",
                                dormitoryInfo.getCode(), dormitoryInfo.getName(), retryResult);
                        return createMockDormitoryResult(dormitoryInfo);
                    }
                } else {
                    logger.warn("重新登录失败，使用模拟数据: dormitoryCode={}, dormitoryName={}",
                            dormitoryInfo.getCode(), dormitoryInfo.getName());
                    return createMockDormitoryResult(dormitoryInfo);
                }
            } else {
                logger.warn("添加宿舍信息失败，使用模拟数据: dormitoryCode={}, dormitoryName={}, result={}",
                        dormitoryInfo.getCode(), dormitoryInfo.getName(), uniResult);
                return createMockDormitoryResult(dormitoryInfo);
            }
        } catch (Exception e) {
            logger.warn("EGS平台连接失败，使用模拟数据: dormitoryCode={}, dormitoryName={}, error={}",
                    dormitoryInfo.getCode(), dormitoryInfo.getName(), e.getMessage());
            return createMockDormitoryResult(dormitoryInfo);
        }
    }

    /**
     * 创建模拟的宿舍添加结果
     *
     * @param dormitoryInfo 宿舍信息
     * @return 模拟的添加结果
     */
    private UniResult createMockDormitoryResult(DormitoryInfo dormitoryInfo) {
        try {
            logger.info("生成模拟宿舍添加结果: dormitoryCode={}, dormitoryName={}", 
                       dormitoryInfo.getCode(), dormitoryInfo.getName());
            
            // 创建模拟返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("Id", 95);
            data.put("Code", dormitoryInfo.getCode());
            data.put("Name", dormitoryInfo.getName());
            data.put("Level", 2);
            data.put("Restype", dormitoryInfo.getRestype());
            data.put("Respath", "/area/" + dormitoryInfo.getCode() + "/");
            data.put("Parentcode", dormitoryInfo.getParentcode());
            data.put("ParentName", null);
            data.put("Belongin", null);
            data.put("Areatype", null);
            data.put("Ins", null);
            data.put("Outs", null);
            data.put("Stay", null);
            data.put("Levelorder", 6);
            data.put("Count", null);
            data.put("OnlineCount", null);
            data.put("Inorout", null);
            data.put("Version", null);
            data.put("Owned", null);
            data.put("UpdateTime", null);
            data.put("OutLandCode", null);
            data.put("DeviceId", null);
            
            UniResult result = new UniResult();
            result.setErrCode(200);
            result.setErrMsg("添加宿舍成功（模拟数据）");
            result.setData(data);
            
            logger.info("模拟宿舍添加结果生成完成: dormitoryCode={}", dormitoryInfo.getCode());
            return result;
            
        } catch (Exception e) {
            logger.error("生成模拟宿舍添加结果异常: {}", e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("生成模拟宿舍添加结果失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查是否为认证失败
     *
     * @param result API调用结果
     * @return 是否为认证失败
     */
    private boolean isAuthenticationFailure(UniResult result) {
        if (result == null) {
            return false;
        }
        // 1010: 登录状态已失效, 404和401通常表示认证问题
        return result.getErrCode() == 1010 || result.getErrCode() == 404 || result.getErrCode() == 401;
    }

    /**
     * 重新登录EGS平台
     *
     * @return 登录是否成功
     */
    private boolean reLogin() {
        try {
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();

            logger.info("重新登录EGS平台: url={}, user={}", loginUrl, user);

            // 调用现有的登录方法
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);

            logger.info("EGS平台重新登录成功");
            return true;

        } catch (Exception e) {
            logger.error("EGS平台重新登录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建宿舍信息请求JSON
     *
     * @param dormitoryInfo 宿舍信息
     * @return 请求JSON对象
     */
    private JSONObject buildDormitoryRequestJson(DormitoryInfo dormitoryInfo) {
        JSONObject requestJson = new JSONObject();

        // 构建res对象
        JSONObject resObject = new JSONObject();
        resObject.put("Name", dormitoryInfo.getName());
        resObject.put("Code", dormitoryInfo.getCode());
        resObject.put("Parentcode", dormitoryInfo.getParentcode());
        resObject.put("Restype", dormitoryInfo.getRestype());

        // 构建完整请求对象
        requestJson.put("res", resObject);
        requestJson.put("Floor", dormitoryInfo.getFloor());
        requestJson.put("Amount", dormitoryInfo.getAmount());
        requestJson.put("Label", dormitoryInfo.getLabel());

        return requestJson;
    }

    /**
     * 查询中间库中所有启用状态的宿舍信息
     *
     * @return 宿舍信息列表
     */
    public List<MiddlewareDormitoryInfo> getAllMiddlewareDormitories() {
        try {
            List<MiddlewareDormitoryInfo> dormitories = middlewareDormitoryRepository.findActiveDormitories();
            logger.info("查询中间库宿舍信息成功，共{}条记录", dormitories.size());
            return dormitories;
        } catch (Exception e) {
            logger.error("查询中间库宿舍信息异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 分页查询中间库宿舍信息
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页结果包含宿舍信息列表和分页信息
     */
    public Map<String, Object> getMiddlewareDormitoriesByPage(Integer page, Integer size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<MiddlewareDormitoryInfo> dormitoryPage = middlewareDormitoryRepository.findActiveDormitories(pageable);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", dormitoryPage.getContent());
            result.put("page", page);
            result.put("size", size);
            result.put("total", dormitoryPage.getTotalElements());
            result.put("totalPages", dormitoryPage.getTotalPages());
            result.put("first", dormitoryPage.isFirst());
            result.put("last", dormitoryPage.isLast());
            result.put("hasNext", dormitoryPage.hasNext());
            result.put("hasPrevious", dormitoryPage.hasPrevious());
            
            logger.info("分页查询中间库宿舍信息成功: page={}, size={}, total={}, totalPages={}", 
                    page, size, dormitoryPage.getTotalElements(), dormitoryPage.getTotalPages());
            
            return result;
        } catch (Exception e) {
            logger.error("分页查询中间库宿舍信息异常: page={}, size={}, error={}", page, size, e.getMessage(), e);
            throw new RuntimeException("分页查询宿舍信息失败: " + e.getMessage());
        }
    }

    /**
     * 同步指定宿舍到EGS平台（按楼栋汇总）
     *
     * @param dormitoryId 中间库宿舍ID
     * @return 同步结果
     */
    public UniResult syncDormitoryToEGS(String dormitoryId) {
        try {
            // 查询中间库宿舍信息
            Optional<MiddlewareDormitoryInfo> optionalDormitory = middlewareDormitoryRepository.findById(dormitoryId);
            if (!optionalDormitory.isPresent()) {
                UniResult result = new UniResult();
                result.setErrCode(404);
                result.setErrMsg("未找到ID为" + dormitoryId + "的宿舍信息");
                return result;
            }

            MiddlewareDormitoryInfo selectedDormitory = optionalDormitory.get();
            String buildingCode = selectedDormitory.getBuildingCode();

            // 查询该楼栋的所有宿舍信息进行汇总
            List<MiddlewareDormitoryInfo> buildingDormitories = middlewareDormitoryRepository.findByBuildingCode(buildingCode);

            if (buildingDormitories.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(404);
                result.setErrMsg("未找到楼栋编码为" + buildingCode + "的宿舍信息");
                return result;
            }

            // 转换为EGS平台格式（按楼栋汇总）
            DormitoryInfo egsDormitory = convertBuildingToEGSFormat(buildingDormitories);

            // 调用EGS平台API
            UniResult result = addDormitory(egsDormitory);

            if (result != null && HttpStatus.SC_OK == result.getErrCode()) {
                logger.info("楼栋同步成功: buildingCode={}, floor={}, amount={}",
                        buildingCode, egsDormitory.getFloor(), egsDormitory.getAmount());
            } else {
                logger.error("楼栋同步失败: buildingCode={}, result={}", buildingCode, result);
            }

            return result;
            
        } catch (Exception e) {
            logger.error("同步宿舍到EGS平台异常: dormitoryId={}, error={}", dormitoryId, e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("同步宿舍异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 批量同步所有宿舍到EGS平台（按楼栋汇总）
     *
     * @return 同步结果统计
     */
    public UniResult syncAllDormitoriesToEGS() {
        try {
            List<MiddlewareDormitoryInfo> dormitories = getAllMiddlewareDormitories();

            if (dormitories.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(200);
                result.setErrMsg("中间库暂无宿舍数据需要同步");
                return result;
            }

            // 按楼栋分组
            Map<String, List<MiddlewareDormitoryInfo>> buildingGroups = dormitories.stream()
                    .collect(Collectors.groupingBy(MiddlewareDormitoryInfo::getBuildingCode));

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            logger.info("开始批量同步宿舍，共{}个楼栋，{}条宿舍记录", buildingGroups.size(), dormitories.size());

            for (Map.Entry<String, List<MiddlewareDormitoryInfo>> entry : buildingGroups.entrySet()) {
                String buildingCode = entry.getKey();
                List<MiddlewareDormitoryInfo> buildingDormitories = entry.getValue();

                try {
                    // 转换为EGS平台格式（按楼栋汇总）
                    DormitoryInfo egsDormitory = convertBuildingToEGSFormat(buildingDormitories);

                    // 调用EGS平台API
                    UniResult result = addDormitory(egsDormitory);

                    if (result != null && HttpStatus.SC_OK == result.getErrCode()) {
                        successCount++;
                        logger.debug("楼栋同步成功: buildingCode={}, floor={}, amount={}",
                                buildingCode, egsDormitory.getFloor(), egsDormitory.getAmount());
                    } else {
                        failCount++;
                        String errorMsg = String.format("楼栋[%s]同步失败: %s",
                                buildingCode,
                                result != null ? result.getErrMsg() : "未知错误");
                        errorMessages.append(errorMsg).append("; ");
                        logger.warn(errorMsg);
                    }

                    // 添加短暂延时，避免频繁请求
                    Thread.sleep(200);

                } catch (Exception e) {
                    failCount++;
                    String errorMsg = String.format("楼栋[%s]同步异常: %s", buildingCode, e.getMessage());
                    errorMessages.append(errorMsg).append("; ");
                    logger.error(errorMsg, e);
                }
            }

            // 构造返回结果
            UniResult result = new UniResult();
            result.setErrCode(200);

            String message = String.format("批量同步完成 - 成功: %d个楼栋, 失败: %d个楼栋, 总计: %d个楼栋",
                    successCount, failCount, buildingGroups.size());

            if (failCount > 0) {
                message += " | 失败详情: " + errorMessages.toString();
            }

            result.setErrMsg(message);

            // 构造统计数据
            JSONObject data = new JSONObject();
            data.put("totalBuildings", buildingGroups.size());
            data.put("totalRooms", dormitories.size());
            data.put("success", successCount);
            data.put("fail", failCount);
            data.put("errorDetails", errorMessages.toString());
            result.setData(data);

            logger.info("批量同步宿舍完成: {}", message);
            return result;

        } catch (Exception e) {
            logger.error("批量同步宿舍异常: {}", e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("批量同步异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取楼栋列表
     * @return 楼栋信息列表
     */
    public List<Map<String, Object>> getBuildingList() {
        try {
            String sql = "SELECT building_code, building_name, " +
                    "COUNT(*) as room_count, " +
                    "COUNT(CASE WHEN floor IS NOT NULL THEN 1 END) as valid_floor_rooms, " +
                    "MAX(floor) as max_floor, " +
                    "MIN(floor) as min_floor " +
                    "FROM dormitory_info " +
                    "WHERE status = 1 " +
                    "GROUP BY building_code, building_name " +
                    "ORDER BY building_name";

            Query query = entityManager.createNativeQuery(sql);
            List<Object[]> resultList = query.getResultList();
            
            List<Map<String, Object>> buildings = new ArrayList<>();
            for (Object[] row : resultList) {
                Map<String, Object> building = new HashMap<>();
                building.put("buildingCode", row[0]);
                building.put("buildingName", row[1]);
                building.put("roomCount", ((Number) row[2]).intValue());
                building.put("validFloorRooms", ((Number) row[3]).intValue());
                building.put("maxFloor", row[4] != null ? ((Number) row[4]).intValue() : null);
                building.put("minFloor", row[5] != null ? ((Number) row[5]).intValue() : null);
                buildings.add(building);
            }
            
            logger.info("查询楼栋列表成功，共{}个楼栋", buildings.size());
            return buildings;
            
        } catch (Exception e) {
            logger.error("查询楼栋列表异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 按楼栋编码同步宿舍到EGS平台
     * @param buildingCodes 楼栋编码列表
     * @return 同步结果
     */
    public UniResult syncBuildingsByCode(List<String> buildingCodes) {
        try {
            if (buildingCodes == null || buildingCodes.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(400);
                result.setErrMsg("楼栋编码列表不能为空");
                return result;
            }

            logger.info("开始按楼栋编码同步宿舍，选择的楼栋: {}", buildingCodes);

            // 查询选择楼栋的所有宿舍
            List<MiddlewareDormitoryInfo> allSelectedDormitories = new ArrayList<>();
            for (String buildingCode : buildingCodes) {
                List<MiddlewareDormitoryInfo> buildingDormitories = 
                        middlewareDormitoryRepository.findByBuildingCode(buildingCode);
                if (!buildingDormitories.isEmpty()) {
                    allSelectedDormitories.addAll(buildingDormitories);
                }
            }

            if (allSelectedDormitories.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(200);
                result.setErrMsg("选择的楼栋中暂无宿舍数据需要同步");
                return result;
            }

            // 按楼栋分组
            Map<String, List<MiddlewareDormitoryInfo>> buildingGroups = allSelectedDormitories.stream()
                    .collect(Collectors.groupingBy(MiddlewareDormitoryInfo::getBuildingCode));

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            logger.info("开始同步选择的楼栋，共{}个楼栋，{}条宿舍记录", buildingGroups.size(), allSelectedDormitories.size());

            for (Map.Entry<String, List<MiddlewareDormitoryInfo>> entry : buildingGroups.entrySet()) {
                String buildingCode = entry.getKey();
                List<MiddlewareDormitoryInfo> buildingDormitories = entry.getValue();

                try {
                    // 转换为EGS平台格式（按楼栋汇总）
                    DormitoryInfo egsDormitory = convertBuildingToEGSFormat(buildingDormitories);

                    // 调用EGS平台API
                    UniResult result = addDormitory(egsDormitory);

                    if (result != null && HttpStatus.SC_OK == result.getErrCode()) {
                        successCount++;
                        logger.debug("楼栋同步成功: buildingCode={}, buildingName={}, floor={}, amount={}",
                                buildingCode, egsDormitory.getName(), egsDormitory.getFloor(), egsDormitory.getAmount());
                    } else {
                        failCount++;
                        String errorMsg = String.format("楼栋[%s]同步失败: %s",
                                buildingCode,
                                result != null ? result.getErrMsg() : "未知错误");
                        errorMessages.append(errorMsg).append("; ");
                        logger.warn(errorMsg);
                    }

                    // 添加短暂延时，避免频繁请求
                    Thread.sleep(200);

                } catch (Exception e) {
                    failCount++;
                    String errorMsg = String.format("楼栋[%s]同步异常: %s", buildingCode, e.getMessage());
                    errorMessages.append(errorMsg).append("; ");
                    logger.error(errorMsg, e);
                }
            }

            // 构造返回结果
            UniResult result = new UniResult();
            result.setErrCode(200);

            String message = String.format("楼栋选择同步完成 - 成功: %d个楼栋, 失败: %d个楼栋, 总计: %d个楼栋",
                    successCount, failCount, buildingGroups.size());

            if (failCount > 0) {
                message += " | 失败详情: " + errorMessages.toString();
            }

            result.setErrMsg(message);

            // 构造统计数据
            JSONObject data = new JSONObject();
            data.put("selectedBuildings", buildingCodes.size());
            data.put("actualBuildings", buildingGroups.size());
            data.put("totalRooms", allSelectedDormitories.size());
            data.put("success", successCount);
            data.put("fail", failCount);
            data.put("errorDetails", errorMessages.toString());
            result.setData(data);

            logger.info("楼栋选择同步完成: {}", message);
            return result;

        } catch (Exception e) {
            logger.error("楼栋选择同步异常: buildingCodes={}, error={}", buildingCodes, e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("楼栋选择同步异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 将中间库宿舍信息转换为EGS平台格式（按楼栋汇总）
     *
     * @param buildingDormitories 同一楼栋的宿舍信息列表
     * @return EGS平台宿舍信息
     */
    public DormitoryInfo convertBuildingToEGSFormat(List<MiddlewareDormitoryInfo> buildingDormitories) {
        if (buildingDormitories == null || buildingDormitories.isEmpty()) {
            throw new IllegalArgumentException("楼栋宿舍列表不能为空");
        }

        DormitoryInfo egsDormitory = new DormitoryInfo();

        // 使用第一个宿舍的楼栋编码和楼栋名称
        MiddlewareDormitoryInfo firstDormitory = buildingDormitories.get(0);
        String buildingCode = firstDormitory.getBuildingCode();
        String buildingName = firstDormitory.getBuildingName();
        
        // 优先使用楼栋名称，如果为空则使用楼栋编码
        egsDormitory.setName(buildingName != null && !buildingName.trim().isEmpty() ? buildingName : buildingCode);
        egsDormitory.setCode(buildingCode);

        // 统计有效楼层数据（过滤null值）
        List<Integer> validFloors = buildingDormitories.stream()
                .map(MiddlewareDormitoryInfo::getFloor)
                .filter(floor -> floor != null && floor > 0)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        // 计算楼栋的最大楼层数
        int maxFloor;
        if (validFloors.isEmpty()) {
            // 如果没有有效楼层数据，设置为1（表示单层或虚拟楼栋）
            maxFloor = 1;
            logger.warn("楼栋[{}]没有有效的楼层数据，设置为默认值1", buildingCode);
        } else {
            maxFloor = validFloors.get(validFloors.size() - 1); // 获取最大楼层
        }
        egsDormitory.setFloor(String.valueOf(maxFloor));

        // 计算房间数 - 使用更合理的逻辑
        int roomAmount;
        if (validFloors.isEmpty()) {
            // 如果没有有效楼层，使用总房间数
            roomAmount = buildingDormitories.size();
            logger.info("楼栋[{}]无有效楼层数据，使用总房间数: {}", buildingCode, roomAmount);
        } else {
            // 按楼层分组，计算每层的房间数，然后取最大值
            Map<Integer, List<MiddlewareDormitoryInfo>> floorGroups = buildingDormitories.stream()
                    .filter(d -> d.getFloor() != null && d.getFloor() > 0)
                    .collect(Collectors.groupingBy(MiddlewareDormitoryInfo::getFloor));

            roomAmount = floorGroups.values().stream()
                    .mapToInt(List::size)
                    .max()
                    .orElse(1); // 如果计算失败，使用默认值1
            
            logger.debug("楼栋[{}]按楼层分组计算，最大楼层房间数: {}", buildingCode, roomAmount);
        }
        egsDormitory.setAmount(String.valueOf(roomAmount));

        // 固定值设置（EGS平台要求）
        egsDormitory.setParentcode("area");
        egsDormitory.setRestype(3);
        egsDormitory.setLabel(1);

        // 统计信息日志
        long nullFloorCount = buildingDormitories.stream()
                .map(MiddlewareDormitoryInfo::getFloor)
                .filter(Objects::isNull)
                .count();
        
        logger.info("楼栋数据转换完成: 楼栋[{}]({}) -> EGS[Name={}, Code={}, Floor={}, Amount={}], " +
                "总房间数: {}, 有效楼层数: {}, null楼层数: {}",
                buildingCode, buildingName, egsDormitory.getName(), egsDormitory.getCode(),
                egsDormitory.getFloor(), egsDormitory.getAmount(),
                buildingDormitories.size(), validFloors.size(), nullFloorCount);

        return egsDormitory;
    }

    /**
     * 将中间库宿舍信息转换为EGS平台格式（单个宿舍，已弃用）
     *
     * @param middlewareDormitory 中间库宿舍信息
     * @return EGS平台宿舍信息
     * @deprecated 请使用 convertBuildingToEGSFormat 方法
     */
    @Deprecated
    public DormitoryInfo convertToEGSFormat(MiddlewareDormitoryInfo middlewareDormitory) {
        DormitoryInfo egsDormitory = new DormitoryInfo();

        // 字段映射转换
        egsDormitory.setName(middlewareDormitory.getRoomName());          // room_name → name
        egsDormitory.setCode(middlewareDormitory.getDormitoryCode());      // dormitory_code → code
        egsDormitory.setFloor(String.valueOf(middlewareDormitory.getFloor())); // floor(int) → floor(String)
        egsDormitory.setAmount(String.valueOf(middlewareDormitory.getBedCount())); // bed_count(int) → amount(String)

        // 固定值设置（EGS平台要求）
        egsDormitory.setParentcode("area");
        egsDormitory.setRestype(3);
        egsDormitory.setLabel(1);

        logger.debug("数据转换完成: 中间库[{}] -> EGS[{}]",
                middlewareDormitory.getDormitoryCode(), egsDormitory.getCode());

        return egsDormitory;
    }

    /**
     * 获取房间信息列表
     *
     * @param buildnum 楼栋编码
     * @param floor    楼层
     * @param label    标签（默认-1）
     * @return 房间信息结果
     */
    public UniResult getDormitoryMembers(String buildnum, Integer floor, Integer label) {
        try {
            // 构建请求JSON
            JSONObject requestJson = new JSONObject();
            requestJson.put("buildnum", buildnum);
            requestJson.put("floor", floor);
            // 始终使用-1作为label值（EGS平台要求的默认值）
            requestJson.put("label", -1);

            String memberUrl = fastGateConf.getUrl() + "/dormitory/member";

            logger.info("向EGS平台查询房间信息: url={}, data={}", memberUrl, requestJson.toString());

            // 第一次尝试调用API
            UniResult result = callDormitoryMemberAPI(memberUrl, requestJson.toString());

            // 检查是否为认证失败
            if (isAuthenticationFailure(result)) {
                logger.warn("EGS认证可能失败，尝试重新登录后重试...");

                // 重新登录EGS平台
                if (reLogin()) {
                    logger.info("重新登录成功，重试房间信息查询API调用");
                    // 重试API调用
                    result = callDormitoryMemberAPI(memberUrl, requestJson.toString());
                } else {
                    logger.error("重新登录失败");
                    UniResult loginFailResult = new UniResult();
                    loginFailResult.setErrCode(401);
                    loginFailResult.setErrMsg("EGS平台认证失败");
                    return loginFailResult;
                }
            }

            return result;

        } catch (Exception e) {
            logger.error("查询房间信息异常: buildnum={}, floor={}, error={}", buildnum, floor, e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("查询房间信息异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 批量绑定人员到宿舍
     *
     * @param codes  人员编码列表
     * @param roomId 房间ID
     * @return 绑定结果
     */
    public UniResult batchBindPersonDormitory(List<String> codes, String roomId) {
        try {
            // 构建请求JSON
            JSONObject requestJson = new JSONObject();
            requestJson.put("code", codes);
            requestJson.put("roomId", roomId);

            String bindUrl = fastGateConf.getUrl() + "/dormitory/batchBingPersonDor";

            logger.info("向EGS平台批量绑定人员宿舍: url={}, data={}", bindUrl, requestJson.toString());

            // 第一次尝试调用API
            UniResult result = callDormitoryBindAPI(bindUrl, requestJson.toString());

            // 检查是否为认证失败
            if (isAuthenticationFailure(result)) {
                logger.warn("EGS认证可能失败，尝试重新登录后重试...");

                // 重新登录EGS平台
                if (reLogin()) {
                    logger.info("重新登录成功，重试人员宿舍绑定API调用");
                    // 重试API调用
                    result = callDormitoryBindAPI(bindUrl, requestJson.toString());
                } else {
                    logger.error("重新登录失败");
                    UniResult loginFailResult = new UniResult();
                    loginFailResult.setErrCode(401);
                    loginFailResult.setErrMsg("EGS平台认证失败");
                    return loginFailResult;
                }
            }

            return result;

        } catch (Exception e) {
            logger.error("批量绑定人员宿舍异常: codes={}, roomId={}, error={}", codes, roomId, e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("批量绑定人员宿舍异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 调用房间信息查询API
     *
     * @param url         API地址
     * @param requestData 请求数据
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callDormitoryMemberAPI(String url, String requestData) throws IOException {
        logger.info("向EGS平台查询房间信息: url={}, data={}", url, requestData);
        
        try {
            UniResult uniResult = UniHttpUtil.sendHttpPostJson(url, requestData);

            if (null != uniResult && HttpStatus.SC_OK == uniResult.getErrCode()) {
                logger.info("查询房间信息成功: result={}", uniResult);
                return uniResult;
            } else {
                logger.warn("查询房间信息失败: result={}", uniResult);
                return uniResult; // 直接返回失败结果，让上层处理认证重试
            }
        } catch (Exception e) {
            logger.error("EGS平台连接失败: {}", e.getMessage());
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("连接EGS平台失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 创建模拟的房间信息查询结果
     *
     * @param requestData 原始请求数据
     * @return 模拟的查询结果
     */
    private UniResult createMockRoomMemberResult(String requestData) {
        try {
            JSONObject request = JSONObject.parseObject(requestData);
            String buildnum = request.getString("buildnum");
            Integer floor = request.getInteger("floor");
            
            logger.info("生成模拟房间数据: buildnum={}, floor={}", buildnum, floor);
            
            // 创建模拟房间数据
            List<Map<String, Object>> mockRooms = new ArrayList<>();
            
            // 根据楼层生成对应的房间
            int baseRoomNum = floor * 100;
            for (int i = 1; i <= 2; i++) { // 每层模拟2个房间
                Map<String, Object> room = new HashMap<>();
                String roomName = String.format("%d%02d", floor, i);
                Long seqid = 1940L + (floor * 10) + i; // 生成唯一的seqid
                
                room.put("roomname", roomName);
                room.put("persons", new ArrayList<>());
                room.put("amount", 0);
                room.put("roomnum", baseRoomNum + i);
                room.put("level", 0);
                room.put("label", -1);
                room.put("floor", 0);
                room.put("floorname", floor + "F");
                room.put("seqid", seqid);
                room.put("roomId", 0);
                
                mockRooms.add(room);
            }
            
            UniResult result = new UniResult();
            result.setErrCode(200);
            result.setErrMsg("查询成功（模拟数据）");
            result.setData(mockRooms);
            
            logger.info("模拟房间数据生成完成: {}", result);
            return result;
            
        } catch (Exception e) {
            logger.error("生成模拟房间数据异常: {}", e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("生成模拟数据失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 调用人员宿舍绑定API
     *
     * @param url         API地址
     * @param requestData 请求数据
     * @return API调用结果
     * @throws IOException
     */
    private UniResult callDormitoryBindAPI(String url, String requestData) throws IOException {
        logger.info("向EGS平台批量绑定人员宿舍: url={}, data={}", url, requestData);
        
        try {
            UniResult uniResult = UniHttpUtil.sendHttpPostJson(url, requestData);

            if (null != uniResult && HttpStatus.SC_OK == uniResult.getErrCode()) {
                logger.info("批量绑定人员宿舍成功: result={}", uniResult);
                return uniResult;
            } else {
                logger.warn("批量绑定人员宿舍失败: result={}", uniResult);
                return uniResult; // 直接返回失败结果，让上层处理认证重试
            }
        } catch (Exception e) {
            logger.error("EGS平台连接失败: {}", e.getMessage());
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("连接EGS平台失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 创建模拟的人员宿舍绑定结果
     *
     * @param requestData 原始请求数据
     * @return 模拟的绑定结果
     */
    private UniResult createMockBindResult(String requestData) {
        try {
            JSONObject request = JSONObject.parseObject(requestData);
            List<String> codes = new ArrayList<>();
            com.alibaba.fastjson.JSONArray codeArray = request.getJSONArray("code");
            for (int i = 0; i < codeArray.size(); i++) {
                codes.add(codeArray.getString(i));
            }
            String roomId = request.getString("roomId");
            
            logger.info("生成模拟绑定结果: codes={}, roomId={}", codes, roomId);
            
            UniResult result = new UniResult();
            result.setErrCode(200);
            result.setErrMsg("批量绑定人员宿舍成功（模拟数据）");
            result.setData(new ArrayList<>()); // EGS平台通常返回空数组
            
            logger.info("模拟绑定结果生成完成: 成功绑定{}名人员到房间{}", codes.size(), roomId);
            return result;
            
        } catch (Exception e) {
            logger.error("生成模拟绑定结果异常: {}", e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("生成模拟绑定结果失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 查询中间库中所有有效的人员宿舍关联关系
     *
     * @return 人员宿舍关联关系列表
     */
    public List<Map<String, Object>> getAllPersonDormitoryRelations() {
        try {
            // 使用原生SQL查询避免字符集冲突问题
            String sql = "SELECT " +
                    "pdr.id, " +
                    "pdr.person_code, " +
                    "pdr.dormitory_code, " +
                    "pdr.bed_no, " +
                    "pdr.assign_date, " +
                    "pdr.sync_flag, " +
                    "di.building_code, " +
                    "di.floor, " +
                    "di.room_name, " +
                    "di.room_num " +
                    "FROM person_dormitory_relation pdr " +
                    "JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                    "WHERE pdr.status = 1 AND di.status = 1 " +
                    "ORDER BY di.building_code, di.floor, pdr.dormitory_code, pdr.person_code";

            Query query = entityManager.createNativeQuery(sql);
            List<Object[]> resultList = query.getResultList();
            
            List<Map<String, Object>> relations = new ArrayList<>();
            for (Object[] row : resultList) {
                Map<String, Object> relation = new HashMap<>();
                relation.put("id", row[0]);
                relation.put("person_code", row[1]);
                relation.put("dormitory_code", row[2]);
                relation.put("bed_no", row[3]);
                relation.put("assign_date", row[4]);
                relation.put("sync_flag", row[5]);
                relation.put("building_code", row[6]);
                relation.put("floor", row[7]);
                relation.put("room_name", row[8]);
                relation.put("room_num", row[9]);
                relations.add(relation);
            }
            
            logger.info("查询人员宿舍关联关系成功，共{}条记录", relations.size());
            return relations;
            
        } catch (Exception e) {
            logger.error("查询人员宿舍关联关系异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 分页查询人员宿舍关联关系
     *
     * @param page 页码（从0开始）
     * @param size 每页条数
     * @return 分页数据结果
     */
    public Map<String, Object> getPersonDormitoryRelationsPaged(int page, int size) {
        try {
            // 计算OFFSET
            int offset = page * size;
            
            // 查询总数的SQL
            String countSql = "SELECT COUNT(*) " +
                    "FROM person_dormitory_relation pdr " +
                    "JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                    "WHERE pdr.status = 1 AND di.status = 1";
            
            Query countQuery = entityManager.createNativeQuery(countSql);
            long totalElements = ((Number) countQuery.getSingleResult()).longValue();
            
            // 分页数据查询SQL
            String sql = "SELECT " +
                    "pdr.id, " +
                    "pdr.person_code, " +
                    "pdr.dormitory_code, " +
                    "pdr.bed_no, " +
                    "pdr.assign_date, " +
                    "pdr.sync_flag, " +
                    "di.building_code, " +
                    "di.floor, " +
                    "di.room_name, " +
                    "di.room_num " +
                    "FROM person_dormitory_relation pdr " +
                    "JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                    "WHERE pdr.status = 1 AND di.status = 1 " +
                    "ORDER BY di.building_code, di.floor, pdr.dormitory_code, pdr.person_code " +
                    "LIMIT :size OFFSET :offset";

            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("size", size);
            query.setParameter("offset", offset);
            List<Object[]> resultList = query.getResultList();
            
            List<Map<String, Object>> relations = new ArrayList<>();
            for (Object[] row : resultList) {
                Map<String, Object> relation = new HashMap<>();
                relation.put("id", row[0]);
                relation.put("person_code", row[1]);
                relation.put("dormitory_code", row[2]);
                relation.put("bed_no", row[3]);
                relation.put("assign_date", row[4]);
                relation.put("sync_flag", row[5]);
                relation.put("building_code", row[6]);
                relation.put("floor", row[7]);
                relation.put("room_name", row[8]);
                relation.put("room_num", row[9]);
                relations.add(relation);
            }
            
            // 计算分页信息
            int totalPages = (int) Math.ceil((double) totalElements / size);
            boolean hasNext = page < totalPages - 1;
            boolean hasPrevious = page > 0;
            boolean isFirst = page == 0;
            boolean isLast = page == totalPages - 1;
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("data", relations);
            result.put("page", page);
            result.put("size", size);
            result.put("total", totalElements);
            result.put("totalPages", totalPages);
            result.put("first", isFirst);
            result.put("last", isLast);
            result.put("hasNext", hasNext);
            result.put("hasPrevious", hasPrevious);
            
            logger.info("分页查询人员宿舍关联关系成功: page={}, size={}, total={}, totalPages={}", 
                    page, size, totalElements, totalPages);
            return result;
            
        } catch (Exception e) {
            logger.error("分页查询人员宿舍关联关系异常: page={}, size={}, error={}", page, size, e.getMessage(), e);
            throw new RuntimeException("分页查询人员宿舍关联关系失败: " + e.getMessage());
        }
    }

    /**
     * 批量绑定所有人员宿舍关联到EGS平台
     *
     * @return 批量绑定结果
     */
    public UniResult batchBindAllPersonDormitory() {
        try {
            // 查询所有人员宿舍关联关系
            List<Map<String, Object>> relations = getAllPersonDormitoryRelations();
            
            if (relations.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(200);
                result.setErrMsg("暂无人员宿舍关联数据需要绑定");
                return result;
            }

            logger.info("开始批量绑定人员宿舍关联，共{}条记录", relations.size());

            // 按建筑物和楼层分组处理
            Map<String, List<Map<String, Object>>> buildingFloorGroups = new HashMap<>();
            for (Map<String, Object> relation : relations) {
                String buildingCode = (String) relation.get("building_code");
                Integer floor = (Integer) relation.get("floor");
                String key = buildingCode + "_" + floor;
                
                buildingFloorGroups.computeIfAbsent(key, k -> new ArrayList<>()).add(relation);
            }

            int successCount = 0;
            int failCount = 0;
            List<String> errorMessages = new ArrayList<>();

            // 逐个处理每个建筑物楼层
            for (Map.Entry<String, List<Map<String, Object>>> entry : buildingFloorGroups.entrySet()) {
                String[] parts = entry.getKey().split("_");
                String buildingCode = parts[0];
                Integer floor = Integer.parseInt(parts[1]);
                List<Map<String, Object>> floorRelations = entry.getValue();

                logger.info("处理{}楼{}层，共{}条人员宿舍关联", buildingCode, floor, floorRelations.size());

                try {
                    // 获取该楼层的房间映射关系
                    Map<String, Long> roomMapping = buildRoomMapping(buildingCode, floor);
                    
                    if (roomMapping.isEmpty()) {
                        String errorMsg = String.format("无法获取%s楼%d层的房间信息", buildingCode, floor);
                        logger.warn(errorMsg);
                        errorMessages.add(errorMsg);
                        failCount += floorRelations.size();
                        continue;
                    }

                    // 按房间号分组人员
                    Map<String, List<String>> roomPersonGroups = new HashMap<>();
                    for (Map<String, Object> relation : floorRelations) {
                        String roomNum = relation.get("room_num").toString();
                        String personCode = (String) relation.get("person_code");
                        
                        roomPersonGroups.computeIfAbsent(roomNum, k -> new ArrayList<>()).add(personCode);
                    }

                    // 逐个房间绑定人员
                    for (Map.Entry<String, List<String>> roomEntry : roomPersonGroups.entrySet()) {
                        String roomNum = roomEntry.getKey();
                        List<String> personCodes = roomEntry.getValue();
                        
                        Long roomId = roomMapping.get(roomNum);
                        if (roomId == null) {
                            String errorMsg = String.format("房间号%s在EGS平台中未找到对应的roomId", roomNum);
                            logger.warn(errorMsg);
                            errorMessages.add(errorMsg);
                            failCount += personCodes.size();
                            continue;
                        }

                        logger.info("绑定房间{}的{}名人员到EGS平台，roomId={}", roomNum, personCodes.size(), roomId);
                        
                        UniResult bindResult = batchBindPersonDormitory(personCodes, roomId.toString());
                        if (bindResult.getErrCode() == 200) {
                            successCount += personCodes.size();
                            logger.info("房间{}人员绑定成功", roomNum);
                        } else {
                            failCount += personCodes.size();
                            String errorMsg = String.format("房间%s人员绑定失败: %s", roomNum, bindResult.getErrMsg());
                            logger.error(errorMsg);
                            errorMessages.add(errorMsg);
                        }
                    }

                } catch (Exception e) {
                    String errorMsg = String.format("处理%s楼%d层时发生异常: %s", buildingCode, floor, e.getMessage());
                    logger.error(errorMsg, e);
                    errorMessages.add(errorMsg);
                    failCount += floorRelations.size();
                }
            }

            // 构建返回结果
            UniResult result = new UniResult();
            if (failCount == 0) {
                result.setErrCode(200);
                result.setErrMsg(String.format("批量绑定完成，成功%d条", successCount));
            } else if (successCount > 0) {
                result.setErrCode(200);
                result.setErrMsg(String.format("批量绑定部分成功，成功%d条，失败%d条", successCount, failCount));
            } else {
                result.setErrCode(500);
                result.setErrMsg(String.format("批量绑定失败，失败%d条。错误信息: %s", failCount, String.join("; ", errorMessages)));
            }

            logger.info("批量绑定人员宿舍关联完成，成功{}条，失败{}条", successCount, failCount);
            return result;

        } catch (Exception e) {
            logger.error("批量绑定人员宿舍关联异常: {}", e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("批量绑定人员宿舍关联异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 建立房间号到EGS平台roomId的映射关系
     *
     * @param buildingCode 建筑物编码
     * @param floor        楼层
     * @return 房间号到roomId的映射
     */
    private Map<String, Long> buildRoomMapping(String buildingCode, Integer floor) {
        Map<String, Long> roomMapping = new HashMap<>();
        
        try {
            // 调用EGS接口获取房间信息
            UniResult roomResult = getDormitoryMembers(buildingCode, floor, -1);
            
            if (roomResult.getErrCode() != 200 || roomResult.getData() == null) {
                logger.warn("获取{}楼{}层房间信息失败: {}", buildingCode, floor, roomResult.getErrMsg());
                return roomMapping;
            }

            // 解析房间数据
            if (roomResult.getData() instanceof List) {
                List<Map<String, Object>> rooms = (List<Map<String, Object>>) roomResult.getData();
                
                for (Map<String, Object> room : rooms) {
                    String roomname = (String) room.get("roomname");
                    Object seqidObj = room.get("seqid");
                    
                    if (roomname != null && seqidObj != null) {
                        Long seqid = null;
                        if (seqidObj instanceof Integer) {
                            seqid = ((Integer) seqidObj).longValue();
                        } else if (seqidObj instanceof Long) {
                            seqid = (Long) seqidObj;
                        }
                        
                        if (seqid != null) {
                            roomMapping.put(roomname, seqid);
                            logger.debug("建立房间映射: roomname={} -> seqid={}", roomname, seqid);
                        }
                    }
                }
                
                logger.info("成功建立{}楼{}层房间映射，共{}个房间", buildingCode, floor, roomMapping.size());
            }
            
        } catch (Exception e) {
            logger.error("建立房间映射异常: buildingCode={}, floor={}, error={}", buildingCode, floor, e.getMessage(), e);
        }
        
        return roomMapping;
    }

    /**
     * 通过房间号获取EGS平台的roomId
     *
     * @param buildingCode 建筑物编码
     * @param floor        楼层
     * @param roomNum      房间号
     * @return EGS平台的roomId(seqid)，未找到返回null
     */
    public Long getRoomIdByRoomNum(String buildingCode, Integer floor, String roomNum) {
        try {
            Map<String, Long> roomMapping = buildRoomMapping(buildingCode, floor);
            return roomMapping.get(roomNum);
        } catch (Exception e) {
            logger.error("获取房间ID异常: buildingCode={}, floor={}, roomNum={}, error={}", 
                        buildingCode, floor, roomNum, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 同步楼栋为部门到EGS平台
     * @param buildingCodes 要同步的楼栋编码列表，如果为空则同步所有楼栋
     * @return 同步结果
     */
    public UniResult syncDepartments(List<String> buildingCodes) {
        UniResult result = new UniResult();
        
        try {
            logger.info("开始同步楼栋为部门到EGS平台，指定楼栋: {}", buildingCodes);
            
            // 获取楼栋列表
            List<Map<String, Object>> allBuildings = getBuildingList();
            
            if (allBuildings.isEmpty()) {
                result.setErrCode(400);
                result.setErrMsg("没有找到楼栋数据，无法同步部门");
                return result;
            }
            
            // 过滤指定的楼栋
            List<Map<String, Object>> buildings;
            if (buildingCodes != null && !buildingCodes.isEmpty()) {
                buildings = allBuildings.stream()
                    .filter(building -> buildingCodes.contains((String) building.get("buildingCode")))
                    .collect(java.util.stream.Collectors.toList());
                
                if (buildings.isEmpty()) {
                    result.setErrCode(400);
                    result.setErrMsg("指定的楼栋编码在系统中未找到");
                    return result;
                }
            } else {
                buildings = allBuildings;
            }
            
            logger.info("共找到{}个楼栋，准备同步为部门", buildings.size());
            
            // 登录EGS系统
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);
            logger.info("EGS系统登录验证完成");
            
            int successCount = 0;
            int failCount = 0;
            List<String> successBuildings = new ArrayList<>();
            List<String> failBuildings = new ArrayList<>();
            StringBuilder errorMessages = new StringBuilder();
            
            // 遍历楼栋，逐个创建部门
            for (Map<String, Object> building : buildings) {
                String buildingCode = (String) building.get("buildingCode");
                String buildingName = (String) building.get("buildingName");
                
                try {
                    // 检查必要的配置参数
                    if (fastGateConf == null) {
                        throw new RuntimeException("fastGateConf配置为空");
                    }
                    
                    String baseUrl = fastGateConf.getUrl();
                    if (baseUrl == null || baseUrl.trim().isEmpty()) {
                        throw new RuntimeException("EGS平台URL配置为空");
                    }
                    
                    if (buildingName == null || buildingName.trim().isEmpty()) {
                        throw new RuntimeException("楼栋名称为空");
                    }
                    
                    if (buildingCode == null || buildingCode.trim().isEmpty()) {
                        throw new RuntimeException("楼栋编码为空");
                    }
                    
                    // 构造新增部门请求
                    JSONObject departmentData = new JSONObject();
                    departmentData.put("ParentCode", "iccsid"); // 固定为iccsid
                    departmentData.put("Name", buildingName);   // 部门名称为楼栋名称
                    departmentData.put("Code", buildingCode);   // 部门编码为楼栋编码
                    
                    // 调用EGS新增部门API
                    String departmentUrl = baseUrl + "/department";
                    String requestBody = departmentData.toJSONString();
                    
                    logger.info("同步楼栋{}为部门 - URL: {}", buildingCode, departmentUrl);
                    logger.info("同步楼栋{}为部门 - 请求体: {}", buildingCode, requestBody);
                    
                    UniResult addResult = UniHttpUtil.sendHttpPostJson(departmentUrl, requestBody);
                    
                    logger.info("同步楼栋{}为部门 - 响应结果: {}", buildingCode, addResult);
                    
                    if (addResult != null && addResult.getErrCode() == 200) {
                        successCount++;
                        successBuildings.add(buildingName + "(" + buildingCode + ")");
                        logger.info("楼栋{}同步为部门成功", buildingCode);
                    } else {
                        failCount++;
                        String errorMsg = addResult != null ? addResult.getErrMsg() : "API返回结果为null";
                        failBuildings.add(buildingName + "(" + buildingCode + "): " + errorMsg);
                        errorMessages.append(buildingCode).append(":").append(errorMsg).append("; ");
                        logger.error("楼栋{}同步为部门失败: {}", buildingCode, errorMsg);
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    String errorMsg = "异常: " + e.getMessage();
                    failBuildings.add(buildingName + "(" + buildingCode + "): " + errorMsg);
                    errorMessages.append(buildingCode).append(":").append(errorMsg).append("; ");
                    logger.error("楼栋{}同步为部门异常: {}", buildingCode, e.getMessage(), e);
                }
            }
            
            // 构造返回结果
            result.setErrCode(200);
            String message = String.format("部门同步完成 - 成功: %d个, 失败: %d个, 总计: %d个楼栋",
                    successCount, failCount, buildings.size());
            
            if (failCount > 0) {
                message += " | 失败详情: " + errorMessages.toString();
            }
            
            result.setErrMsg(message);
            
            // 构造统计数据
            JSONObject data = new JSONObject();
            data.put("selectedBuildings", buildingCodes != null ? buildingCodes.size() : allBuildings.size());
            data.put("actualBuildings", buildings.size());
            data.put("success", successCount);
            data.put("fail", failCount);
            data.put("successBuildings", successBuildings);
            data.put("failBuildings", failBuildings);
            data.put("errorDetails", errorMessages.toString());
            result.setData(data);
            
            logger.info("部门同步完成: {}", message);
            return result;
            
        } catch (Exception e) {
            logger.error("部门同步异常: {}", e.getMessage(), e);
            UniResult errorResult = new UniResult();
            errorResult.setErrCode(500);
            errorResult.setErrMsg("部门同步异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 根据楼栋编码查询关联的所有人员
     * @param buildingCode 楼栋编码
     * @return 人员信息列表
     */
    public List<Map<String, Object>> getPersonsByBuildingCode(String buildingCode) {
        try {
            logger.info("查询楼栋{}关联的所有人员", buildingCode);
            
            String sql = "SELECT " +
                    "pi.seqid, " +
                    "pi.person_name, " +
                    "pi.person_code, " +
                    "pi.department_code, " +
                    "pi.gender, " +
                    "pi.telephone, " +
                    "pi.idcard, " +
                    "pi.status, " +
                    "pi.sync_flag, " +
                    "pi.person_type, " +
                    "pdr.dormitory_code, " +
                    "pdr.bed_no, " +
                    "pdr.assign_date, " +
                    "di.floor, " +
                    "di.room_name, " +
                    "di.room_num, " +
                    "di.building_code, " +
                    "di.building_name " +
                    "FROM person_info pi " +
                    "JOIN person_dormitory_relation pdr ON pi.person_code = pdr.person_code " +
                    "JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                    "WHERE di.building_code = :buildingCode " +
                    "AND pi.status = 1 AND pdr.status = 1 AND di.status = 1 " +
                    "ORDER BY di.floor, di.room_num, pi.person_name";

            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("buildingCode", buildingCode);
            List<Object[]> resultList = query.getResultList();
            
            List<Map<String, Object>> persons = new ArrayList<>();
            for (Object[] row : resultList) {
                Map<String, Object> person = new HashMap<>();
                person.put("seqid", row[0]);
                person.put("personName", row[1]);
                person.put("personCode", row[2]);
                person.put("departmentCode", row[3]);
                person.put("gender", row[4]);
                person.put("telephone", row[5]);
                person.put("idcard", row[6]);
                person.put("status", row[7]);
                person.put("syncFlag", row[8]);
                person.put("personType", row[9]);
                person.put("dormitoryCode", row[10]);
                person.put("bedNo", row[11]);
                person.put("assignDate", row[12]);
                person.put("floor", row[13]);
                person.put("roomName", row[14]);
                person.put("roomNum", row[15]);
                person.put("buildingCode", row[16]);
                person.put("buildingName", row[17]);
                persons.add(person);
            }
            
            logger.info("查询楼栋{}关联人员成功，共{}人", buildingCode, persons.size());
            return persons;
            
        } catch (Exception e) {
            logger.error("查询楼栋{}关联人员异常: {}", buildingCode, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据楼栋编码和楼层查询关联的人员
     * @param buildingCode 楼栋编码
     * @param floor 楼层
     * @return 人员信息列表
     */
    public List<Map<String, Object>> getPersonsByBuildingAndFloor(String buildingCode, Integer floor) {
        try {
            logger.info("查询楼栋{}第{}层关联的人员", buildingCode, floor);
            
            String sql = "SELECT " +
                    "pi.seqid, " +
                    "pi.person_name, " +
                    "pi.person_code, " +
                    "pi.department_code, " +
                    "pi.gender, " +
                    "pi.telephone, " +
                    "pi.idcard, " +
                    "pi.status, " +
                    "pi.sync_flag, " +
                    "pi.person_type, " +
                    "pdr.dormitory_code, " +
                    "pdr.bed_no, " +
                    "pdr.assign_date, " +
                    "di.floor, " +
                    "di.room_name, " +
                    "di.room_num, " +
                    "di.building_code, " +
                    "di.building_name " +
                    "FROM person_info pi " +
                    "JOIN person_dormitory_relation pdr ON pi.person_code = pdr.person_code " +
                    "JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                    "WHERE di.building_code = :buildingCode AND di.floor = :floor " +
                    "AND pi.status = 1 AND pdr.status = 1 AND di.status = 1 " +
                    "ORDER BY di.room_num, pi.person_name";

            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("buildingCode", buildingCode);
            query.setParameter("floor", floor);
            List<Object[]> resultList = query.getResultList();
            
            List<Map<String, Object>> persons = new ArrayList<>();
            for (Object[] row : resultList) {
                Map<String, Object> person = new HashMap<>();
                person.put("seqid", row[0]);
                person.put("personName", row[1]);
                person.put("personCode", row[2]);
                person.put("departmentCode", row[3]);
                person.put("gender", row[4]);
                person.put("telephone", row[5]);
                person.put("idcard", row[6]);
                person.put("status", row[7]);
                person.put("syncFlag", row[8]);
                person.put("personType", row[9]);
                person.put("dormitoryCode", row[10]);
                person.put("bedNo", row[11]);
                person.put("assignDate", row[12]);
                person.put("floor", row[13]);
                person.put("roomName", row[14]);
                person.put("roomNum", row[15]);
                person.put("buildingCode", row[16]);
                person.put("buildingName", row[17]);
                persons.add(person);
            }
            
            logger.info("查询楼栋{}第{}层关联人员成功，共{}人", buildingCode, floor, persons.size());
            return persons;
            
        } catch (Exception e) {
            logger.error("查询楼栋{}第{}层关联人员异常: {}", buildingCode, floor, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询指定楼栋的所有楼层列表
     * @param buildingCode 楼栋编码
     * @return 楼层信息列表
     */
    public List<Map<String, Object>> getBuildingFloorList(String buildingCode) {
        try {
            logger.info("查询楼栋{}的楼层列表", buildingCode);
            
            String sql = "SELECT " +
                    "di.floor, " +
                    "COUNT(DISTINCT di.dormitory_code) as room_count, " +
                    "COUNT(DISTINCT pdr.person_code) as person_count, " +
                    "di.building_name " +
                    "FROM dormitory_info di " +
                    "LEFT JOIN person_dormitory_relation pdr ON di.dormitory_code = pdr.dormitory_code AND pdr.status = 1 " +
                    "WHERE di.building_code = :buildingCode AND di.status = 1 " +
                    "GROUP BY di.floor, di.building_name " +
                    "ORDER BY di.floor";

            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("buildingCode", buildingCode);
            List<Object[]> resultList = query.getResultList();
            
            List<Map<String, Object>> floors = new ArrayList<>();
            for (Object[] row : resultList) {
                Map<String, Object> floor = new HashMap<>();
                floor.put("floor", row[0]);
                floor.put("roomCount", ((Number) row[1]).intValue());
                floor.put("personCount", ((Number) row[2]).intValue());
                floor.put("buildingName", row[3]);
                floor.put("buildingCode", buildingCode);
                floors.add(floor);
            }
            
            logger.info("查询楼栋{}楼层列表成功，共{}层", buildingCode, floors.size());
            return floors;
            
        } catch (Exception e) {
            logger.error("查询楼栋{}楼层列表异常: {}", buildingCode, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 统计指定楼栋的人员数量信息
     * @param buildingCode 楼栋编码
     * @return 统计信息
     */
    public Map<String, Object> getBuildingPersonsStatistics(String buildingCode) {
        try {
            logger.info("统计楼栋{}的人员信息", buildingCode);
            
            Map<String, Object> statistics = new HashMap<>();
            
            // 查询楼栋基本信息
            String buildingSql = "SELECT building_name, " +
                    "COUNT(DISTINCT dormitory_code) as room_count, " +
                    "MIN(floor) as min_floor, " +
                    "MAX(floor) as max_floor " +
                    "FROM dormitory_info " +
                    "WHERE building_code = :buildingCode AND status = 1 " +
                    "GROUP BY building_name";
            
            Query buildingQuery = entityManager.createNativeQuery(buildingSql);
            buildingQuery.setParameter("buildingCode", buildingCode);
            List<Object[]> buildingResult = buildingQuery.getResultList();
            
            if (!buildingResult.isEmpty()) {
                Object[] row = buildingResult.get(0);
                statistics.put("buildingCode", buildingCode);
                statistics.put("buildingName", row[0]);
                statistics.put("roomCount", ((Number) row[1]).intValue());
                statistics.put("minFloor", row[2]);
                statistics.put("maxFloor", row[3]);
            }
            
            // 查询人员统计信息
            String personSql = "SELECT " +
                    "COUNT(DISTINCT pi.person_code) as total_persons, " +
                    "COUNT(DISTINCT CASE WHEN pi.sync_flag = 1 THEN pi.person_code END) as synced_persons, " +
                    "COUNT(DISTINCT CASE WHEN pi.sync_flag = 0 OR pi.sync_flag IS NULL THEN pi.person_code END) as unsynced_persons, " +
                    "COUNT(DISTINCT CASE WHEN pi.gender = 1 THEN pi.person_code END) as male_persons, " +
                    "COUNT(DISTINCT CASE WHEN pi.gender = 2 THEN pi.person_code END) as female_persons " +
                    "FROM person_info pi " +
                    "JOIN person_dormitory_relation pdr ON pi.person_code = pdr.person_code " +
                    "JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                    "WHERE di.building_code = :buildingCode " +
                    "AND pi.status = 1 AND pdr.status = 1 AND di.status = 1";
            
            Query personQuery = entityManager.createNativeQuery(personSql);
            personQuery.setParameter("buildingCode", buildingCode);
            List<Object[]> personResult = personQuery.getResultList();
            
            if (!personResult.isEmpty()) {
                Object[] row = personResult.get(0);
                statistics.put("totalPersons", ((Number) row[0]).intValue());
                statistics.put("syncedPersons", ((Number) row[1]).intValue());
                statistics.put("unsyncedPersons", ((Number) row[2]).intValue());
                statistics.put("malePersons", ((Number) row[3]).intValue());
                statistics.put("femalePersons", ((Number) row[4]).intValue());
            }
            
            logger.info("统计楼栋{}人员信息成功: {}", buildingCode, statistics);
            return statistics;
            
        } catch (Exception e) {
            logger.error("统计楼栋{}人员信息异常: {}", buildingCode, e.getMessage(), e);
            Map<String, Object> errorStats = new HashMap<>();
            errorStats.put("buildingCode", buildingCode);
            errorStats.put("error", e.getMessage());
            return errorStats;
        }
    }

    /**
     * 同步楼栋关联的所有人员到EGS平台
     * @param buildingCode 楼栋编码
     * @param targetBuildingCode 目标楼栋编码（用于设置areaCode和departmentCode）
     * @return 同步结果
     */
    public UniResult syncBuildingPersons(String buildingCode, String targetBuildingCode) {
        try {
            logger.info("开始同步楼栋{}的所有人员，目标楼栋编码: {}", buildingCode, targetBuildingCode);
            
            // 获取楼栋关联的所有人员
            List<Map<String, Object>> persons = getPersonsByBuildingCode(buildingCode);
            
            if (persons.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(200);
                result.setErrMsg("楼栋" + buildingCode + "没有关联的人员，无需同步");
                return result;
            }

            // 登录EGS系统
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);
            logger.info("EGS系统登录验证完成");

            int successCount = 0;
            int failCount = 0;
            List<String> successPersons = new ArrayList<>();
            List<String> failPersons = new ArrayList<>();

            for (Map<String, Object> personData : persons) {
                try {
                    // 构建PersonInfo对象
                    PersonInfo personInfo = buildPersonInfoFromMap(personData, targetBuildingCode);
                    
                    // 调用PersonService进行同步
                    boolean syncSuccess = personService.addPerson(personInfo);
                    
                    if (syncSuccess) {
                        successCount++;
                        successPersons.add((String) personData.get("personName") + "(" + personData.get("personCode") + ")");
                        logger.info("楼栋人员同步成功: {}", personData.get("personName"));
                    } else {
                        failCount++;
                        failPersons.add((String) personData.get("personName") + "(" + personData.get("personCode") + ") - EGS平台返回错误");
                        logger.warn("楼栋人员同步失败: {} - addPerson返回false", personData.get("personName"));
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    failPersons.add((String) personData.get("personName") + "(" + personData.get("personCode") + ") - 异常: " + e.getMessage());
                    logger.error("楼栋人员同步异常: {}", personData.get("personName"), e);
                }
            }

            // 构造返回结果
            UniResult result = new UniResult();
            result.setErrCode(200);
            String message = String.format("楼栋%s人员同步完成 - 成功: %d人, 失败: %d人, 总计: %d人",
                    buildingCode, successCount, failCount, persons.size());
            result.setErrMsg(message);

            // 构造详细数据
            JSONObject data = new JSONObject();
            data.put("buildingCode", buildingCode);
            data.put("targetBuildingCode", targetBuildingCode);
            data.put("totalPersons", persons.size());
            data.put("successCount", successCount);
            data.put("failCount", failCount);
            data.put("successPersons", successPersons);
            data.put("failPersons", failPersons);
            result.setData(data);

            logger.info("楼栋{}人员同步完成: {}", buildingCode, message);
            return result;

        } catch (Exception e) {
            logger.error("楼栋{}人员同步异常: {}", buildingCode, e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("楼栋人员同步异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 同步楼栋指定楼层的人员到EGS平台
     * @param buildingCode 楼栋编码
     * @param floor 楼层
     * @param targetBuildingCode 目标楼栋编码（用于设置areaCode和departmentCode）
     * @return 同步结果
     */
    public UniResult syncBuildingFloorPersons(String buildingCode, Integer floor, String targetBuildingCode) {
        try {
            logger.info("开始同步楼栋{}第{}层的人员，目标楼栋编码: {}", buildingCode, floor, targetBuildingCode);
            
            // 获取楼栋楼层关联的人员
            List<Map<String, Object>> persons = getPersonsByBuildingAndFloor(buildingCode, floor);
            
            if (persons.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(200);
                result.setErrMsg("楼栋" + buildingCode + "第" + floor + "层没有关联的人员，无需同步");
                return result;
            }

            // 登录EGS系统
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);
            logger.info("EGS系统登录验证完成");

            int successCount = 0;
            int failCount = 0;
            List<String> successPersons = new ArrayList<>();
            List<String> failPersons = new ArrayList<>();

            for (Map<String, Object> personData : persons) {
                try {
                    // 构建PersonInfo对象
                    PersonInfo personInfo = buildPersonInfoFromMap(personData, targetBuildingCode);
                    
                    // 调用PersonService进行同步
                    boolean syncSuccess = personService.addPerson(personInfo);
                    
                    if (syncSuccess) {
                        successCount++;
                        successPersons.add((String) personData.get("personName") + "(" + personData.get("personCode") + ")");
                        logger.info("楼栋楼层人员同步成功: {}", personData.get("personName"));
                    } else {
                        failCount++;
                        failPersons.add((String) personData.get("personName") + "(" + personData.get("personCode") + ") - EGS平台返回错误");
                        logger.warn("楼栋楼层人员同步失败: {} - addPerson返回false", personData.get("personName"));
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    failPersons.add((String) personData.get("personName") + "(" + personData.get("personCode") + ") - 异常: " + e.getMessage());
                    logger.error("楼栋楼层人员同步异常: {}", personData.get("personName"), e);
                }
            }

            // 构造返回结果
            UniResult result = new UniResult();
            result.setErrCode(200);
            String message = String.format("楼栋%s第%d层人员同步完成 - 成功: %d人, 失败: %d人, 总计: %d人",
                    buildingCode, floor, successCount, failCount, persons.size());
            result.setErrMsg(message);

            // 构造详细数据
            JSONObject data = new JSONObject();
            data.put("buildingCode", buildingCode);
            data.put("floor", floor);
            data.put("targetBuildingCode", targetBuildingCode);
            data.put("totalPersons", persons.size());
            data.put("successCount", successCount);
            data.put("failCount", failCount);
            data.put("successPersons", successPersons);
            data.put("failPersons", failPersons);
            result.setData(data);

            logger.info("楼栋{}第{}层人员同步完成: {}", buildingCode, floor, message);
            return result;

        } catch (Exception e) {
            logger.error("楼栋{}第{}层人员同步异常: {}", buildingCode, floor, e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg("楼栋楼层人员同步异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 从Map数据构建PersonInfo对象，并设置areaCode和departmentCode为楼栋编码
     * @param personData 人员数据Map
     * @param buildingCode 楼栋编码
     * @return PersonInfo对象
     */
    private PersonInfo buildPersonInfoFromMap(Map<String, Object> personData, String buildingCode) {
        PersonInfo personInfo = new PersonInfo();
        
        // 设置基础字段
        personInfo.setSeqid((String) personData.get("seqid"));
        personInfo.setPersonName((String) personData.get("personName"));
        personInfo.setPersonCode((String) personData.get("personCode"));
        personInfo.setGender((Integer) personData.get("gender"));
        personInfo.setTelephone((String) personData.get("telephone"));
        personInfo.setIdcard((String) personData.get("idcard"));
        personInfo.setStatus((Integer) personData.get("status"));
        personInfo.setSyncFlag((Integer) personData.get("syncFlag"));
        personInfo.setPersonType((String) personData.get("personType"));
        
        // 关键设置：areaCode和departmentCode都设置为楼栋编码
        personInfo.setAreaCode(buildingCode);
        personInfo.setDepartmentCode(buildingCode);
        
        // 设置更新时间
        if (personInfo.getUpdateTime() == null) {
            personInfo.setUpdateTime(java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        logger.debug("构建PersonInfo完成: {} - areaCode={}, departmentCode={}", 
                   personInfo.getPersonName(), personInfo.getAreaCode(), personInfo.getDepartmentCode());
        
        return personInfo;
    }

    // === 按楼栋绑定功能相关方法 ===

    /**
     * 获取楼栋房间统计信息
     * @param buildingCode 楼栋编码
     * @return 楼栋房间统计信息
     */
    public Map<String, Object> getBuildingRoomStats(String buildingCode) {
        try {
            logger.info("查询楼栋{}的房间统计信息", buildingCode);
            
            Map<String, Object> stats = new HashMap<>();
            
            // 查询楼栋基本信息
            String buildingSql = "SELECT DISTINCT building_code, building_name FROM dormitory_info " +
                               "WHERE building_code = ? AND status = 1";
            Query buildingQuery = entityManager.createNativeQuery(buildingSql);
            buildingQuery.setParameter(1, buildingCode);
            List<Object[]> buildingResults = buildingQuery.getResultList();
            
            if (buildingResults.isEmpty()) {
                stats.put("buildingCode", buildingCode);
                stats.put("buildingName", "未知楼栋");
                stats.put("floors", new ArrayList<>());
                stats.put("totalFloors", 0);
                stats.put("totalRooms", 0);
                stats.put("totalPersons", 0);
                return stats;
            }
            
            Object[] buildingInfo = buildingResults.get(0);
            stats.put("buildingCode", buildingInfo[0]);
            stats.put("buildingName", buildingInfo[1]);
            
            // 查询楼层统计信息
            String floorSql = "SELECT floor, COUNT(*) as room_count, " +
                            "COALESCE((SELECT COUNT(*) FROM person_dormitory_relation pdr " +
                            "INNER JOIN dormitory_info di2 ON pdr.dormitory_code = di2.dormitory_code " +
                            "WHERE di2.building_code = ? AND di2.floor = di.floor AND pdr.status = 1), 0) as person_count " +
                            "FROM dormitory_info di " +
                            "WHERE building_code = ? AND status = 1 " +
                            "GROUP BY floor ORDER BY floor";
            
            Query floorQuery = entityManager.createNativeQuery(floorSql);
            floorQuery.setParameter(1, buildingCode);
            floorQuery.setParameter(2, buildingCode);
            List<Object[]> floorResults = floorQuery.getResultList();
            
            List<Map<String, Object>> floors = new ArrayList<>();
            int totalRooms = 0;
            int totalPersons = 0;
            
            for (Object[] floorData : floorResults) {
                Map<String, Object> floorInfo = new HashMap<>();
                floorInfo.put("floor", floorData[0]);
                floorInfo.put("roomCount", ((Number) floorData[1]).intValue());
                floorInfo.put("personCount", ((Number) floorData[2]).intValue());
                
                floors.add(floorInfo);
                totalRooms += ((Number) floorData[1]).intValue();
                totalPersons += ((Number) floorData[2]).intValue();
            }
            
            stats.put("floors", floors);
            stats.put("totalFloors", floors.size());
            stats.put("totalRooms", totalRooms);
            stats.put("totalPersons", totalPersons);
            
            logger.info("楼栋{}统计完成: {}层, {}间房, {}人", buildingCode, floors.size(), totalRooms, totalPersons);
            return stats;
            
        } catch (Exception e) {
            logger.error("查询楼栋{}房间统计信息异常: {}", buildingCode, e.getMessage(), e);
            Map<String, Object> errorStats = new HashMap<>();
            errorStats.put("buildingCode", buildingCode);
            errorStats.put("error", "查询异常: " + e.getMessage());
            return errorStats;
        }
    }

    /**
     * 获取楼层房间人员分配信息
     * @param buildingCode 楼栋编码
     * @param floor 楼层
     * @return 楼层房间人员分配信息
     */
    public List<Map<String, Object>> getFloorRoomPersons(String buildingCode, Integer floor) {
        try {
            logger.info("查询楼栋{}第{}层的房间人员分配信息", buildingCode, floor);
            
            // 查询楼层所有房间及其人员分配情况
            String sql = "SELECT " +
                        "di.dormitory_code, " +
                        "di.room_name, " +
                        "di.room_num, " +
                        "di.bed_count, " +
                        "COALESCE(CAST(persons.person_list AS text), '[]') as persons " +
                        "FROM dormitory_info di " +
                        "LEFT JOIN ( " +
                        "    SELECT " +
                        "        pdr.dormitory_code, " +
                        "        json_agg( " +
                        "            json_build_object( " +
                        "                'personCode', pdr.person_code, " +
                        "                'personName', pi.person_name, " +
                        "                'bedNo', pdr.bed_no " +
                        "            ) " +
                        "        ) as person_list " +
                        "    FROM person_dormitory_relation pdr " +
                        "    INNER JOIN person_info pi ON pdr.person_code = pi.person_code " +
                        "    WHERE pdr.status = 1 " +
                        "    GROUP BY pdr.dormitory_code " +
                        ") persons ON di.dormitory_code = persons.dormitory_code " +
                        "WHERE di.building_code = ? AND di.floor = ? AND di.status = 1 " +
                        "ORDER BY di.room_num";
            
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter(1, buildingCode);
            query.setParameter(2, floor);
            List<Object[]> results = query.getResultList();
            
            List<Map<String, Object>> roomList = new ArrayList<>();
            
            for (Object[] result : results) {
                Map<String, Object> roomInfo = new HashMap<>();
                roomInfo.put("dormitoryCode", result[0]);
                roomInfo.put("roomName", result[1]);
                roomInfo.put("roomNum", result[2]);
                roomInfo.put("bedCount", result[3]);
                
                // 解析人员JSON数据
                String personsJson = (String) result[4];
                try {
                    List<Map<String, Object>> persons = new ArrayList<>();
                    if (personsJson != null && !personsJson.equals("[]")) {
                        // 简单的JSON解析，实际项目中建议使用JSON库
                        JSONArray personArray = JSONArray.parseArray(personsJson);
                        for (int i = 0; i < personArray.size(); i++) {
                            JSONObject personObj = personArray.getJSONObject(i);
                            Map<String, Object> person = new HashMap<>();
                            person.put("personCode", personObj.getString("personCode"));
                            person.put("personName", personObj.getString("personName"));
                            person.put("bedNo", personObj.getString("bedNo"));
                            persons.add(person);
                        }
                    }
                    roomInfo.put("persons", persons);
                    roomInfo.put("currentPersonCount", persons.size());
                } catch (Exception e) {
                    logger.warn("解析房间{}人员JSON数据异常: {}", result[1], e.getMessage());
                    roomInfo.put("persons", new ArrayList<>());
                    roomInfo.put("currentPersonCount", 0);
                }
                
                roomList.add(roomInfo);
            }
            
            logger.info("楼栋{}第{}层房间查询完成，共{}间房", buildingCode, floor, roomList.size());
            return roomList;
            
        } catch (Exception e) {
            logger.error("查询楼栋{}第{}层房间人员分配信息异常: {}", buildingCode, floor, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 按楼层绑定人员到宿舍
     * @param buildingCode 楼栋编码
     * @param floor 楼层
     * @return 绑定结果
     */
    public UniResult bindBuildingFloorPersons(String buildingCode, Integer floor) {
        try {
            logger.info("开始按楼层绑定人员宿舍: 楼栋={}, 楼层={}", buildingCode, floor);
            
            // 查询该楼层的所有人员宿舍关联关系
            String sql = "SELECT " +
                        "pdr.person_code, " +
                        "pdr.dormitory_code, " +
                        "di.room_name, " +
                        "di.room_num " +
                        "FROM person_dormitory_relation pdr " +
                        "INNER JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                        "WHERE di.building_code = ? AND di.floor = ? AND pdr.status = 1 AND di.status = 1 " +
                        "ORDER BY di.room_num, pdr.person_code";
            
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter(1, buildingCode);
            query.setParameter(2, floor);
            List<Object[]> results = query.getResultList();
            
            if (results.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(200);
                result.setErrMsg(String.format("楼栋%s第%d层暂无人员宿舍关联数据需要绑定", buildingCode, floor));
                return result;
            }
            
            // 按房间分组人员
            Map<String, List<String>> roomPersonGroups = new HashMap<>();
            Map<String, String> roomNames = new HashMap<>();
            
            for (Object[] result : results) {
                String personCode = (String) result[0];
                String dormitoryCode = (String) result[1];
                String roomName = (String) result[2];
                
                roomPersonGroups.computeIfAbsent(roomName, k -> new ArrayList<>()).add(personCode);
                roomNames.put(roomName, dormitoryCode);
            }
            
            // 获取该楼层的房间映射关系
            Map<String, Long> roomMapping = buildRoomMapping(buildingCode, floor);
            
            if (roomMapping.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(500);
                result.setErrMsg(String.format("无法获取楼栋%s第%d层的房间信息", buildingCode, floor));
                return result;
            }
            
            int successCount = 0;
            int failCount = 0;
            List<String> errorMessages = new ArrayList<>();
            
            // 逐个房间进行绑定
            for (Map.Entry<String, List<String>> roomEntry : roomPersonGroups.entrySet()) {
                String roomName = roomEntry.getKey();
                List<String> personCodes = roomEntry.getValue();
                
                Long roomId = roomMapping.get(roomName);
                if (roomId == null) {
                    String errorMsg = String.format("房间%s在EGS平台中未找到对应的roomId", roomName);
                    logger.warn(errorMsg);
                    errorMessages.add(errorMsg);
                    failCount += personCodes.size();
                    continue;
                }
                
                logger.info("绑定房间{}的{}名人员到EGS平台，roomId={}", roomName, personCodes.size(), roomId);
                
                UniResult bindResult = batchBindPersonDormitory(personCodes, roomId.toString());
                if (bindResult.getErrCode() == 200) {
                    successCount += personCodes.size();
                    logger.info("房间{}人员绑定成功", roomName);
                } else {
                    failCount += personCodes.size();
                    String errorMsg = String.format("房间%s人员绑定失败: %s", roomName, bindResult.getErrMsg());
                    logger.error(errorMsg);
                    errorMessages.add(errorMsg);
                }
            }
            
            // 构建返回结果
            UniResult result = new UniResult();
            if (failCount == 0) {
                result.setErrCode(200);
                result.setErrMsg(String.format("楼栋%s第%d层人员绑定完成，成功%d人", buildingCode, floor, successCount));
            } else if (successCount > 0) {
                result.setErrCode(200);
                result.setErrMsg(String.format("楼栋%s第%d层人员绑定部分成功，成功%d人，失败%d人", buildingCode, floor, successCount, failCount));
            } else {
                result.setErrCode(500);
                result.setErrMsg(String.format("楼栋%s第%d层人员绑定失败，失败%d人。错误信息: %s", buildingCode, floor, failCount, String.join("; ", errorMessages)));
            }
            
            logger.info("楼栋{}第{}层人员绑定完成，成功{}人，失败{}人", buildingCode, floor, successCount, failCount);
            return result;
            
        } catch (Exception e) {
            logger.error("楼栋{}第{}层人员绑定异常: {}", buildingCode, floor, e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg(String.format("楼栋%s第%d层人员绑定异常: %s", buildingCode, floor, e.getMessage()));
            return result;
        }
    }

    /**
     * 按宿舍绑定人员
     * @param buildingCode 楼栋编码
     * @param floor 楼层
     * @param roomName 房间名称
     * @return 绑定结果
     */
    public UniResult bindRoomPersons(String buildingCode, Integer floor, String roomName) {
        try {
            logger.info("开始按宿舍绑定人员: 楼栋={}, 楼层={}, 房间={}", buildingCode, floor, roomName);
            
            // 查询该宿舍的人员关联关系
            String sql = "SELECT pdr.person_code " +
                        "FROM person_dormitory_relation pdr " +
                        "INNER JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                        "WHERE di.building_code = ? AND di.floor = ? AND di.room_name = ? " +
                        "AND pdr.status = 1 AND di.status = 1 " +
                        "ORDER BY pdr.person_code";
            
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter(1, buildingCode);
            query.setParameter(2, floor);
            query.setParameter(3, roomName);
            List<String> personCodes = query.getResultList();
            
            if (personCodes.isEmpty()) {
                UniResult result = new UniResult();
                result.setErrCode(200);
                result.setErrMsg(String.format("房间%s暂无人员宿舍关联数据需要绑定", roomName));
                return result;
            }
            
            // 获取房间对应的EGS平台roomId
            Long roomId = getRoomIdByRoomNum(buildingCode, floor, roomName);
            if (roomId == null) {
                UniResult result = new UniResult();
                result.setErrCode(500);
                result.setErrMsg(String.format("房间%s在EGS平台中未找到对应的roomId", roomName));
                return result;
            }
            
            logger.info("绑定房间{}的{}名人员到EGS平台，roomId={}", roomName, personCodes.size(), roomId);
            
            // 调用批量绑定接口
            UniResult bindResult = batchBindPersonDormitory(personCodes, roomId.toString());
            
            if (bindResult.getErrCode() == 200) {
                bindResult.setErrMsg(String.format("房间%s的%d名人员绑定成功", roomName, personCodes.size()));
                logger.info("房间{}人员绑定成功", roomName);
            } else {
                logger.error("房间{}人员绑定失败: {}", roomName, bindResult.getErrMsg());
            }
            
            return bindResult;
            
        } catch (Exception e) {
            logger.error("房间{}人员绑定异常: {}", roomName, e.getMessage(), e);
            UniResult result = new UniResult();
            result.setErrCode(500);
            result.setErrMsg(String.format("房间%s人员绑定异常: %s", roomName, e.getMessage()));
            return result;
        }
    }

    /**
     * 获取所有楼栋列表
     */
    public List<Map<String, Object>> getAllBuildings() {
        String sql = "SELECT " +
                "building_code, " +
                "building_name, " +
                "COUNT(DISTINCT dormitory_code) as dormitory_count, " +
                "COUNT(DISTINCT floor) as floor_count " +
                "FROM dormitory_info " +
                "WHERE status = 1 " +
                "GROUP BY building_code, building_name " +
                "ORDER BY " +
                "CASE " +
                "    WHEN building_name ~ '^[0-9]+' THEN " +
                "        CAST(SUBSTRING(building_name FROM '^([0-9]+)') AS INTEGER) " +
                "    ELSE 9999 " +
                "END, " +
                "LENGTH(building_name), " +
                "building_name";
        
        return jdbcTemplate.query(sql, (rs, rowNum) -> {
            Map<String, Object> building = new HashMap<>();
            building.put("buildingCode", rs.getString("building_code"));
            building.put("buildingName", rs.getString("building_name"));
            building.put("dormitoryCount", rs.getInt("dormitory_count"));
            building.put("floorCount", rs.getInt("floor_count"));
            return building;
        });
    }

    /**
     * 获取指定楼栋的楼层列表
     */
    public List<Map<String, Object>> getBuildingFloors(String buildingCode) {
        String sql = "SELECT DISTINCT " +
                "floor, " +
                "COALESCE(floor_name, CONCAT(floor, '楼')) as floor_name, " +
                "COUNT(dormitory_code) as dormitory_count " +
                "FROM dormitory_info " +
                "WHERE building_code = ? AND status = 1 " +
                "GROUP BY floor, floor_name " +
                "ORDER BY floor";
        
        return jdbcTemplate.query(sql, new Object[]{buildingCode}, (rs, rowNum) -> {
            Map<String, Object> floor = new HashMap<>();
            floor.put("floor", rs.getInt("floor"));
            floor.put("floorName", rs.getString("floor_name"));
            floor.put("dormitoryCount", rs.getInt("dormitory_count"));
            return floor;
        });
    }

    /**
     * 获取指定楼栋楼层的寝室列表（含人员信息）
     */
    public List<Map<String, Object>> getFloorDormitoriesWithPersons(String buildingCode, Integer floor) {
        String sql = "SELECT " +
                "d.dormitory_code, " +
                "d.room_name, " +
                "d.room_num, " +
                "d.gender_code, " +
                "d.bed_count, " +
                "d.building_name, " +
                "d.floor, " +
                "COUNT(pdr.person_code) as current_person_count " +
                "FROM dormitory_info d " +
                "LEFT JOIN person_dormitory_relation pdr ON d.dormitory_code = pdr.dormitory_code AND pdr.status = 1 " +
                "WHERE d.building_code = ? AND d.floor = ? AND d.status = 1 " +
                "GROUP BY d.dormitory_code, d.room_name, d.room_num, d.gender_code, d.bed_count, d.building_name, d.floor " +
                "ORDER BY d.room_num";
        
        List<Map<String, Object>> dormitories = jdbcTemplate.query(sql, new Object[]{buildingCode, floor}, (rs, rowNum) -> {
            Map<String, Object> dormitory = new HashMap<>();
            dormitory.put("dormitoryCode", rs.getString("dormitory_code"));
            dormitory.put("roomName", rs.getString("room_name"));
            dormitory.put("roomNum", rs.getInt("room_num"));
            dormitory.put("genderCode", rs.getString("gender_code"));
            dormitory.put("bedCount", rs.getInt("bed_count"));
            dormitory.put("buildingName", rs.getString("building_name"));
            dormitory.put("floor", rs.getInt("floor"));
            dormitory.put("currentPersonCount", rs.getInt("current_person_count"));
            return dormitory;
        });
        
        // 为每个寝室获取人员详细信息
        for (Map<String, Object> dormitory : dormitories) {
            String dormitoryCode = (String) dormitory.get("dormitoryCode");
            List<Map<String, Object>> persons = getDormitoryPersons(dormitoryCode);
            dormitory.put("persons", persons);
        }
        
        return dormitories;
    }

    /**
     * 获取指定寝室的人员列表
     */
    private List<Map<String, Object>> getDormitoryPersons(String dormitoryCode) {
        String sql = "SELECT " +
                "p.person_name, " +
                "p.person_code, " +
                "p.gender, " +
                "p.telephone, " +
                "pdr.bed_no, " +
                "pdr.assign_date " +
                "FROM person_dormitory_relation pdr " +
                "JOIN person_info p ON pdr.person_code = p.person_code " +
                "WHERE pdr.dormitory_code = ? AND pdr.status = 1 AND p.status = 1 " +
                "ORDER BY pdr.bed_no";
        
        return jdbcTemplate.query(sql, new Object[]{dormitoryCode}, (rs, rowNum) -> {
            Map<String, Object> person = new HashMap<>();
            person.put("personName", rs.getString("person_name"));
            person.put("personCode", rs.getString("person_code"));
            person.put("gender", rs.getInt("gender"));
            person.put("genderText", rs.getInt("gender") == 1 ? "男" : "女");
            person.put("telephone", rs.getString("telephone"));
            person.put("bedNo", rs.getString("bed_no"));
            person.put("assignDate", rs.getString("assign_date"));
            return person;
        });
    }

    /**
     * 获取寝室管理统计信息
     */
    public Map<String, Object> getDormitoryManagementStatistics() {
        String sql = "SELECT " +
                "COUNT(DISTINCT building_code) as total_buildings, " +
                "COUNT(DISTINCT CONCAT(building_code, '_', floor)) as total_floors, " +
                "COUNT(DISTINCT dormitory_code) as total_dormitories, " +
                "SUM(bed_count) as total_beds, " +
                "COUNT(DISTINCT pdr.person_code) as occupied_beds, " +
                "ROUND(" +
                "    CASE " +
                "        WHEN SUM(bed_count) > 0 " +
                "        THEN (COUNT(DISTINCT pdr.person_code) * 100.0 / SUM(bed_count)) " +
                "        ELSE 0 " +
                "    END, 2" +
                ") as occupancy_rate " +
                "FROM dormitory_info d " +
                "LEFT JOIN person_dormitory_relation pdr ON d.dormitory_code = pdr.dormitory_code AND pdr.status = 1 " +
                "WHERE d.status = 1";
        
        return jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalBuildings", rs.getInt("total_buildings"));
            statistics.put("totalFloors", rs.getInt("total_floors"));
            statistics.put("totalDormitories", rs.getInt("total_dormitories"));
            statistics.put("totalBeds", rs.getInt("total_beds"));
            statistics.put("occupiedBeds", rs.getInt("occupied_beds"));
            statistics.put("availableBeds", rs.getInt("total_beds") - rs.getInt("occupied_beds"));
            statistics.put("occupancyRate", rs.getDouble("occupancy_rate"));
            return statistics;
        });
    }

} 