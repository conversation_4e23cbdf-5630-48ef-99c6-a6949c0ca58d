package fastgatedemo.demo.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import fastgatedemo.demo.dto.AccessRecordDTO;
import fastgatedemo.demo.model.AccessRecordQueue;
import fastgatedemo.demo.model.fastgate.AccessControlRecord;
import fastgatedemo.demo.repository.AccessRecordQueueRepository;
import fastgatedemo.demo.repository.fastgate.AccessControlRecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @description 异步批量持久化服务
 * 负责将通行记录从内存队列异步批量写入数据库
 * 包含智能限流和压力控制机制
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class AsyncBatchPersistenceService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncBatchPersistenceService.class);

    @Autowired
    private AccessRecordQueueRepository queueRepository;

    @Autowired
    private AccessControlRecordRepository accessControlRecordRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 配置参数
    @Value("${async.persistence.batch.size:500}")
    private int batchSize = 500;

    @Value("${async.persistence.max.retries:3}")
    private int maxRetries = 3;

    @Value("${async.persistence.retry.delay.minutes:5}")
    private int retryDelayMinutes = 5;

    @Value("${async.persistence.max.tps:1000}")
    private int maxTPS = 1000;

    @Value("${async.persistence.enabled:true}")
    private boolean persistenceEnabled = true;

    // 性能监控计数器
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    private final AtomicInteger currentTPS = new AtomicInteger(0);
    private volatile LocalDateTime lastResetTime = LocalDateTime.now();

    /**
     * 添加记录到异步队列
     * @param accessRecord 通行记录DTO
     * @return 是否添加成功
     */
    @DS("master")
    public boolean addToQueue(AccessRecordDTO accessRecord) {
        try {
            AccessRecordQueue queueRecord = convertToQueueRecord(accessRecord);
            queueRepository.save(queueRecord);
            
            logger.debug("通行记录添加到异步队列成功: personCode={}, inOrOut={}", 
                        accessRecord.getPersonCode(), accessRecord.getInOrOut());
            return true;
            
        } catch (Exception e) {
            logger.error("通行记录添加到异步队列失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 定时处理待写入的记录
     * 每5秒执行一次
     */
    @Scheduled(fixedDelay = 5000, initialDelay = 10000)
    public void processPendingRecords() {
        if (!persistenceEnabled) {
            return;
        }

        try {
            // 检查TPS限制
            if (!checkTPSLimit()) {
                logger.debug("TPS达到限制，跳过本次处理");
                return;
            }

            // 获取待处理记录
            Pageable pageable = PageRequest.of(0, batchSize);
            List<AccessRecordQueue> pendingRecords = queueRepository.findPendingRecords(pageable);
            
            if (pendingRecords.isEmpty()) {
                return;
            }

            logger.info("开始处理{}条待写入记录", pendingRecords.size());
            long startTime = System.currentTimeMillis();

            // 按目标表分组批量处理
            Map<String, List<AccessRecordQueue>> tableGroups = groupByTargetTable(pendingRecords);
            
            int successCount = 0;
            int failCount = 0;

            for (Map.Entry<String, List<AccessRecordQueue>> entry : tableGroups.entrySet()) {
                String tableName = entry.getKey();
                List<AccessRecordQueue> records = entry.getValue();
                
                try {
                    batchInsertToFastGate(tableName, records);
                    markRecordsAsProcessed(records);
                    successCount += records.size();
                    
                } catch (Exception e) {
                    markRecordsAsFailed(records, e.getMessage());
                    failCount += records.size();
                    logger.error("批量写入表{}失败: {}", tableName, e.getMessage());
                }
            }

            // 更新性能计数器
            updatePerformanceCounters(successCount, failCount);
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("批量处理完成: 成功{}条, 失败{}条, 耗时{}ms", successCount, failCount, duration);

        } catch (Exception e) {
            logger.error("处理待写入记录异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理需要重试的失败记录
     * 每30分钟执行一次
     */
    @Scheduled(fixedDelay = 1800000, initialDelay = 300000) // 30分钟
    public void processRetryRecords() {
        if (!persistenceEnabled) {
            return;
        }

        try {
            LocalDateTime retryAfter = LocalDateTime.now().minusMinutes(retryDelayMinutes);
            Pageable pageable = PageRequest.of(0, batchSize / 2); // 重试时用更小的批次
            
            List<AccessRecordQueue> retryRecords = queueRepository.findRetryRecords(
                    maxRetries, retryAfter, pageable);
            
            if (retryRecords.isEmpty()) {
                return;
            }

            logger.info("开始重试{}条失败记录", retryRecords.size());
            
            // 重置状态并重新处理
            for (AccessRecordQueue record : retryRecords) {
                record.resetForRetry();
            }
            queueRepository.saveAll(retryRecords);
            
        } catch (Exception e) {
            logger.error("处理重试记录异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理已处理的历史记录
     * 每天凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void cleanupProcessedRecords() {
        try {
            // 删除7天前的已处理记录
            LocalDateTime weekAgo = LocalDateTime.now().minusDays(7);
            int deletedCount = queueRepository.deleteProcessedRecordsBefore(weekAgo);
            
            // 删除超过最大重试次数的失败记录
            int deletedFailedCount = queueRepository.deleteFailedRecordsOverMaxRetries(maxRetries);
            
            logger.info("清理历史记录完成: 已处理记录{}条, 失败记录{}条", deletedCount, deletedFailedCount);
            
        } catch (Exception e) {
            logger.error("清理历史记录异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 批量插入到FastGate数据库
     * @param tableName 目标表名
     * @param records 记录列表
     */
    @DS("fastgate")
    @Transactional
    private void batchInsertToFastGate(String tableName, List<AccessRecordQueue> records) {
        try {
            // 构建批量插入SQL
            String sql = buildBatchInsertSQL(tableName);
            
            List<Object[]> batchParams = records.stream()
                    .map(this::buildInsertParams)
                    .collect(Collectors.toList());
            
            jdbcTemplate.batchUpdate(sql, batchParams);
            
            logger.debug("批量插入表{}成功: {}条记录", tableName, records.size());
            
        } catch (Exception e) {
            logger.error("批量插入表{}失败: {}", tableName, e.getMessage());
            throw e;
        }
    }

    /**
     * 构建批量插入SQL
     * @param tableName 表名
     * @return SQL语句
     */
    private String buildBatchInsertSQL(String tableName) {
        return "INSERT INTO " + tableName + " " +
               "(person_code, person_name, device_code, device_name, place_code, place_name, " +
               "area_code, area_name, inorout, pass_time, record_date, record_time, " +
               "match_confidence, device_ip, temperature, update_time) " +
               "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
    }

    /**
     * 构建插入参数
     * @param record 队列记录
     * @return 参数数组
     */
    private Object[] buildInsertParams(AccessRecordQueue record) {
        return new Object[]{
                record.getPersonCode(),
                record.getPersonName(),
                record.getDeviceCode(),
                record.getDeviceName(),
                record.getPlaceCode(),
                record.getPlaceName(),
                record.getAreaCode(),
                record.getAreaName(),
                record.getInOrOut(),
                record.getPassTime(),
                record.getRecordDate(),
                record.getRecordTime(),
                record.getMatchConfidence(),
                record.getDeviceIp(),
                record.getTemperature()
        };
    }

    /**
     * 按目标表分组
     * @param records 记录列表
     * @return 分组结果
     */
    private Map<String, List<AccessRecordQueue>> groupByTargetTable(List<AccessRecordQueue> records) {
        return records.stream().collect(Collectors.groupingBy(record -> {
            // 根据记录日期确定目标表名
            String recordDate = record.getRecordDate();
            if (recordDate != null) {
                return "tbl_access_control_record_" + recordDate.replace("-", "_");
            } else {
                return "tbl_access_control_record";
            }
        }));
    }

    /**
     * 标记记录为已处理
     * @param records 记录列表
     */
    @DS("master")
    private void markRecordsAsProcessed(List<AccessRecordQueue> records) {
        try {
            List<Long> ids = records.stream()
                    .map(AccessRecordQueue::getId)
                    .collect(Collectors.toList());
            
            queueRepository.batchMarkAsProcessed(ids);
            
        } catch (Exception e) {
            logger.error("标记记录为已处理失败: {}", e.getMessage());
        }
    }

    /**
     * 标记记录为失败
     * @param records 记录列表
     * @param errorMessage 错误信息
     */
    @DS("master")
    private void markRecordsAsFailed(List<AccessRecordQueue> records, String errorMessage) {
        try {
            List<Long> ids = records.stream()
                    .map(AccessRecordQueue::getId)
                    .collect(Collectors.toList());
            
            String truncatedError = errorMessage.length() > 500 ? 
                    errorMessage.substring(0, 500) : errorMessage;
            
            queueRepository.batchMarkAsFailed(ids, truncatedError);
            
        } catch (Exception e) {
            logger.error("标记记录为失败失败: {}", e.getMessage());
        }
    }

    /**
     * 转换为队列记录
     * @param accessRecord 通行记录DTO
     * @return 队列记录
     */
    private AccessRecordQueue convertToQueueRecord(AccessRecordDTO accessRecord) {
        AccessRecordQueue queueRecord = new AccessRecordQueue();
        
        queueRecord.setPersonCode(accessRecord.getPersonCode());
        queueRecord.setPersonName(accessRecord.getPersonName());
        queueRecord.setDeviceCode(accessRecord.getDeviceCode());
        queueRecord.setDeviceName(accessRecord.getDeviceName());
        queueRecord.setPlaceCode(accessRecord.getPlaceCode());
        queueRecord.setPlaceName(accessRecord.getPlaceName());
        queueRecord.setAreaCode(accessRecord.getAreaCode());
        queueRecord.setAreaName(accessRecord.getAreaName());
        queueRecord.setInOrOut(accessRecord.getInOrOut());
        queueRecord.setPassTime(accessRecord.getPassTime());
        queueRecord.setRecordDate(accessRecord.getRecordDate());
        queueRecord.setRecordTime(accessRecord.getRecordTime());
        queueRecord.setMatchConfidence(accessRecord.getMatchConfidence());
        queueRecord.setDeviceIp(accessRecord.getDeviceIp());
        queueRecord.setTemperature(accessRecord.getTemperature());
        
        return queueRecord;
    }

    /**
     * 检查TPS限制
     * @return 是否允许处理
     */
    private boolean checkTPSLimit() {
        LocalDateTime now = LocalDateTime.now();
        
        // 每分钟重置TPS计数器
        if (now.isAfter(lastResetTime.plusMinutes(1))) {
            currentTPS.set(0);
            lastResetTime = now;
        }
        
        return currentTPS.get() < maxTPS;
    }

    /**
     * 更新性能计数器
     * @param successCount 成功数量
     * @param failCount 失败数量
     */
    private void updatePerformanceCounters(int successCount, int failCount) {
        totalProcessed.addAndGet(successCount);
        totalFailed.addAndGet(failCount);
        currentTPS.addAndGet(successCount);
    }

    /**
     * 获取队列统计信息
     * @return 统计信息
     */
    public Map<String, Object> getQueueStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            Object[] queueStats = queueRepository.getQueueStatistics();
            if (queueStats != null && queueStats.length >= 3) {
                stats.put("pendingCount", queueStats[0]);
                stats.put("failedCount", queueStats[1]);
                stats.put("todayProcessedCount", queueStats[2]);
            }
            
            stats.put("totalProcessed", totalProcessed.get());
            stats.put("totalFailed", totalFailed.get());
            stats.put("currentTPS", currentTPS.get());
            stats.put("maxTPS", maxTPS);
            stats.put("batchSize", batchSize);
            stats.put("enabled", persistenceEnabled);
            
        } catch (Exception e) {
            logger.error("获取队列统计信息失败: {}", e.getMessage());
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    /**
     * 设置持久化开关
     * @param enabled 是否启用
     */
    public void setPersistenceEnabled(boolean enabled) {
        this.persistenceEnabled = enabled;
        logger.info("异步持久化状态设置为: {}", enabled);
    }

    /**
     * 动态调整最大TPS
     * @param maxTPS 最大TPS
     */
    public void setMaxTPS(int maxTPS) {
        this.maxTPS = maxTPS;
        logger.info("最大TPS调整为: {}", maxTPS);
    }
}