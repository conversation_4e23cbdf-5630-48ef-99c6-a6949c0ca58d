package fastgatedemo.demo.service;

import fastgatedemo.demo.model.MiddlewareDormitoryInfo;
import fastgatedemo.demo.model.PersonDormitoryRelation;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.repository.MiddlewareDormitoryRepository;
import fastgatedemo.demo.repository.PersonDormitoryRelationRepository;
import fastgatedemo.demo.repository.PersonRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 人员宿舍同步预览服务
 */
@Service
public class PersonDormitorySyncPreviewService {

    private static final Logger logger = LoggerFactory.getLogger(PersonDormitorySyncPreviewService.class);

    @Autowired
    private PersonDormitoryRelationRepository relationRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private MiddlewareDormitoryRepository dormitoryRepository;

    /**
     * 获取同步预览数据
     * @return 预览数据
     */
    public Map<String, Object> getSyncPreviewData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始获取同步预览数据...");
            
            // 查询未同步的关联关系
            List<PersonDormitoryRelation> unsyncedRelations = relationRepository.findByStatusAndSyncFlag(1, 0);
            
            if (unsyncedRelations.isEmpty()) {
                result.put("success", true);
                result.put("totalCount", 0);
                result.put("message", "没有需要同步的人员宿舍关联关系");
                result.put("buildings", new ArrayList<>());
                return result;
            }
            
            logger.info("查询到 {} 条未同步的关联关系", unsyncedRelations.size());
            
            // 按楼栋分组处理
            Map<String, List<SyncPreviewItem>> buildingGroups = new LinkedHashMap<>();
            
            for (PersonDormitoryRelation relation : unsyncedRelations) {
                try {
                    // 查询人员信息
                    Optional<PersonInfo> personOpt = personRepository.findByPersonCode(relation.getPersonCode());
                    if (!personOpt.isPresent()) {
                        logger.warn("未找到人员信息: {}", relation.getPersonCode());
                        continue;
                    }
                    
                    // 查询宿舍信息
                    Optional<MiddlewareDormitoryInfo> dormitoryOpt = dormitoryRepository.findByDormitoryCode(relation.getDormitoryCode());
                    if (!dormitoryOpt.isPresent()) {
                        logger.warn("未找到宿舍信息: {}", relation.getDormitoryCode());
                        continue;
                    }
                    
                    PersonInfo person = personOpt.get();
                    MiddlewareDormitoryInfo dormitory = dormitoryOpt.get();
                    
                    // 创建预览项
                    SyncPreviewItem item = new SyncPreviewItem();
                    item.setPersonCode(person.getPersonCode());
                    item.setPersonName(person.getPersonName());
                    item.setPersonType(getPersonTypeText(person.getPersonType()));
                    item.setDormitoryCode(dormitory.getDormitoryCode());
                    item.setBuildingCode(dormitory.getBuildingCode());
                    item.setBuildingName(dormitory.getBuildingName());
                    item.setFloor(dormitory.getFloor() != null ? dormitory.getFloor() : 0);
                    item.setRoomNum(dormitory.getRoomNum() != null ? dormitory.getRoomNum() : 0);
                    item.setRoomName(dormitory.getRoomName());
                    item.setBedNo(relation.getBedNo());
                    item.setAssignDate(relation.getAssignDate());
                    
                    // 按楼栋分组
                    String buildingKey = dormitory.getBuildingCode() + "_" + dormitory.getBuildingName();
                    buildingGroups.computeIfAbsent(buildingKey, k -> new ArrayList<>()).add(item);
                    
                } catch (Exception e) {
                    logger.error("处理关联关系异常: personCode={}, dormitoryCode={}", 
                               relation.getPersonCode(), relation.getDormitoryCode(), e);
                }
            }
            
            // 转换为前端需要的格式
            List<Map<String, Object>> buildings = new ArrayList<>();
            
            for (Map.Entry<String, List<SyncPreviewItem>> entry : buildingGroups.entrySet()) {
                String[] buildingInfo = entry.getKey().split("_", 2);
                String buildingCode = buildingInfo[0];
                String buildingName = buildingInfo.length > 1 ? buildingInfo[1] : "未知楼栋";
                
                List<SyncPreviewItem> buildingItems = entry.getValue();
                
                // 按楼层分组
                Map<Integer, List<SyncPreviewItem>> floorGroups = buildingItems.stream()
                    .collect(Collectors.groupingBy(SyncPreviewItem::getFloor, TreeMap::new, Collectors.toList()));
                
                List<Map<String, Object>> floors = new ArrayList<>();
                
                for (Map.Entry<Integer, List<SyncPreviewItem>> floorEntry : floorGroups.entrySet()) {
                    Integer floor = floorEntry.getKey();
                    List<SyncPreviewItem> floorItems = floorEntry.getValue();
                    
                    // 按房间分组
                    Map<Integer, List<SyncPreviewItem>> roomGroups = floorItems.stream()
                        .collect(Collectors.groupingBy(SyncPreviewItem::getRoomNum, TreeMap::new, Collectors.toList()));
                    
                    List<Map<String, Object>> rooms = new ArrayList<>();
                    
                    for (Map.Entry<Integer, List<SyncPreviewItem>> roomEntry : roomGroups.entrySet()) {
                        Integer roomNum = roomEntry.getKey();
                        List<SyncPreviewItem> roomItems = roomEntry.getValue();
                        
                        Map<String, Object> room = new HashMap<>();
                        room.put("roomNum", roomNum);
                        room.put("roomName", roomItems.get(0).getRoomName());
                        room.put("personCount", roomItems.size());
                        room.put("persons", roomItems);
                        
                        rooms.add(room);
                    }
                    
                    Map<String, Object> floorData = new HashMap<>();
                    floorData.put("floor", floor);
                    floorData.put("floorName", floor == 0 ? "未知楼层" : floor + "楼");
                    floorData.put("roomCount", rooms.size());
                    floorData.put("personCount", floorItems.size());
                    floorData.put("rooms", rooms);
                    
                    floors.add(floorData);
                }
                
                Map<String, Object> building = new HashMap<>();
                building.put("buildingCode", buildingCode);
                building.put("buildingName", buildingName);
                building.put("floorCount", floors.size());
                building.put("personCount", buildingItems.size());
                building.put("floors", floors);
                
                buildings.add(building);
            }
            
            result.put("success", true);
            result.put("totalCount", unsyncedRelations.size());
            result.put("buildingCount", buildings.size());
            result.put("buildings", buildings);
            result.put("message", String.format("共找到 %d 个楼栋，%d 个人员需要同步", 
                                               buildings.size(), unsyncedRelations.size()));
            
            logger.info("同步预览数据获取完成：楼栋数={}, 人员数={}", buildings.size(), unsyncedRelations.size());
            
        } catch (Exception e) {
            logger.error("获取同步预览数据异常", e);
            result.put("success", false);
            result.put("message", "获取预览数据失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取人员类型文本
     * @param personType 人员类型代码
     * @return 人员类型文本
     */
    private String getPersonTypeText(String personType) {
        if (personType == null) return "未知";
        
        switch (personType) {
            case "1": return "学生";
            case "2": return "教师";
            case "3": return "职工";
            default: return "其他";
        }
    }
    
    /**
     * 同步预览项内部类
     */
    public static class SyncPreviewItem {
        private String personCode;
        private String personName;
        private String personType;
        private String dormitoryCode;
        private String buildingCode;
        private String buildingName;
        private Integer floor;
        private Integer roomNum;
        private String roomName;
        private String bedNo;
        private String assignDate;
        
        // Getter和Setter方法
        public String getPersonCode() { return personCode; }
        public void setPersonCode(String personCode) { this.personCode = personCode; }
        
        public String getPersonName() { return personName; }
        public void setPersonName(String personName) { this.personName = personName; }
        
        public String getPersonType() { return personType; }
        public void setPersonType(String personType) { this.personType = personType; }
        
        public String getDormitoryCode() { return dormitoryCode; }
        public void setDormitoryCode(String dormitoryCode) { this.dormitoryCode = dormitoryCode; }
        
        public String getBuildingCode() { return buildingCode; }
        public void setBuildingCode(String buildingCode) { this.buildingCode = buildingCode; }
        
        public String getBuildingName() { return buildingName; }
        public void setBuildingName(String buildingName) { this.buildingName = buildingName; }
        
        public Integer getFloor() { return floor; }
        public void setFloor(Integer floor) { this.floor = floor; }
        
        public Integer getRoomNum() { return roomNum; }
        public void setRoomNum(Integer roomNum) { this.roomNum = roomNum; }
        
        public String getRoomName() { return roomName; }
        public void setRoomName(String roomName) { this.roomName = roomName; }
        
        public String getBedNo() { return bedNo; }
        public void setBedNo(String bedNo) { this.bedNo = bedNo; }
        
        public String getAssignDate() { return assignDate; }
        public void setAssignDate(String assignDate) { this.assignDate = assignDate; }
    }
}
