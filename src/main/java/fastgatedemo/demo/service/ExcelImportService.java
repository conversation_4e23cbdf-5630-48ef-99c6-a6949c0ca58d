package fastgatedemo.demo.service;

import fastgatedemo.demo.dto.PersonImportResultDTO;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * @description Excel文件导入处理服务
 * 负责Excel文件的验证、解析和数据清洗
 */
@Service
public class ExcelImportService {

    private static final Logger logger = LoggerFactory.getLogger(ExcelImportService.class);

    // 支持的文件格式
    private static final String EXCEL_XLS = ".xls";
    private static final String EXCEL_XLSX = ".xlsx";
    
    // 文件大小限制：5MB
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;
    
    // 最大行数限制
    private static final int MAX_ROWS = 1000;

    // 必需的列名（支持中文）
    private static final Set<String> REQUIRED_COLUMNS = new HashSet<>(Arrays.asList(
        "姓名", "工号", "部门编码", "name", "jobnumber", "departmentcode"
    ));

    /**
     * 验证上传的Excel文件
     */
    public Map<String, Object> validateFile(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择要上传的文件");
                return result;
            }

            // 检查文件大小
            if (file.getSize() > MAX_FILE_SIZE) {
                result.put("success", false);
                result.put("message", "文件大小不能超过5MB");
                return result;
            }

            // 检查文件格式
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(EXCEL_XLS) && 
                                   !fileName.toLowerCase().endsWith(EXCEL_XLSX))) {
                result.put("success", false);
                result.put("message", "请上传Excel文件（.xls或.xlsx格式）");
                return result;
            }

            result.put("success", true);
            result.put("message", "文件验证通过");
            result.put("fileName", fileName);
            result.put("fileSize", file.getSize());

        } catch (Exception e) {
            logger.error("文件验证异常", e);
            result.put("success", false);
            result.put("message", "文件验证失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 解析Excel文件并返回人员数据
     */
    public Map<String, Object> parseExcelFile(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        List<PersonImportResultDTO> personList = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            
            // 创建工作簿对象
            Workbook workbook = createWorkbook(file, inputStream);
            
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            
            // 检查是否有数据
            if (sheet.getPhysicalNumberOfRows() < 2) {
                result.put("success", false);
                result.put("message", "Excel文件中没有数据行");
                return result;
            }

            // 检查行数限制
            if (sheet.getPhysicalNumberOfRows() > MAX_ROWS) {
                result.put("success", false);
                result.put("message", "数据行数不能超过" + MAX_ROWS + "行");
                return result;
            }

            // 解析表头
            Row headerRow = sheet.getRow(0);
            Map<String, Integer> columnIndexMap = parseHeader(headerRow);
            
            // 验证必需列是否存在
            String validationError = validateRequiredColumns(columnIndexMap);
            if (validationError != null) {
                result.put("success", false);
                result.put("message", validationError);
                return result;
            }

            // 解析数据行
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }

                // 检查是否为空行
                if (isEmptyRow(row)) {
                    continue;
                }

                PersonImportResultDTO person = parseDataRow(row, columnIndexMap, rowIndex + 1);
                if (person != null) {
                    personList.add(person);
                }
            }

            workbook.close();

            result.put("success", true);
            result.put("message", "Excel解析成功");
            result.put("data", personList);
            result.put("totalCount", personList.size());
            result.put("columnMapping", columnIndexMap);

        } catch (Exception e) {
            logger.error("Excel文件解析异常", e);
            result.put("success", false);
            result.put("message", "Excel解析失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 创建工作簿对象
     */
    private Workbook createWorkbook(MultipartFile file, InputStream inputStream) throws IOException {
        String fileName = file.getOriginalFilename();
        if (fileName != null && fileName.toLowerCase().endsWith(EXCEL_XLSX)) {
            return new XSSFWorkbook(inputStream);
        } else {
            return new HSSFWorkbook(inputStream);
        }
    }

    /**
     * 解析表头，获取列索引映射
     */
    private Map<String, Integer> parseHeader(Row headerRow) {
        Map<String, Integer> columnIndexMap = new HashMap<>();
        
        if (headerRow != null) {
            for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
                Cell cell = headerRow.getCell(cellIndex);
                if (cell != null) {
                    String cellValue = getCellValueAsString(cell).trim().toLowerCase();
                    
                    // 映射中文列名
                    if ("姓名".equals(cellValue) || "name".equals(cellValue)) {
                        columnIndexMap.put("name", cellIndex);
                    } else if ("工号".equals(cellValue) || "jobnumber".equals(cellValue)) {
                        columnIndexMap.put("jobNumber", cellIndex);
                    } else if ("部门编码".equals(cellValue) || "departmentcode".equals(cellValue)) {
                        columnIndexMap.put("departmentCode", cellIndex);
                    }
                }
            }
        }
        
        return columnIndexMap;
    }

    /**
     * 验证必需列是否存在
     */
    private String validateRequiredColumns(Map<String, Integer> columnIndexMap) {
        if (!columnIndexMap.containsKey("name")) {
            return "缺少'姓名'列，请检查Excel表头";
        }
        if (!columnIndexMap.containsKey("jobNumber")) {
            return "缺少'工号'列，请检查Excel表头";
        }
        if (!columnIndexMap.containsKey("departmentCode")) {
            return "缺少'部门编码'列，请检查Excel表头";
        }
        return null;
    }

    /**
     * 解析数据行
     */
    private PersonImportResultDTO parseDataRow(Row row, Map<String, Integer> columnIndexMap, int rowNumber) {
        try {
            String name = getCellValueAsString(row.getCell(columnIndexMap.get("name"))).trim();
            String jobNumber = getCellValueAsString(row.getCell(columnIndexMap.get("jobNumber"))).trim();
            String departmentCode = getCellValueAsString(row.getCell(columnIndexMap.get("departmentCode"))).trim();

            // 验证必填字段
            if (StringUtils.isEmpty(name) || StringUtils.isEmpty(jobNumber)) {
                logger.warn("第{}行数据不完整：姓名={}, 工号={}", rowNumber, name, jobNumber);
                return null;
            }

            return new PersonImportResultDTO(name, jobNumber, departmentCode, rowNumber);

        } catch (Exception e) {
            logger.error("解析第{}行数据异常", rowNumber, e);
            return null;
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getStringCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字格式，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case Cell.CELL_TYPE_BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case Cell.CELL_TYPE_FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 检查是否为空行
     */
    private boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }
        
        for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            if (cell != null && !StringUtils.isEmpty(getCellValueAsString(cell).trim())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取Excel数据预览（前几行）
     */
    public List<PersonImportResultDTO> getPreviewData(List<PersonImportResultDTO> allData, int previewCount) {
        if (allData == null || allData.isEmpty()) {
            return new ArrayList<>();
        }
        
        int endIndex = Math.min(previewCount, allData.size());
        return allData.subList(0, endIndex);
    }
} 