package fastgatedemo.demo.service;

import fastgatedemo.demo.model.MiddlewareFacePhoto;
import fastgatedemo.demo.model.MiddlewarePersonInfo;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.repository.MiddlewareFacePhotoRepository;
import fastgatedemo.demo.repository.MiddlewarePersonRepository;
import fastgatedemo.demo.repository.PersonRepository;
import fastgatedemo.demo.service.PersonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import fastgatedemo.demo.config.FastGateConf;
import fastgatedemo.demo.constant.BaseConstant;
import fastgatedemo.demo.service.PersonDataConverter;
import fastgatedemo.demo.util.UniHttpUtil;

/**
 * @description 中间库业务服务
 * 提供中间库数据的查询、转换和同步功能
 */
@Service
@Transactional
public class MiddlewareService {

    private static final Logger logger = LoggerFactory.getLogger(MiddlewareService.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // 并发配置
    private static final int THREAD_POOL_SIZE = 10; // 线程池大小
    private static final int TASK_TIMEOUT_SECONDS = 30; // 单个任务超时时间

    @Autowired
    private MiddlewarePersonRepository middlewarePersonRepository;

    @Autowired
    private MiddlewareFacePhotoRepository middlewareFacePhotoRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private PersonService personService;

    @Autowired
    private FastGateConf fastGateConf;

    @Autowired
    private PersonDataConverter personDataConverter;

    /**
     * 查询所有启用状态的中间库人员信息
     */
    public List<MiddlewarePersonInfo> getAllActivePersons() {
        return middlewarePersonRepository.findActivePersons();
    }

    /**
     * 根据人员类型查询人员信息
     */
    public List<MiddlewarePersonInfo> getPersonsByType(Integer perType) {
        return middlewarePersonRepository.findByPerType(perType);
    }

    /**
     * 根据部门编码查询人员信息
     */
    public List<MiddlewarePersonInfo> getPersonsByDepartment(String departmentCode) {
        return middlewarePersonRepository.findByDepartmentCode(departmentCode);
    }

    /**
     * 根据姓名模糊查询人员信息
     */
    public List<MiddlewarePersonInfo> searchPersonsByName(String name) {
        return middlewarePersonRepository.findByNameContaining(name);
    }

    /**
     * 获取人员统计信息
     */
    public Map<String, Long> getPersonStatistics() {
        Long totalPersons = middlewarePersonRepository.countActivePersons();
        Long totalStudents = middlewarePersonRepository.countActiveStudents();
        Long totalStaff = middlewarePersonRepository.countActiveStaff();
        Long totalPhotos = middlewareFacePhotoRepository.countValidPhotos();

        Map<String, Long> statistics = new HashMap<String, Long>();
        statistics.put("totalPersons", totalPersons != null ? totalPersons : 0L);
        statistics.put("totalStudents", totalStudents != null ? totalStudents : 0L);
        statistics.put("totalStaff", totalStaff != null ? totalStaff : 0L);
        statistics.put("totalPhotos", totalPhotos != null ? totalPhotos : 0L);
        statistics.put("personsWithoutPhoto", Math.max(0, (totalPersons != null ? totalPersons : 0L) - (totalPhotos != null ? totalPhotos : 0L)));
        
        return statistics;
    }

    /**
     * 转换单个人员信息到EGS格式
     */
    public PersonInfo convertSinglePerson(String perCode) {
        Optional<MiddlewarePersonInfo> personOpt = middlewarePersonRepository.findByPerCode(perCode);
        if (!personOpt.isPresent()) {
            throw new RuntimeException("找不到人员编码为 " + perCode + " 的人员信息");
        }

        MiddlewarePersonInfo middlewarePersonInfo = personOpt.get();
        Optional<MiddlewareFacePhoto> photoOpt = middlewareFacePhotoRepository.findByPerCode(perCode);
        
        // 使用PersonDataConverter统一处理转换逻辑
        return personDataConverter.convertToPersonInfo(middlewarePersonInfo, photoOpt.orElse(null));
    }

    /**
     * 检查数据库连接状态
     */
    public boolean checkDatabaseConnection() {
        try {
            Long count = middlewarePersonRepository.count();
            logger.info("中间库连接正常，共有{}条人员记录", count);
            return true;
        } catch (Exception e) {
            logger.error("中间库连接失败", e);
            return false;
        }
    }

    /**
     * 分页查询启用状态的中间库人员信息
     */
    public Page<MiddlewarePersonInfo> getAllActivePersonsPaged(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updateTime"));
        return middlewarePersonRepository.findActivePersons(pageable);
    }

    /**
     * 根据条件分页查询人员信息
     */
    public Page<MiddlewarePersonInfo> getPersonsPagedByCondition(Integer perType, String department, String name, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updateTime"));
        
        if (perType != null) {
            return middlewarePersonRepository.findByPerType(perType, pageable);
        } else if (department != null && !department.trim().isEmpty()) {
            return middlewarePersonRepository.findByDepartmentCode(department.trim(), pageable);
        } else if (name != null && !name.trim().isEmpty()) {
            return middlewarePersonRepository.findByNameContaining(name.trim(), pageable);
        } else {
            return middlewarePersonRepository.findActivePersons(pageable);
        }
    }

    /**
     * 批量同步中间库数据到EGS系统
     */
    @Transactional
    public Map<String, Object> batchSyncToEGS(List<String> perCodes) {
        return batchSyncToEGS(perCodes, THREAD_POOL_SIZE);
    }

    /**
     * 批量同步中间库数据到EGS系统（可指定并发线程数）
     * @param perCodes 人员编码列表，为空时同步所有启用人员
     * @param threadPoolSize 并发线程池大小
     */
    @Transactional
    public Map<String, Object> batchSyncToEGS(List<String> perCodes, int threadPoolSize) {
        Map<String, Object> result = new HashMap<String, Object>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        List<String> failedItems = Collections.synchronizedList(new ArrayList<String>());
        
        // 验证并调整线程池大小
        int actualThreadPoolSize = Math.max(1, Math.min(threadPoolSize, 20)); // 限制在1-20之间
        
        try {
            // 首先进行登录验证，获取Cookie（参考PersonDeal.run）
            String loginUrl = fastGateConf.getUrl() + BaseConstant.LOGIN_URL;
            String user = fastGateConf.getUser();
            String passwd = fastGateConf.getPasswd();
            UniHttpUtil.getCookieStore(loginUrl, user, passwd);
            logger.info("EGS系统登录验证完成");

            // 如果没有指定人员编码，则同步所有启用的人员
            List<MiddlewarePersonInfo> personsToSync;
            if (perCodes == null || perCodes.isEmpty()) {
                personsToSync = middlewarePersonRepository.findActivePersons();
                logger.info("开始批量并发同步所有启用人员，共{}条记录，使用{}个线程", 
                           personsToSync.size(), actualThreadPoolSize);
            } else {
                personsToSync = new ArrayList<MiddlewarePersonInfo>();
                for (String perCode : perCodes) {
                    Optional<MiddlewarePersonInfo> personOpt = middlewarePersonRepository.findByPerCode(perCode);
                    if (personOpt.isPresent()) {
                        personsToSync.add(personOpt.get());
                    }
                }
                logger.info("开始批量并发同步指定人员，共{}条记录，使用{}个线程", 
                           personsToSync.size(), actualThreadPoolSize);
            }

            // 使用线程池进行批量并发同步
            ExecutorService executorService = Executors.newFixedThreadPool(actualThreadPoolSize);
            List<Future<Boolean>> futures = new ArrayList<Future<Boolean>>();
            
            for (MiddlewarePersonInfo middlewarePerson : personsToSync) {
                Future<Boolean> future = executorService.submit(() -> {
                    try {
                        // 获取关联的人脸照片
                        Optional<MiddlewareFacePhoto> facePhotoOpt = 
                            middlewareFacePhotoRepository.findByPerCode(middlewarePerson.getPerCode());
                        
                        // 转换为EGS PersonInfo格式
                        PersonInfo personInfo = personDataConverter.convertToPersonInfo(
                            middlewarePerson, 
                            facePhotoOpt.orElse(null)
                        );
                        
                        // 验证转换后的数据
                        if (!personDataConverter.validatePersonInfo(personInfo)) {
                            logger.warn("人员数据验证失败，跳过同步: {}", middlewarePerson.getAccName());
                            failCount.incrementAndGet();
                            failedItems.add(middlewarePerson.getPerCode() + " (数据验证失败)");
                            return false;
                        }

                        // 调用PersonService进行同步（参考PersonDeal.run）
                        boolean syncSuccess = personService.addPerson(personInfo);
                        
                        if (syncSuccess) {
                            successCount.incrementAndGet();
                            logger.info("同步人员成功: {} ({})", middlewarePerson.getAccName(), middlewarePerson.getPerCode());
                            return true;
                        } else {
                            failCount.incrementAndGet();
                            failedItems.add(middlewarePerson.getPerCode() + " (EGS平台返回错误)");
                            logger.warn("同步人员失败: {} ({}) - addPerson返回false", middlewarePerson.getAccName(), middlewarePerson.getPerCode());
                            return false;
                        }
                        
                    } catch (Exception e) {
                        failCount.incrementAndGet();
                        String errorMsg = middlewarePerson.getPerCode() + " (" + e.getMessage() + ")";
                        failedItems.add(errorMsg);
                        logger.error("同步人员失败: {} ({})", middlewarePerson.getAccName(), middlewarePerson.getPerCode(), e);
                        return false;
                    }
                });
                futures.add(future);
            }

            // 等待所有任务完成或超时
            int completedTasks = 0;
            for (Future<Boolean> future : futures) {
                try {
                    future.get(TASK_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                    completedTasks++;
                } catch (TimeoutException e) {
                    logger.warn("同步任务超时");
                    future.cancel(true);
                } catch (Exception e) {
                    logger.error("同步任务执行异常", e);
                }
            }
            
            // 关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }

            result.put("success", true);
            result.put("message", String.format("批量并发同步完成：成功 %d 条，失败 %d 条", 
                                               successCount.get(), failCount.get()));
            result.put("successCount", successCount.get());
            result.put("failCount", failCount.get());
            result.put("failedItems", failedItems);
            result.put("completedTasks", completedTasks);
            
            logger.info("批量并发同步操作完成：成功{}条，失败{}条，完成任务{}个", 
                       successCount.get(), failCount.get(), completedTasks);
            
        } catch (Exception e) {
            logger.error("批量同步操作异常", e);
            result.put("success", false);
            result.put("message", "批量同步失败：" + e.getMessage());
            result.put("successCount", 0);
            result.put("failCount", 0);
        }
        
        return result;
    }

    /**
     * 获取同步进度统计
     */
    public Map<String, Object> getSyncProgress() {
        Map<String, Object> progress = new HashMap<String, Object>();
        try {
            // 统计中间库总数
            Long middlewareTotal = middlewarePersonRepository.countActivePersons();
            
            // 统计已同步数量（通过人员编码匹配）
            List<MiddlewarePersonInfo> middlewarePersons = middlewarePersonRepository.findActivePersons();
            long syncedCount = 0;
            for (MiddlewarePersonInfo middlewarePerson : middlewarePersons) {
                Optional<PersonInfo> existingPerson = personRepository.findByPersonCode(middlewarePerson.getPerCode());
                if (existingPerson.isPresent() && existingPerson.get().getSyncFlag() == 1) {
                    syncedCount++;
                }
            }
            
            progress.put("total", middlewareTotal != null ? middlewareTotal : 0L);
            progress.put("synced", syncedCount);
            progress.put("unsynced", (middlewareTotal != null ? middlewareTotal : 0L) - syncedCount);
            progress.put("syncRate", middlewareTotal != null && middlewareTotal > 0 
                ? Math.round((double) syncedCount / middlewareTotal * 100) : 0);
            
        } catch (Exception e) {
            logger.error("获取同步进度失败", e);
            progress.put("total", 0L);
            progress.put("synced", 0L);
            progress.put("unsynced", 0L);
            progress.put("syncRate", 0);
        }
        
        return progress;
    }
} 