# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Maven Commands
- `mvn clean compile` - Compile the project
- `mvn clean package` - Build JAR file with external configs copied to target/
- `mvn spring-boot:run` - Run the application in development mode
- `mvn test` - Run tests (if any exist)

### Frontend Development (dashboard-frontend/)
- `npm install` - Install dependencies
- `npm run dev` - Start development server (Vite)
- `npm run build` - Build for production
- `npm run serve` - Preview production build on port 3000

### Running the Application
- Main class: `fastgatedemo.demo.StartMain`
- Development environment (port 8080): `java -Dspring.profiles.active=dev -jar app.jar`
- Production environment (port 8099): `java -Dspring.profiles.active=prod -jar app.jar`
- Access: http://localhost:8080 (dev) or http://localhost:8099 (prod)

## Architecture Overview

This project is a **dual-component system**:
1. **Backend**: Spring Boot 2.0.5 middleware application (Java 8, <PERSON>ven)
2. **Frontend**: Vue 3 dashboard application (Vite, Element Plus)

### Multi-Database System
This is a Spring Boot application serving as a middleware between multiple systems:
- **Primary Database (master)**: PostgreSQL transfer database - stores intermediate data
- **Secondary Database (fastgate)**: PostgreSQL fastgate database - stores access control data
- **Dynamic Data Source**: Uses `@DS` annotation to switch between databases

### Core Components

#### Data Flow Architecture
1. **Source Systems**: Student management system (学工系统) and ID card system (一卡通系统)
2. **Middleware Database**: Stores and processes data from source systems
3. **Target System**: EGS platform (人脸识别门禁系统)

#### Key Domain Models
- **PersonInfo**: Student/staff information from student management system
- **FacePhoto**: Face photos from ID card system  
- **DormitoryInfo**: Dormitory information mapping
- **PersonDormitoryRelation**: Person-dormitory associations
- **FastGate Models**: Access control records in separate database

#### Frontend Components (dashboard-frontend/)
- **Vue 3 + Vite**: Modern frontend framework with fast development server
- **Element Plus**: UI component library for consistent interface
- **ECharts**: Data visualization for dormitory statistics dashboard
- **SCSS**: Styling with variables and global styles
- **Axios**: HTTP client for API communication with backend

#### Service Layer Structure
- **Data Synchronization**: PersonService, DormitoryService handle data sync
- **Mock Data**: MockDataService for testing with configurable mock data
- **Import/Export**: ExcelImportService for bulk data operations
- **Monitoring**: ScheduledTaskManagementService for background tasks

### Configuration Management

#### Multi-Profile Configuration
- `application.yml` - Main configuration with common settings
- `application-dev.yml` - Development environment configuration
- `application-prod.yml` - Production environment configuration
- `application-mock.yml` - Mock data configuration (legacy)
- `application-external.yml` - External deployment overrides (optional, not in jar)

#### Environment Usage
- **Development**: `java -Dspring.profiles.active=dev -jar app.jar`
- **Production**: `java -Dspring.profiles.active=prod -jar app.jar`
- **External Config**: Place `application-external.yml` next to JAR for additional overrides

#### Key Configuration Properties
- `fastgate.*` - FastGate platform connection settings
- `spring.datasource.dynamic.*` - Multi-database configuration
- `app.mock.enabled` - Toggle mock data generation
- `face.photo.sync.fix.*` - Face photo sync repair settings

#### Development Stack
- **Java 8** with Spring Boot 2.0.5
- **PostgreSQL** databases (master/fastgate)
- **Maven** for dependency management and build
- **Thymeleaf** for server-side templates
- **Log4j2** for logging (Spring Boot default logging excluded)
- **Dynamic DataSource** (Baomidou) for multi-database support
- **Apache POI** for Excel file processing

### Database Schema
Tables are created via SQL scripts in `create_tables_postgresql.sql`. Key tables:
- `person_info` - Consolidated person records
- `face_photo` - Face photo data with Base64 storage
- `dormitory_info` - Dormitory structure
- `person_dormitory_relation` - Person-dormitory mappings
- FastGate schema in separate database

## Development Guidelines

### Data Source Usage
- Use `@DS("master")` for transfer database operations
- Use `@DS("fastgate")` for fastgate database operations
- Default is "master" if no annotation specified

### Mock Data System
- Enable with `app.mock.enabled=true`
- Configurable via `MockConfigProperties`
- Generates test data for development/testing

### External Configuration
- Place `application-external.yml` next to JAR for deployment
- Maven copies configs to `target/config/` during build
- External config automatically overrides internal settings

#### Deployment Structure
After `mvn clean package`, the deployment structure is:
```
target/
├── demo-0.0.1-SNAPSHOT.jar          # Main application JAR
├── application-external.yml          # External configuration (next to JAR)
└── config/                           # Config directory
    ├── application.yml               # Internal configs
    ├── application-dev.yml
    ├── application-prod.yml
    ├── application-mock.yml
    ├── application-external.yml
    └── log4j2.xml
```

### Image Handling
- Face photos stored as Base64 in database
- Photo paths configured via `fastgate.person.picPath`
- Image utilities in `ImageUtils` class

### Scheduled Tasks
- Use `ScheduledTaskManagementService` for background jobs
- Task status tracking via `ScheduledTaskInfo` model
- Configurable execution intervals

## Common Development Patterns

### Repository Pattern
- JPA repositories extend standard Spring Data interfaces
- Separate repositories for each database using `@DS` annotation
- Custom query methods follow Spring Data naming conventions

### Service Layer
- Services handle business logic and data transformation
- Use `PersonDataConverter` for data mapping between systems
- Error handling through custom exceptions

### Controller Layer
- REST controllers for API endpoints
- Thymeleaf controllers for web UI
- Separate controllers for different functional areas

### Testing
- Mock data system provides consistent test data
- Use profiles to switch between real and mock data
- Database operations can be tested against both data sources