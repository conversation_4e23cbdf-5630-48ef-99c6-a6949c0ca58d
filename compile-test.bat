@echo off
echo 正在测试WebSocket相关类的编译...

echo.
echo 1. 检查Maven依赖...
mvn dependency:resolve -q

echo.
echo 2. 编译WebSocket相关类...
mvn compile -Dcompiler.includes="**/websocket/**,**/config/WebSocketConfig.java" -q

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ WebSocket类编译成功！
    echo.
    echo 3. 编译整个项目...
    mvn compile -q
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ 整个项目编译成功！
    ) else (
        echo ❌ 项目编译失败，请检查其他类的错误
    )
) else (
    echo ❌ WebSocket类编译失败，请检查错误信息
)

echo.
echo 编译测试完成。
pause
