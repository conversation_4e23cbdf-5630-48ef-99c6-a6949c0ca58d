-- 创建异步写入队列表
-- 使用主数据源存储待写入FastGate数据库的通行记录
-- 确保在高并发场景下数据不丢失

CREATE TABLE IF NOT EXISTS access_record_queue (
    id BIGSERIAL PRIMARY KEY,
    person_code VA<PERSON>HAR(50),
    person_name VA<PERSON><PERSON><PERSON>(100),
    device_code VA<PERSON><PERSON><PERSON>(50),
    device_name VA<PERSON>HAR(100),
    place_code VA<PERSON><PERSON>R(50),
    place_name <PERSON><PERSON><PERSON><PERSON>(100),
    area_code VA<PERSON><PERSON>R(50),
    area_name VARCHAR(100),
    inorout INTEGER,
    pass_time TIMESTAMP,
    record_date VARCHAR(20),
    record_time VARCHAR(20),
    match_confidence INTEGER,
    device_ip VARCHAR(50),
    temperature NUMERIC(5,2),
    process_status INTEGER DEFAULT 0 CHECK (process_status IN (0, 1, 2)), -- 0:待处理, 1:已处理, 2:处理失败
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    process_time TIMESTAMP,
    retry_count INTEGER DEFAULT 0,
    error_message VARCHAR(500)
);

-- 创建索引提升查询性能
CREATE INDEX IF NOT EXISTS idx_access_record_queue_process_status ON access_record_queue(process_status);
CREATE INDEX IF NOT EXISTS idx_access_record_queue_create_time ON access_record_queue(create_time);
CREATE INDEX IF NOT EXISTS idx_access_record_queue_process_time ON access_record_queue(process_time);
CREATE INDEX IF NOT EXISTS idx_access_record_queue_person_code ON access_record_queue(person_code);
CREATE INDEX IF NOT EXISTS idx_access_record_queue_retry ON access_record_queue(process_status, retry_count, process_time);

-- 添加表注释
COMMENT ON TABLE access_record_queue IS '异步写入队列表，用于缓冲待写入FastGate数据库的通行记录';
COMMENT ON COLUMN access_record_queue.process_status IS '处理状态：0=待处理，1=已处理，2=处理失败';
COMMENT ON COLUMN access_record_queue.retry_count IS '重试次数';
COMMENT ON COLUMN access_record_queue.error_message IS '处理失败时的错误信息';