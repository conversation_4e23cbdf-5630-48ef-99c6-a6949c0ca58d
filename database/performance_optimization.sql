-- 统计查询性能优化索引建议
-- 用于优化 DormitoryStatusService 中的统计查询性能

-- 1. 为 tbl_person 表添加状态索引
-- 优化查询：SELECT COUNT(p) FROM TblPerson p WHERE p.status = 1
CREATE INDEX IF NOT EXISTS idx_tbl_person_status ON tbl_person(status);

-- 2. 为 tbl_access_control_record 表添加复合索引
-- 优化查询中的 person_code 和 pass_time 排序
CREATE INDEX IF NOT EXISTS idx_tbl_access_control_record_person_time 
ON tbl_access_control_record(person_code, pass_time DESC);

-- 3. 为 tbl_access_control_record 表添加过滤索引
-- 优化查询中的 person_code 过滤条件
CREATE INDEX IF NOT EXISTS idx_tbl_access_control_record_person_filter 
ON tbl_access_control_record(person_code) 
WHERE person_code IS NOT NULL AND person_code != '-';

-- 4. 为 tbl_access_control_record 表添加 inorout 索引
-- 优化按进出状态的统计查询
CREATE INDEX IF NOT EXISTS idx_tbl_access_control_record_inorout 
ON tbl_access_control_record(inorout);

-- 5. 为 tbl_person 表添加 code 索引（如果不存在）
-- 优化 JOIN 操作
CREATE INDEX IF NOT EXISTS idx_tbl_person_code ON tbl_person(code);

-- 查看索引使用情况的查询（PostgreSQL）
-- SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch 
-- FROM pg_stat_user_indexes 
-- WHERE tablename IN ('tbl_person', 'tbl_access_control_record')
-- ORDER BY tablename, indexname;

-- 查看表统计信息（PostgreSQL）
-- SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del, n_live_tup, n_dead_tup
-- FROM pg_stat_user_tables 
-- WHERE tablename IN ('tbl_person', 'tbl_access_control_record');

-- 分析表以更新统计信息（建议定期执行）
ANALYZE tbl_person;
ANALYZE tbl_access_control_record;
