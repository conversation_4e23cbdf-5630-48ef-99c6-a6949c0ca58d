-- 分表性能优化索引脚本
-- 为所有tbl_access_control_record分表创建性能优化索引
-- 用于提升分表查询的性能

-- 批量为所有分表创建索引
DO $$
DECLARE
    table_record RECORD;
    index_name_person_time TEXT;
    index_name_inorout TEXT;
    table_short_name TEXT;
BEGIN
    -- 遍历所有分表
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE (table_name LIKE 'tbl_access_control_record_2024%' 
               OR table_name LIKE 'tbl_access_control_record_2025%')
        AND table_schema = 'public'
        ORDER BY table_name
    LOOP
        -- 生成简短的表名用于索引命名
        table_short_name := replace(table_record.table_name, 'tbl_access_control_record_', '');
        
        -- 生成索引名称
        index_name_person_time := 'idx_' || table_short_name || '_person_time';
        index_name_inorout := 'idx_' || table_short_name || '_inorout';
        
        -- 创建核心索引：person_code + pass_time DESC（最重要的索引）
        -- 这个索引对DISTINCT ON (person_code) ORDER BY person_code, pass_time DESC查询至关重要
        BEGIN
            EXECUTE format('CREATE INDEX IF NOT EXISTS %s 
                           ON %s(person_code, pass_time DESC) 
                           WHERE person_code IS NOT NULL AND person_code != ''-''', 
                           index_name_person_time, 
                           table_record.table_name);
            RAISE NOTICE '已为表 % 创建person_code+pass_time索引: %', table_record.table_name, index_name_person_time;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING '为表 % 创建person_code+pass_time索引失败: %', table_record.table_name, SQLERRM;
        END;
        
        -- 创建inorout索引（用于统计查询中的CASE WHEN条件）
        BEGIN
            EXECUTE format('CREATE INDEX IF NOT EXISTS %s 
                           ON %s(inorout)', 
                           index_name_inorout, 
                           table_record.table_name);
            RAISE NOTICE '已为表 % 创建inorout索引: %', table_record.table_name, index_name_inorout;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING '为表 % 创建inorout索引失败: %', table_record.table_name, SQLERRM;
        END;
        
    END LOOP;
    
    RAISE NOTICE '分表索引创建完成！';
END $$;

-- 为主表也创建相同的索引（如果还没有的话）
CREATE INDEX IF NOT EXISTS idx_main_table_person_time 
ON tbl_access_control_record(person_code, pass_time DESC) 
WHERE person_code IS NOT NULL AND person_code != '-';

CREATE INDEX IF NOT EXISTS idx_main_table_inorout 
ON tbl_access_control_record(inorout);

-- 为tbl_person表创建状态索引（如果还没有的话）
CREATE INDEX IF NOT EXISTS idx_tbl_person_status ON tbl_person(status);
CREATE INDEX IF NOT EXISTS idx_tbl_person_code ON tbl_person(code);

-- 查看索引创建情况的查询
-- 可以手动执行以下查询来检查索引状态

-- 查看所有分表的索引情况
/*
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename LIKE 'tbl_access_control_record_%'
AND (indexname LIKE '%person_time%' OR indexname LIKE '%inorout%')
ORDER BY tablename, indexname;
*/

-- 查看索引使用统计
/*
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE tablename LIKE 'tbl_access_control_record_%'
AND (indexname LIKE '%person_time%' OR indexname LIKE '%inorout%')
ORDER BY tablename, indexname;
*/

-- 分析所有相关表以更新统计信息
ANALYZE tbl_person;
ANALYZE tbl_access_control_record;

-- 分析所有分表（可选，如果分表很多可能耗时较长）
DO $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE (table_name LIKE 'tbl_access_control_record_2024%' 
               OR table_name LIKE 'tbl_access_control_record_2025%')
        AND table_schema = 'public'
    LOOP
        EXECUTE format('ANALYZE %s', table_record.table_name);
        RAISE NOTICE '已分析表: %', table_record.table_name;
    END LOOP;
END $$;
