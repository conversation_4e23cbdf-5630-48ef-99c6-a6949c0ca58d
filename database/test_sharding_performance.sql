-- 分表查询性能测试脚本
-- 用于验证分表查询相比主表查询的性能提升

-- 1. 查看当前有哪些分表
SELECT 
    table_name,
    pg_size_pretty(pg_total_relation_size(quote_ident(table_name))) as table_size
FROM information_schema.tables 
WHERE table_name LIKE 'tbl_access_control_record%' 
AND table_schema = 'public'
ORDER BY pg_total_relation_size(quote_ident(table_name)) DESC;

-- 2. 查看主表记录数量
SELECT 
    'tbl_access_control_record' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT person_code) as unique_persons,
    MIN(pass_time) as earliest_record,
    MAX(pass_time) as latest_record
FROM tbl_access_control_record
WHERE person_code IS NOT NULL AND person_code != '-';

-- 3. 查看最近30天分表的记录数量（示例）
-- 注意：这个查询需要根据实际存在的分表进行调整
DO $$
DECLARE
    table_record RECORD;
    total_records BIGINT := 0;
    table_count INTEGER := 0;
BEGIN
    RAISE NOTICE '=== 分表记录统计 ===';
    
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE (table_name LIKE 'tbl_access_control_record_2024%' 
               OR table_name LIKE 'tbl_access_control_record_2025%')
        AND table_schema = 'public'
        ORDER BY table_name DESC
        LIMIT 30  -- 最近30个表
    LOOP
        DECLARE
            record_count BIGINT;
        BEGIN
            EXECUTE format('SELECT COUNT(*) FROM %s WHERE person_code IS NOT NULL AND person_code != ''-''', 
                          table_record.table_name) INTO record_count;
            
            IF record_count > 0 THEN
                RAISE NOTICE '表 %: % 条记录', table_record.table_name, record_count;
                total_records := total_records + record_count;
                table_count := table_count + 1;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING '查询表 % 失败: %', table_record.table_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE '=== 统计完成 ===';
    RAISE NOTICE '共检查 % 个分表', table_count;
    RAISE NOTICE '分表总记录数: %', total_records;
END $$;

-- 4. 测试主表查询性能（当前使用的查询）
\timing on
EXPLAIN (ANALYZE, BUFFERS) 
WITH person_status AS (
    SELECT p.code, 
           COALESCE(latest.inorout, 0) as last_inorout 
    FROM tbl_person p 
    LEFT JOIN (
        SELECT person_code, inorout, 
               ROW_NUMBER() OVER (PARTITION BY person_code ORDER BY pass_time DESC) as rn 
        FROM tbl_access_control_record 
        WHERE person_code IS NOT NULL AND person_code != '-' 
    ) latest ON p.code = latest.person_code AND latest.rn = 1 
    WHERE p.status = 1 
) 
SELECT 
    COUNT(*) as total_persons, 
    SUM(CASE WHEN last_inorout = 1 THEN 1 ELSE 0 END) as returned_persons, 
    SUM(CASE WHEN last_inorout = 2 THEN 1 ELSE 0 END) as not_returned_persons, 
    SUM(CASE WHEN last_inorout = 0 THEN 1 ELSE 0 END) as no_record_persons 
FROM person_status;

-- 5. 测试分表查询性能（新的优化查询）
-- 注意：这个查询需要根据实际存在的分表进行调整
-- 这里提供一个示例，实际使用时需要动态生成

/*
EXPLAIN (ANALYZE, BUFFERS)
WITH latest_records AS (
    SELECT DISTINCT ON (person_code) person_code, inorout 
    FROM (
        SELECT person_code, inorout, pass_time 
        FROM tbl_access_control_record_2024_12_31
        WHERE person_code IS NOT NULL AND person_code != '-'
        UNION ALL
        SELECT person_code, inorout, pass_time 
        FROM tbl_access_control_record_2025_01_01
        WHERE person_code IS NOT NULL AND person_code != '-'
        -- 添加更多分表...
    ) combined 
    ORDER BY person_code, pass_time DESC
) 
SELECT 
    COUNT(p.code) as total_persons, 
    COUNT(CASE WHEN lr.inorout = 1 THEN 1 END) as returned_persons, 
    COUNT(CASE WHEN lr.inorout = 2 THEN 1 END) as not_returned_persons, 
    COUNT(CASE WHEN lr.inorout IS NULL THEN 1 END) as no_record_persons 
FROM tbl_person p 
LEFT JOIN latest_records lr ON p.code = lr.person_code 
WHERE p.status = 1;
*/

-- 6. 查看索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE tablename LIKE 'tbl_access_control_record%'
AND idx_scan > 0
ORDER BY tablename, idx_scan DESC;

-- 7. 查看表的统计信息
SELECT 
    schemaname,
    relname as tablename,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    last_vacuum,
    last_analyze
FROM pg_stat_user_tables 
WHERE relname LIKE 'tbl_access_control_record%'
OR relname = 'tbl_person'
ORDER BY n_live_tup DESC;

\timing off

-- 使用说明：
-- 1. 首先执行索引创建脚本：database/sharding_table_optimization.sql
-- 2. 重启应用以加载新的分表查询逻辑
-- 3. 调用大屏统计接口，观察日志中的性能数据
-- 4. 执行此脚本查看数据库层面的性能指标
