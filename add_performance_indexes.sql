-- 性能优化索引添加脚本
-- 用于优化 /api/dashboard/realtime 接口性能
-- 执行前请确保连接到正确的数据库

-- ============================================
-- 1. person_info 表索引（如果不存在）
-- ============================================

-- 检查并创建 person_code 索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'person_info' AND indexname = 'idx_person_info_person_code') THEN
        CREATE INDEX idx_person_info_person_code ON person_info(person_code);
        RAISE NOTICE '已创建索引: idx_person_info_person_code';
    ELSE
        RAISE NOTICE '索引已存在: idx_person_info_person_code';
    END IF;
END
$$;

-- 检查并创建 status 索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'person_info' AND indexname = 'idx_person_info_status') THEN
        CREATE INDEX idx_person_info_status ON person_info(status);
        RAISE NOTICE '已创建索引: idx_person_info_status';
    ELSE
        RAISE NOTICE '索引已存在: idx_person_info_status';
    END IF;
END
$$;

-- ============================================
-- 2. person_dormitory_relation 表索引优化
-- ============================================

-- 检查并创建复合索引（最重要的优化）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'person_dormitory_relation' AND indexname = 'idx_relation_status_person_code') THEN
        CREATE INDEX idx_relation_status_person_code ON person_dormitory_relation(status, person_code);
        RAISE NOTICE '已创建复合索引: idx_relation_status_person_code （这是最重要的性能优化索引）';
    ELSE
        RAISE NOTICE '复合索引已存在: idx_relation_status_person_code';
    END IF;
END
$$;

-- 检查基础索引是否存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'person_dormitory_relation' AND indexname = 'idx_relation_person_code') THEN
        CREATE INDEX idx_relation_person_code ON person_dormitory_relation(person_code);
        RAISE NOTICE '已创建索引: idx_relation_person_code';
    ELSE
        RAISE NOTICE '索引已存在: idx_relation_person_code';
    END IF;
END
$$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'person_dormitory_relation' AND indexname = 'idx_relation_status') THEN
        CREATE INDEX idx_relation_status ON person_dormitory_relation(status);
        RAISE NOTICE '已创建索引: idx_relation_status';
    ELSE
        RAISE NOTICE '索引已存在: idx_relation_status';
    END IF;
END
$$;

-- ============================================
-- 3. 验证索引创建结果
-- ============================================

-- 显示相关表的所有索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('person_info', 'person_dormitory_relation')
ORDER BY tablename, indexname;

-- 显示表大小信息
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename IN ('person_info', 'person_dormitory_relation');

RAISE NOTICE '====================================';
RAISE NOTICE '性能优化索引创建完成！';
RAISE NOTICE '核心优化：idx_relation_status_person_code 复合索引';
RAISE NOTICE '预期效果：findAssignedDormitoryStudentCodes() 查询性能显著提升';
RAISE NOTICE '====================================';