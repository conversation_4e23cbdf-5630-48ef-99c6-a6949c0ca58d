# 大屏实时记录WebSocket优化说明

## 🎯 优化目标

针对大屏下方的实时进出记录功能进行优化，解决以下问题：

1. **默认不查询记录**：页面加载时不查询进出记录，减少初始加载时间
2. **WebSocket实时推送**：当有新的进出记录时，通过WebSocket推送到前端
3. **页面布局优化**：增加实时进出记录表格的占比，显示更多记录

## 📋 优化内容

### 1. 前端页面布局优化

#### 调整网格布局比例
```scss
// 优化前：统计面板60%，记录面板40%
grid-template-rows: 60% 40%;

// 优化后：统计面板45%，记录面板55%
grid-template-rows: 45% 55%;
```

#### 增加记录显示数量
```javascript
// 最大显示记录数从默认增加到50条
const maxRecordsCount = 50
```

### 2. WebSocket实时推送架构

#### 后端WebSocket组件
```
DashboardWebSocketHandler     - WebSocket消息处理器
WebSocketConfig              - WebSocket配置类
DashboardWebSocketService    - WebSocket推送服务
WebSocketTestController      - WebSocket测试控制器
```

#### WebSocket端点
```
/ws/dashboard/records        - 主要WebSocket端点（支持SockJS）
/ws/dashboard/records/native - 原生WebSocket端点
```

### 3. 前端WebSocket集成

#### 连接管理
```javascript
// WebSocket连接状态
const websocketStatus = ref('disconnected') // disconnected, connecting, connected, error

// 自动连接和重连
const connectWebSocket = () => {
  websocket = new WebSocket(websocketUrl.value)
  // 自动重连机制（5秒后重试）
}
```

#### 消息处理
```javascript
const handleWebSocketMessage = (data) => {
  if (data.type === 'new_record' && data.record) {
    // 收到新的进出记录
    addNewRecord(data.record)
  } else if (data.type === 'batch_records' && data.records) {
    // 收到批量记录（初始化时）
    setBatchRecords(data.records)
  }
}
```

## 🚀 技术实现

### 1. 后端WebSocket实现

#### WebSocket处理器
```java
@Component
public class DashboardWebSocketHandler implements WebSocketHandler {
    
    // 存储所有连接的WebSocket会话
    private static final CopyOnWriteArraySet<WebSocketSession> sessions = new CopyOnWriteArraySet<>();
    
    // 推送新的进出记录
    public void pushNewRecord(DormitoryStatusDTO record) {
        Map<String, Object> message = Map.of(
            "type", "new_record",
            "record", record,
            "timestamp", System.currentTimeMillis()
        );
        broadcastMessage(message);
    }
}
```

#### WebSocket配置
```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(dashboardWebSocketHandler, "/ws/dashboard/records")
                .setAllowedOrigins("*")
                .withSockJS();
    }
}
```

### 2. 前端WebSocket实现

#### 连接初始化
```javascript
const initWebSocket = () => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const host = window.location.host
  websocketUrl.value = `${protocol}//${host}/ws/dashboard/records`
  connectWebSocket()
}
```

#### 记录处理
```javascript
const addNewRecord = (newRecord) => {
  // 添加到记录列表开头
  allRecords.value.unshift(newRecord)
  
  // 限制记录数量
  if (allRecords.value.length > maxRecordsCount) {
    allRecords.value = allRecords.value.slice(0, maxRecordsCount)
  }
  
  // 显示通知
  ElMessage.success(`新进出记录: ${newRecord.personName} ${newRecord.lastInOrOutDesc}`)
}
```

### 3. 状态监控

#### 前端状态显示
```vue
<template>
  <div class="websocket-status">
    <span class="status-indicator" :class="websocketStatus"></span>
    实时推送: {{ getWebSocketStatusText() }}
  </div>
</template>
```

#### 状态样式
```scss
.status-indicator {
  &.connected {
    background-color: $success-color;
    box-shadow: 0 0 8px $success-color;
  }
  
  &.connecting {
    background-color: $warning-color;
    animation: pulse 1.5s infinite;
  }
  
  &.error {
    background-color: $danger-color;
    animation: blink 1s infinite;
  }
}
```

## 📊 优化效果

### 1. 性能提升
- ✅ **初始加载优化**：页面加载时不查询记录，加载速度更快
- ✅ **实时性提升**：WebSocket推送，记录实时显示，无需轮询
- ✅ **网络优化**：减少HTTP请求，降低服务器压力

### 2. 用户体验提升
- ✅ **显示更多记录**：表格占比从40%增加到55%
- ✅ **实时通知**：新记录推送时显示消息提示
- ✅ **连接状态**：实时显示WebSocket连接状态
- ✅ **自动重连**：连接断开时自动重连

### 3. 功能完善
- ✅ **心跳机制**：定时发送心跳，保持连接活跃
- ✅ **错误处理**：完善的错误处理和重连机制
- ✅ **测试接口**：提供测试接口，便于调试

## 🔧 使用方式

### 1. 启动服务
```bash
# 启动后端服务
mvn spring-boot:run

# 启动前端服务
cd dashboard-frontend
npm run dev
```

### 2. 访问页面
```
http://localhost:3000
```

### 3. 测试WebSocket推送
```bash
# 推送单条测试记录
curl http://localhost:8080/api/websocket/test/push

# 批量推送测试记录
curl -X POST "http://localhost:8080/api/websocket/test/push/batch?count=5"

# 查看WebSocket连接状态
curl http://localhost:8080/api/websocket/test/status
```

## 📁 新增/修改的文件

### 1. 后端文件
- `src/main/java/fastgatedemo/demo/websocket/DashboardWebSocketHandler.java` - WebSocket处理器
- `src/main/java/fastgatedemo/demo/config/WebSocketConfig.java` - WebSocket配置
- `src/main/java/fastgatedemo/demo/service/DashboardWebSocketService.java` - WebSocket推送服务
- `src/main/java/fastgatedemo/demo/controller/WebSocketTestController.java` - WebSocket测试控制器

### 2. 前端文件
- `dashboard-frontend/src/views/DashboardView.vue` - 优化页面布局和WebSocket集成

### 3. 文档文件
- `docs/大屏实时记录WebSocket优化说明.md` - 详细优化说明

## 🔄 WebSocket消息格式

### 1. 新记录推送
```json
{
  "type": "new_record",
  "record": {
    "personCode": "001",
    "personName": "张三",
    "lastInOrOut": 1,
    "lastInOrOutDesc": "进入寝室",
    "lastPassTimeStr": "14:30:25",
    "buildingName": "1号楼",
    "dormitoryName": "1号楼101",
    "lastDeviceName": "门禁设备1"
  },
  "timestamp": 1706598625000
}
```

### 2. 批量记录推送
```json
{
  "type": "batch_records",
  "records": [...],
  "count": 10,
  "timestamp": 1706598625000
}
```

### 3. 心跳消息
```json
{
  "type": "ping",
  "timestamp": 1706598625000
}
```

## 🎯 总结

通过这次优化，我们实现了：

1. **页面布局优化**：实时记录表格占比增加到55%，显示更多记录
2. **性能优化**：默认不查询记录，减少初始加载时间
3. **实时推送**：WebSocket实时推送新记录，用户体验更好
4. **状态监控**：完善的连接状态显示和自动重连机制
5. **测试完善**：提供完整的测试接口，便于调试和验证

这个优化方案完全满足了您的需求，提供了更好的用户体验和更高的实时性！🎉
